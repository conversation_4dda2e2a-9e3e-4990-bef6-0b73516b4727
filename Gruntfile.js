module.exports = function(grunt) {

	// nacitani tasku
	require('jit-grunt')(grunt);

	// pocitani rychlosti
	require('time-grunt')(grunt);

	// konfigurace projektu
	grunt.initConfig({

		// sledovani zmen v souborech
		watch: {
			grunt: {
				files: ['Gruntfile.js']
			},
			javascript: {
				files: ['www/js/*.js', '!www/js/scripts.js'],
				tasks: ['concat']
			},
			less: {
				files: ['www/css/**/*.less'],
				tasks: ['less']
			},
			iconfont: {
				files: ['www/img/icons/*.svg'],
				tasks: ['webfont']
			}
		},

		// spojovani souboru
		concat: {
			dist: {
				src: ['www/js/analytics.js', 'www/js/jquery.magnific-popup.js', 'www/js/netteForms.js', 'www/js/myautocomplete.js', 'www/js/owl-carousel/owl.carousel.js', 'www/js/main.js'],
				dest: 'www/js/scripts.js'
			}
		},

		// minifikace JS
		uglify: {
			my_target: {
				files: {
					'www/js/scripts.js': ['www/js/scripts.js']
				}
			}
		},

		// LESS
		less: {
			development: {
				options: {
					paths: ["css"]
				},
				files: {
					'www/css/web2/styles.css': 'www/css/web2/styles.less',
					'www/css/web3/styles.css': 'www/css/web3/styles.less'
				}
			}
		},

		// autoprefixer
		autoprefixer: {
			dist: {
				options: {
				  browsers: ['last 6 versions', 'ie 7', 'ie 8', 'ie 9', 'Firefox < 20']
				},
				files: {
					'www/css/web2/styles.css': 'www/css/web2/styles.css',
					'www/css/web3/styles.css': 'www/css/web3/styles.css'
				}
			}
		},

		// legaCSSy
	/*	legacssy: {
			dist: {
				files: {
					'www/css/web2/styles-legacy.css': 'www/css/web2/styles.css',
					'www/css/web3/styles-legacy.css': 'www/css/web3/styles.css'
				}
			}
		},*/

		// minifikace CSS
		cssmin: {
			options: {
				shorthandCompacting: true
			},
			dist: {
				files: {
					'www/css/web2/styles.css': 'www/css/web2/styles.css',
					'www/css/web3/styles.css': 'www/css/web3/styles.css',
					'www/css/web2/styles-legacy.css': 'www/css/web2/styles-legacy.css',
					'www/css/web3/styles-legacy.css': 'www/css/web3/styles-legacy.css'
				}
			}
		},

		// optimalizace obrazku
		imagemin: {
			dynamic: {
				files: [{
					expand: true,
					cwd: 'www/img/',
					src: ['**/*.{png,jpg,gif}'],
					dest: 'www/img/'
				}]
			}
		},

		// browser sync
		browserSync: {
			default_options: {
				bsFiles: {
					src: [
						"www/css/**/*.css",
						"www/js/*.js",
						"**/*.html",
						"**/*.htm",
						"**/*.php",
						"**/*.phtml"
					]
				},
				options: {
					watchTask: true,
					proxy: "shopcom.test"
				}
			}
		},

		// generovani icon-fontu
		webfont: {
			icons: {
				src: 'www/img/icons/*.svg',
				dest: 'www/fonts',
				options: {
					engine: 'node',
					stylesheet: 'less',
					hashes: true,
					relativeFontPath: '../../fonts',
					font: 'mainicons',
					templateOptions: {
						baseClass: 'icon',
						classPrefix: 'icon_',
						mixinPrefix: 'icon-'
					}
				}
			}
		}

	});

	// tasky
		// vytvoreni JS
		grunt.registerTask('makejs', ['concat', 'uglify']);
		// vytvoreni CSS
		grunt.registerTask('makecss', ['less', 'autoprefixer', 'cssmin']); // 'legacssy',
		// deploy
		grunt.registerTask('deploy', ['makejs', 'makecss']);
		// defaultni task
		grunt.registerTask('default', ['browserSync', 'watch']);

};
