<?php
namespace Deployer;

require 'recipe/composer.php';

// Config


set('user', function(){
    return getenv('GITLAB_USER_NAME');
});

set('repository', function() {
    return getenv('CI_REPOSITORY_URL');
});

set('shared_files', ['config/local.neon', '.env.local']);
set('shared_dirs', ['data','log','www/pic','www/files','www/banners','www/catalogs','.ssh']);
set('writable_dirs', ['data','log', 'temp','temp_web3','temp_web2','www/banners','www/catalogs']);
set('clear_paths', ['temp/cache', 'temp/proxies']);
set('copy_dirs', ['vendor']);
set('keep_releases', 2);


host(getenv('DEPLOYER_HOST_PRODUCTION'))
    ->set('become', 'www-data')
    ->set('remote_user','deploy')
    ->set('writable_mode', 'chmod')

    ->set ('ssh_multiplexing', false)
    ->set('git_tty', false)



    ->set('deploy_path', '/www/hosting/sccom.cz');



task('deploy:db',  function () {
    run('cd  {{release_or_current_path}} && {{bin/php}} migrations/run.php sql');
});

task('php:reload',  function () {
    run('sudo /usr/sbin/service php8.0-fpm reload');
});


set('php_fpm_version', '8.0');

// Hosts

task('deploy', [
    'deploy:prepare',
    'deploy:copy_dirs',
    'deploy:vendors',
    //'deploy:clear_paths',
    'deploy:db',
    'deploy:publish',
    'php:reload'
]);
// Hooks

after('deploy:failed', 'deploy:unlock');
