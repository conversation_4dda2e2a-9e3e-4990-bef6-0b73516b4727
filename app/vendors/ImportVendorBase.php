<?php
namespace App\vendors;

use App\Models\EntityManager;
use App\Orm\Orm;
use App\Orm\Vendor;
use App\Orm\VendorUpdateLog;
use Dibi\Exception;
use <PERSON>bi\Result;
use <PERSON>bi\Row;
use Nette\Utils\FileSystem;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use SimpleXMLElement;

abstract class ImportVendorBase {

  public Vendor $vendor;

  /** @var array  */
  protected $config = array();

  /** @var array  */
  protected $serverConfig = array();

  /** @var string  */
  protected $serverId = '';

  /** @var string  */
  protected $type = '';

  /** @var string */
  protected $providerType = '';

  /** @var ImportVendorI6 */
  protected $impVenI6;

  /** @var string  */
  protected $downloadPath = '';

  /** @var string  */
  public $log = array();

  /** @var string  */
  public $data = array();

  /** @var string  */
  protected $blackList;

  /** @var array číselník DPH SAZBA V % => ID v eshopu */
  private $enumProVatId;

  /** @var EntityManager */
  protected $em;

  protected Orm $orm;

  protected VendorUpdateLog $updateLog;

  /** @var integer id přihlášeného správce */
  public $admId;

  public bool $forceUpdate = FALSE;

  const VENDOR_NOSRETI = 'nosreti';
  const VENDOR_ATC = 'atc';
  const VENDOR_FAST = 'fast';
  const VENDOR_BALCZ = 'bal';
  const VENDOR_WILLI = 'willi';
  const VENDOR_DEXON = 'dexon';
  const VENDOR_HOMELUX = 'homelux';
  const VENDOR_CATTARA = 'cattara';
  const VENDOR_BAKR = 'bakr';
  const VENDOR_IRD = 'ird';
  const VENDOR_AUTOHAUS = 'autohaus';
  const VENDOR_NOVASERVIS = 'novaservis';
  const VENDOR_BSACOUSTIC = 'bsacoustic';
  const VENDOR_PARTNERTELE = 'partnertel';
  const VENDOR_NICEBOY = 'niceboy';
  const VENDOR_SPOKEY = 'spokey';
  const VENDOR_EASYOFFICE = 'easyoffice';
  const VENDOR_PENTA = 'penta';
  const VENDOR_MADALBAL = 'madalbal';
  const VENDOR_EAST = 'east';
  const VENDOR_TSB = 'tsb';
  const VENDOR_MATYSKA = 'matyska';
  const VENDOR_MUJPERFEKT = 'mujperfekt';
  const VENDOR_PLACEK = 'placek';

  const VENDOR_HSERVICE = 'hservice';

  //typy datových providerů
  const PROVIDER_TYPE_I6 = 'i6';

  //indexy sloupců
  const FAS_CODE = 0;
  const FAS_EAN = 1;
  const FAS_NAME = 2;
  const FAS_PIC = 3;
  const FAS_DESC = 4;
  const FAS_PRICE = 5;
  const FAS_PRICECOM = 6;
  const FAS_PRICEAMOC = 7;

  //indexy sloupců
  const WIN_NAME = 0;
  const WIN_CATLOGS = 1;
  const WIN_CODE = 2;
  const WIN_EAN = 3;
  const WIN_DESCS = 4;
  const WIN_DESC = 5;
  const WIN_IMAGES = 10;
  const WIN_PRICE = 11;
  const WIN_PRICECOM = 12;

  /** @var array cache výrobci */
  protected $manufacturers;

  /**
   * @return array
   * @throws Exception
   */
  protected function getManufacturers() {
    if (empty($this->manufacturers)) {
      $this->manufacturers = $this->database->query('SELECT manid, manname FROM ' .TABLENAME_MANUFACTURERS)->fetchPairs('manname', 'manid');
    }
    return $this->manufacturers;
  }

  /**
   * vrací číselník DPH
   *
   * @return array
   */
  protected function getEnumProVatId() {
    if (empty($this->enumProVatId)) {
      for ($i = 0; $i <= 3; $i++) {
        $v = (int)$this->serverConfig["VATTYPE_" . $i];
        $this->enumProVatId[$v] = $i;
      }
    }
    return $this->enumProVatId;
  }

  /**
   * z ceny textově udělá číslo
   *
   * @param $text
   * @return float
   */
  protected function getPriceFromText($text) {
    $text = str_replace(array('Kč', ' ', ' '), '', trim($text));
    return (double)$text;
  }

  /**
   * vrací ID výrobce, pokud neexistuje tak vytvořím
   *
   * @param string $manName
   * @return mixed
   * @throws Exception
   */
  protected function getManufacturer($manName) {

    if (empty($manName)) {
      return 1; //nevyplněn
    }

    $manRows = $this->getManufacturers();

    $manName = \Nette\Utils\Strings::capitalize(trim($manName));
    if (isset($manRows[$manName])) {
      //vrátím ID výrobce
      return $manRows[$manName];
    }

    return $this->setManufacturer($manName);
  }

  /**
   * založí nového výrobce, vrátí ID
   *
   * @param $manName
   * @return int|Result|null
   */
  protected function setManufacturer($manName) {
    //vlozim vyrobce
    $mans  = $this->em->getManufacturers();
    $manid = $mans->insert(array('manname' => $manName));
    if ($manid > 0) {
      $this->manufacturers[$manName] = $manid;
      return $manid;
    }
    return 0;
  }

  /**
   * zkontroluje data před aktualizací
   * fixace, sklady
   *
   * @param $data
   * @param $pro
   * @return array
   *
   */
  protected function checkDataBeforeUpdate($data, $pro): array {

    if ($this->vendor->storeOff) {
      //vynutím vypnutí skladové dostupnosti u dodavatele
      if (isset($data['proaccessdef'])) {
        $data['proaccessdef'] = 101;
      }
      if (isset($data['proaccess_web2'])) {
        $data['proaccess_web2'] = 101;
      }
      if (isset($data['proaccess_web3'])) {
        $data['proaccess_web3'] = 101;
      }
      if (isset($data['proqty_atc'])) {
        $data['proqty_atc'] = 0;
      };
    }

    //pokud skladem na druhém eshopu skladem eshop
    if ($pro->proaccess_web2 == 0 && isset($data['proaccess_web3']) && $data['proaccess_web3'] > 1) {
      $data['proaccess_web3'] = 1;
    }
    if ($pro->proaccess_web3 == 0 && isset($data['proaccess_web2']) && $data['proaccess_web2'] > 1) {
      $data['proaccess_web2'] = 1;
    }

    //pokud je nastavena dostupnost a změnila se nastavím větší preferenci pro MALL update
    if (isset($pro->proaccess_web2, $data['proaccess_web2']) && (int)$data['proaccess_web2'] !== (int)$pro->proaccess_web2) {
      $data["promalldateavu"] = NULL;
    }
    if (isset($pro->proaccess_web3, $data['proaccess_web3']) && (int)$data['proaccess_web3'] !== (int)$pro->proaccess_web3) {
      $data["promalldateavu"] = NULL;
    }

    //kontrola dostupnosti - pokud skladem nemůžu měnit dostupnost
    if ($pro->proaccess_web2 == 0) {
      unset($data['proaccess_web2']);
    }
    if ($pro->proaccess_web3 == 0) {
      unset($data['proaccess_web3']);
    }

    //pokud fixovaná cena neaktualizuji cenu A
    if ((int)$pro->propricefix_web2 === 1) {
      unset($data['propricecom_web2']);
      unset($data['propricea_web2']);
    }
    if ((int)$pro->propricefix_web3 === 1) {
      unset($data['propricecom_web3']);
      unset($data['propricea_web3']);
    }

    //pokud fixovaný název zboží tak neaktualizuji
    if ((int)$pro->pronamefix === 1) {
      unset($data['proname']);
      unset($data['procode2']);
    }

    if (isset($pro->prodescfix) && (int)$pro->prodescfix === 1) {
        unset($data['prodescs']);
        unset($data['prodesc']);
      }

    //pokud fixovaný výrobce tak neaktualizuji
    if ((int)$pro->promanidfix === 1) {
      unset($data['promanid']);
    }

    // aktualizuji jen pokud se změnila hodnota INT
    $fields = ["promanid", "proaccess_web2", "proaccess_web3", "proqty_atc", "prostatus"];
    foreach ($fields as $field) {
      if (isset($data[$field], $pro->$field)) {
        if ((int)$data[$field] === (int)$pro->$field) {
          unset($data[$field]);
        }
      }
    }

    $fields = ["propricea_web2", "propricea_web3", "prorecfee", "proweight"];
    foreach ($fields as $field) {
      if (isset($data[$field], $pro->$field)) {
        if ((double)$data[$field] === (double)$pro->$field) {
          unset($data[$field]);
        }
      }
    }

    //zjistím jestli se aktualizovala cena která má vliv na marži
    $fields = ["propricec_web2", "propricec_web3", "propriced_web2", "propriced_web3", "propricecom_web2", "propricecom_web3", "prousepriceeu", "price_eu"];
    foreach ($fields as $field) {
      if (isset($data[$field], $pro->$field)) {
        if ((double)$data[$field] === (double)$pro->$field) {
          unset($data[$field]);
        } else {
          $data['need_recalculate_price'] = 1;
        }
      }
    }

    //jen pokud se změní aktualizuji defaultní dostupnost
    if (isset($data['proaccessdef']) &&  (int)$pro->proaccessdef === (int)$data['proaccessdef']) {
      unset($data['proaccessdef']);
    }

    if (!empty($data['propricea_web2']) && !empty($pro['propricea_web2']) && (double)$data['propricea_web2'] !== (double)$pro['propricea_web2']) {
      $data['promalldateu'] = NULL;
    }

    $fields = ["protypid", "protypid2", "protypid3", "protypid4", "protypid5", "procode2", "procodeven", "proname", "prodescs", "prodesc"];

    foreach ($fields as $field) {
      if (isset($data[$field], $pro->$field)) {
        if ($data[$field] == $pro->$field) {
          unset($data[$field]);
        }
      }
    }

    if (isset($pro->prostatus) && (int)$pro->prostatus === 2) {
      //pokud je status=adminblok tak nechat jak je
      unset($data['prostatus']);
    }

    return $data;
  }

  /**
   * vygeneruje kód zboží z kódu dodavatele
   *
   * @param $code string kód dodavatele
   * @return string
   */
  protected function getProCode($code) {

    $code = (string)$code;

    $replace = array(",", '"', '/', " ");
    $remove = array("(", ")");

    if ($this->type == self::VENDOR_NOSRETI) {

      foreach ($replace as $char) {
        $code = str_replace($char, "_", $code);
      }

      foreach ($remove as $char) {
        $code = str_replace($char, "", $code);
      }

      return 'NOS_' . $code;
    }

    if ($this->type === self::VENDOR_BALCZ) {
      return 'PG_' . $code;
    }

    return $this->config["codePrefix"] . $this->getSafeCode($code, '_');
  }

  public function getProName($proName, $partNo, $product=NULL) {
    if ($this->type === self::VENDOR_CATTARA && !empty($partNo)) {
      $proName .= ", " . $partNo;
    }
    return $proName;
  }



  /**
   * vrací ID sazby DPH
   *
   * @param double $vat DPH v %
   * @return null|integer
   */
  protected function getVatId($vat) {
    if (!isset($this->VatProVatId)) {
      for ($i = 0; $i <= 3; $i++) {
        $v = (int)$this->serverConfig["VATTYPE_" . $i];
        $this->VatProVatId[$v] = $i;
      }
    }
    if (isset($this->VatProVatId[$vat])) {
      return (int)$this->VatProVatId[$vat];
    }
    return null;
  }

  /**
   * vrací DPH v %
   *
   * @param int $vatId ID DPH dle číselníku
   * @return null|integer
   */
  protected function getVat($vatId) {
    if (isset($this->serverConfig["VATTYPE_" . $vatId])) {
      return (int)$this->serverConfig["VATTYPE_" . $vatId];
    }
    return null;
  }

  /**
   * vrací cenu s DPH DPH
   *
   * @param double $priceNoVat cena bez DPH
   * @param double $vat DPH v %
   * @param int $decimals zaokrouhlit na des. míst
   * @return null|integer
   */
  protected function getPriceVat($priceNoVat, $vat, $decimals = 0) {
    return round($priceNoVat * (1 + ($vat / 100)), $decimals);
  }

  /**
   * @param $type
   * @param $config
   * @param EntityManager $em
   * @param $serverId
   * @param Orm $orm
   * @param null $admId
   * @return AtcVendor|AutohausVendor|BalVendor|BsacousticVendor|CattaraVendor|DexonVendor|EastVendor|FastVendor|HomeluxVendor|ImportVendor|ImportVendorI6|MatyskaVendor|NosretiVendor|PartnerteleVendor|TsbVendor|WilliVendor
   */
  public static function getImportVendorClassByType($type, $config, EntityManager $em, $serverId,Orm $orm, $admId=NULL) {
    if ($type === ImportVendorBase::VENDOR_BAKR || $type === ImportVendorBase::VENDOR_IRD || $type === ImportVendorBase::VENDOR_SPOKEY || $type === ImportVendorBase::VENDOR_EASYOFFICE || $type === ImportVendorBase::VENDOR_PENTA) {
      return new ImportVendorI6($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_CATTARA) {
      return new CattaraVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_BALCZ) {
      return new BalVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_AUTOHAUS) {
      return new AutohausVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_NOVASERVIS) {
      return new NovaservisVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_BSACOUSTIC) {
      return new BsacousticVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_PARTNERTELE) {
      return new PartnerteleVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_NICEBOY) {
      return new NiceboyVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_MADALBAL) {
      return new MadalbalVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_HOMELUX) {
      return new HomeluxVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_WILLI) {
      return new WilliVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_NOSRETI) {
      return new NosretiVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_ATC) {
      return new AtcVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_EAST) {
      return new EastVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_FAST) {
      return new FastVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_TSB) {
      return new TsbVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_DEXON) {
      return new DexonVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_MATYSKA) {
      return new MatyskaVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_MUJPERFEKT) {
      return new MujPerfektVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_PLACEK) {
      return new PlacekVendor($type, $config, $em, $serverId, $orm, $admId);
    } else if ($type === ImportVendorBase::VENDOR_HSERVICE) {
      return new HServiceVendor($type, $config, $em, $serverId, $orm, $admId);
    } else {
      return new ImportVendor($type, $config, $em, $serverId, $orm, $admId);
    }
  }

  /**
   * načte data o zboží potřebné pro aktualizaci položky
   *
   * @param $proCodeVen
   * @param string $tableName
   * @return Row
   * @throws Exception
   */
  public function loadProductForUpdate($proCodeVen, $tableName='') {
    if (empty($tableName)) {
      $tableName = TABLENAME_PRODUCTS;
    }
    return $this->database->fetch('
        SELECT proid, procode, procode2, procodeven, proname, prodescs, prodesc, 
        proaccess_web2, proaccess_web3, proaccessdef, protypid, protypid2, protypid3, protypid4, protypid5,
        propricecom_web2, propricea_web2, propricec_web2, propriced_web2, 
        propricecom_web3, propricea_web3, propricec_web3, propriced_web3, prorecfee, proweight, prousepriceeu, price_eu,
        propricea_web2 - propriced_web2 AS diff, 100 - (propriced_web2 / propricea_web2 * 100) AS diffpercent, 
        propricefix_web2, propricefix_web3, protypid, promanid, pronamefix, promanidfix, provatid, prostatus, proqty_atc
        ' . ($tableName === TABLENAME_PRODUCTS ? ", prodescfix, proodfix, propicfix, propricefixvalue_web2, propricefixvalue_web3" : "") . '
        FROM ' . $tableName . ' 
        WHERE procodeven=%s', $proCodeVen,  ($tableName === TABLENAME_PRODUCTS ? "AND proodfix!=1" : "") . " AND provenid=%s", $this->type, ($tableName === TABLENAME_IMPORT_PRODUCTS && $this->admId > 0 ? " AND proadmid=" . $this->admId : ""));
  }

  protected function downloadFeed($from = NULL, $to = NULL, $returnData = TRUE, $useCUrl=FALSE): bool|string {

    if ($this->type == 'matyska') {
      $useCUrl = TRUE;
    }

    if ($from === NULL) {
      $from = $this->config["feedUrl"];
    }

    if ($to === NULL) {
      if (!empty($this->updateLog->dataFile)) {
        $to = $this->updateLog->dataFile;
      } else {
        $to = $this->downloadPath . "/data.xml";
      }
    } else {
      $returnData = FALSE;
    }

    $ret = TRUE;

    if ($useCUrl) {

      $curl_handle=curl_init();
      curl_setopt($curl_handle, CURLOPT_URL, $from);
      curl_setopt($curl_handle, CURLOPT_CONNECTTIMEOUT, 2);
      curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($curl_handle, CURLOPT_SSL_VERIFYPEER, FALSE);
      $query = curl_exec($curl_handle);
      curl_close($curl_handle);

      if ($returnData) {
        return $query;
      } else {
        FileSystem::write($to, $query, NULL);
      }
    } else {
      $ret = copy($from, $to);

      if ($returnData) {
        return file_get_contents($to);
      }
    }

    return $ret;
    /*
    if ($to === NULL) {
      return $query;
    } else {
      FileSystem::write($to, $query, NULL);
      return $to;
    }
    */
  }


  public function saveFeed() {
    return $this->downloadFeed(NULL, NULL, FALSE);
  }

  /**
   * @param $pro
   * @param $srcUrl
   * @param $fileBaseName
   * @param int $index
   * @param bool $checkProImgFromVendor
   * @return void
   * @throws Exception
   */
  protected function checkProductImage($pro, $srcUrl, $fileBaseName, int $index=0, bool $checkProImgFromVendor = FALSE) {
    $updImg  = $this->em->getImport_Images_Update();

    $fileBaseName = $fileBaseName . ($index === 0 ? "" : "_" . $index);
    $fileName = $fileBaseName . ".jpg";

    $img = $this->database->fetch("SELECT * FROM " . TABLENAME_IMPORT_IMAGES_UPDATE . " WHERE imgproid=%i", $pro->proid, " AND imgindex=%i", $index);

    $values = array(
      'imgproid' => $pro->proid,
      'imgurl' => $srcUrl,
      'imgstatus' => 'mis',
      'imgindex' => $index,
      'imgdateu' => new \DateTime()
    );

    if ($img === NULL) {
      $updImg->insert($values);
    } else {
      //zjistím jestli obrázek existuje
      if (!file_exists(WWW_DIR . "/pic/web2/product/detail/" . $fileName)) {
        //obrázek neexistuje a měl by
        $updImg->update($img->imgid, $values);
      } else {
        if ($checkProImgFromVendor) {
          $this->checkProImgFromVendor($pro->proid, $srcUrl, $img, $index);
        }
      }
    }
  }

  /**
   * @throws Exception
   */
  public function checkProImgFromVendor($proid, $url, $img=NULL, $index=0) {
    $updImg = $this->em->getImport_Images_Update();

    //kontrola zda kod a nazev obrázku je stejný
    $pro = $this->database->fetch("SELECT procode, propicname, propicfix FROM " . TABLENAME_PRODUCTS . " WHERE proid=%i", $proid);
    if ($pro) {
      if (!empty($pro->propicname) && $pro->procode !== $pro->propicname && $this->getSafeCode($pro->procode) !== (string)$pro->propicname) {
        //název obrázku je jiný, neaktualizuji
        return TRUE;
      }

    } else {
      //produkt neexistuje
      return false;
    }

    if ($img === NULL) {
      $img = $this->database->fetch("SELECT imgid, imgproid, imgurl, imgindex, imgstatus, imgdateu FROM ".TABLENAME_IMPORT_IMAGES_UPDATE." WHERE imgproid=%i", $proid, " AND imgindex=%i", $index);
    }

    If ($img && !empty($url) && $img->imgurl != $url) {
      $updImg->update($img->imgid, array('imgurl'=>$url));
    }

    if ((int)$pro->propicfix === 1) {
      //obrázek fixovaný, neaktualizuji
      return TRUE;
    }

    if ($img && $img->imgstatus !== 'upd') {
      //záznam je v logu a čeká na aktualizaci
      return true;
    }

    $updImg  = $this->em->getImport_Images_Update();
    $headers = @get_headers($url, 1);
    if ($headers === FALSE) {
      //nepodařilo se načíst - vymažu
      if (!empty($img->imgid)) {
        $updImg->delete($img->imgid);
      }
      return false;
    }
    $ret = (int)substr($headers[0], 9, 3);
    if ($ret < '400') {
      if (!isset($headers["Last-Modified"])) {
        if ($img === NULL) {
          $fileDateTime = new \DateTime();
        } else {
          return false;
        }
      } else {
        $lasModified = $headers["Last-Modified"];
        $fileDateTime = new \DateTime($lasModified);
      }

      //obrázek existuje zapíšu do logu aktualizace obrázků
      if ($img === NULL) {
        $values = array(
          'imgproid' => $proid,
          'imgindex' => $index,
          'imgurl' => $url,
          'imgstatus' => 'mis',
          'imgdateu' => $fileDateTime,
        );
        $updImg->insert($values);
      } else {
        //zjistim jestli je třeba aktualizovat - jen ty co mají status upd - aktuální
        if ($img->imgstatus === 'upd') {
          $dbDateTime = new \DateTime($img->imgdateu);
          if ($fileDateTime > $dbDateTime) {
            $updImg->update($img->imgid, array('imgstatus'=>'old'));
          }
        }
      }
    } else {
      if (!empty($img->imgid)) {
        $updImg->delete($img->imgid);
      }
      return false;
    }
    return true;
  }





  /**
   * deaktivuje položky od aktuálního dodavatele, které nebyly aktualizovány do data $startDate
   *
   * @param  $startDate
   * @param bool $setDisable
   * @param array $skippedProIds
   * @return int počet aktualizovaných položek
   * @throws Exception
   */
  protected function deactivateNotUpdatedItems($startDate, bool $setDisable=TRUE, array $skippedProIds = []): int {
    if (count($skippedProIds) === 0) {
      $skippedProIds[] = -1;
    }
    $prostatus = 1;
    if (!$setDisable) {
      $prostatus = 0;
    }

    //položky co jsou skladem nastavím default dostupnost nedostupné protože není ve feedu
    $this->database->query("
      UPDATE products_web2 SET 
        proqty_atc=0,
        proaccessdef=101, 
        promalldateu=Now(),                        
        prodateu=Now()                        
      WHERE 
        provenid='" . $this->type . "' AND 
        prostatus=0 AND 
        proodfix=0 AND 
        (proaccess_web2=0 OR proaccess_web3=0) AND
        proaccessdef<101 AND  
        coalesce(prodateu,prodatec)<%dt", $startDate, " AND 
        proid NOT IN (%i)", $skippedProIds
    );

    $cnt = $this->database->getAffectedRows();

    //položky co nejsou skladem deaktivuji, protože není ve feedu
    $this->database->query("
      UPDATE products_web2 SET 
        prostatus=$prostatus, 
        proqty_atc=0,
        proaccess_web2=101, 
        proaccess_web3=101,
        proaccessdef=101,
        promalldateu=Now(),                        
        prodateu=Now()                        
      WHERE 
        provenid='" . $this->type . "' AND 
        prostatus=0 AND 
        proodfix=0 AND 
        proaccess_web2>0 AND proaccess_web2<101 AND
        proaccess_web3>0 AND proaccess_web3<101 AND 
        coalesce(proqty_web2, 0)=0 AND 
        coalesce(proqty_web3, 0)=0 AND 
        coalesce(prodateu,prodatec)<%dt", $startDate, " AND 
        proid NOT IN (%i)", $skippedProIds
    );

    $cnt += $this->database->getAffectedRows();

    return $cnt;
  }

  /**
   * @throws \Exception
   */
  protected function getVendorXml(): SimpleXMLElement|bool {
    //stáhnu datový soubor
    $data = $this->downloadFeed();
    if (empty($data)) {
      $this->writeLog('err', 'Import nelze dokončit. Datový soubor se nepodařilo stáhnout.');
      return FALSE;
    }

    return $this->getSimpleXMLElement(NULL, $data);
  }

  protected function getSimpleXMLElement($fileName = NULL, $xmlContent = NULL): SimpleXMLElement|bool {
    if ($fileName !== NULL) {
      return new SimpleXMLElement(file_get_contents($fileName), LIBXML_COMPACT | LIBXML_PARSEHUGE);
    } else if ($xmlContent !== NULL) {
      return new SimpleXMLElement($xmlContent, LIBXML_COMPACT | LIBXML_PARSEHUGE);
    }
    return FALSE;
  }

  protected function getXMLReader($fileName): \XMLReader|bool {
    $xml = new \XMLReader();
    if (!$xml->open($fileName)) {
      $this->writeLog("err", "Zdrojový soubor se nepodařilo načíst");
      return FALSE;
    }

    return $xml;
  }

  protected function clearImportTables(): void {
    $this->database->begin();
    $this->database->query('SET FOREIGN_KEY_CHECKS = 0');
    $this->database->query('DELETE FROM ' . TABLENAME_IMPORT_PROPARAMS . " WHERE prpadmid=" . $this->admId);
    $this->database->query('DELETE FROM ' . TABLENAME_IMPORT_PROATACHMENTS . " WHERE ataadmid=" . $this->admId);
    $this->database->query('DELETE FROM ' . TABLENAME_IMPORT_PRODUCTS_FIELDS . " WHERE prfadmid=" . $this->admId);
    $this->database->query('DELETE FROM ' . TABLENAME_IMPORT_IMAGES . " WHERE imgadmid=" . $this->admId);
    $this->database->query('DELETE FROM ' . TABLENAME_IMPORT_CATPLACES . " WHERE capadmid=" . $this->admId);
    $this->database->query('DELETE FROM ' . TABLENAME_IMPORT_PRODUCTS . " WHERE proadmid=" . $this->admId);
    $this->database->query('SET FOREIGN_KEY_CHECKS = 1');
    $this->database->commit();
  }

  /**
   * aktualizuje obrázky
   *
   * @param $product
   * @param array $imagesToUpdate pole URL obrázků k aktualizaci
   * @param bool $clearUrl vymaže z URL ? a vše za ním
   * @param bool $resetImages
   * @return void
   * @throws Exception
   */
  protected function updateProductImages($product, array $imagesToUpdate, bool $clearUrl = TRUE, bool $resetImages = FALSE) : void {
    $impImagesUpdate  = $this->em->getImport_Images_Update();

    if ((int)$product->propicfix === 1) {
      return;
    }

    //obrázky
    $validImages = [];
    foreach ($imagesToUpdate as $idx => $url) {
      if ($clearUrl) {
        //odstraním z URL vše za ?
        $arr = explode("?", $url);
        $url = $arr[0];
      }

      //podle URL obrázku zjstím, jestli obrázek existuje - pokud $resetImages = TRUE beru jako že neexistuje
      If ($resetImages) {
        $img = NULL;
      } else {
        $img = $this->database->fetch("SELECT imgid, imgindex FROM import_images_update_web2 WHERE imgproid=%i", $product->proid, " AND imgurl=%s", trim($url));
      }

      if ($img !== NULL) {
        //existuje neřeším
        $validImages[] = $img->imgid;
      } else {
        //neexistuje - založím
        //musím najít volný index
        for ($i = 0; $i <= 9; $i++) {
          //zjistím, jestli pro daný produkt a index už neexistuje
          $cnt = (int)$this->database->fetchSingle("SELECT count(imgid) FROM " . TABLENAME_IMPORT_IMAGES_UPDATE . " WHERE imgproid=%i", $product->proid , " AND imgindex=%i", $i);
          if ($cnt === 0) {
            $data = array(
              'imgproid' => $product->proid,
              'imgurl' => $url,
              'imgstatus' => 'mis',
              'imgindex' => $i,
            );
            $validImages[] = $impImagesUpdate->insert($data);
            break;
          }
        }
      }
    }

    //ostatní obrázky produktu vymažu
    if (count($validImages) > 0) {
      $this->database->query("DELETE from import_images_update_web2  WHERE imgproid=%i", $product->proid, " AND imgid NOT IN (%i)", $validImages);
    } else {
      $this->database->query("DELETE from import_images_update_web2  WHERE imgproid=%i", $product->proid);
    }

    //po vymazání aktualizuji index
    $i = 0;
    $rows = $this->database->fetchAll("SELECT imgid, imgindex FROM " . TABLENAME_IMPORT_IMAGES_UPDATE . " WHERE imgproid=%i", $product->proid, " ORDER BY imgindex");
    foreach ($rows as $row) {
      if ((int)$row->imgindex !== $i) {
        $impImagesUpdate->update($row->imgid, ["imgindex" => $i]);
      }
      $i ++;
    }
  }

  /**
   * aktualizuje přílohy
   *
   * @param $product
   * @param array $filesToUpdate subory k aktualizaci
   * @param bool $clearUrl
   * @return void
   * @throws Exception
   */
  protected function updateProductFiles($product, array $filesToUpdate, bool $clearUrl = TRUE) : void {
    $impProAttachments  = $this->em->getProAttachments();

    foreach ($filesToUpdate as $file) {
      //zjistím jestli existuje
      $ataId = (int)$this->database->fetchSingle("SELECT ataid FROM " . TABLENAME_PROATACHMENTS . " WHERE ataproid=%i", $product->proid, " AND atacode=%s", $file["code"]);

      $vals = array(
        'atacode' => $file["code"],
        'ataproid' => $product->proid,
        'ataname' => $file["name"],
        'atafilename' => $file["url"],
        'atatype' => pathinfo($file["url"], PATHINFO_EXTENSION)
      );
      if ($ataId === 0) {
        $impProAttachments->insert($vals);
      } else {
        $impProAttachments->update($ataId, $vals);
      }
    }
  }

  /**
   * aktualizuje parametry zboží
   *
   * @param $product
   * @param array $parameters pole parametrů
   * @return void
   * @throws Exception
   */
  protected function updateProductParameters($product, array $parameters) : void {
    $proParams = $this->em->getProParams();
    foreach ($parameters as $parameter) {
      if (!empty($parameter["name"]) && !empty($parameter["value"])) {
        $vals = array(
          'prpproid' => $product->proid,
          'prpname' => $parameter["name"],
          'prpvalue' => $parameter["value"],
        );

        if (!empty($parameter["order"])) {
          $vals["prporder"] = $parameter["order"];
        }

        if (!empty($parameter["unit"])) {
          $vals["prpunit"] = $parameter["unit"];
        }

        //zjistím zda parametr existuje
        $par = $this->database->fetch("SELECT * FROM proparams_web2 WHERE prpproid=%i", $vals["prpproid"], " AND prpname=%s", $vals["prpname"]);
        if ($par) {
          if ($par->prpvalue != $vals["prpvalue"]) {
            unset($vals["prpproid"], $vals["prpname"]);
            $proParams->update($par->prpid, $vals);
          }
        } else {
          $proParams->insert($vals);
        }
      }
    }
  }

  /**
   * vrátí název obrázku odstraní nevhodné znaky
   * @param string $proCode
   * @return string
   */
  protected function getSafeCode(string $proCode): string {
    return  \Nette\Utils\Strings::webalize($proCode, '+');
  }

  /**
   * zda objednávku je možno poslat do heuréky dle dodavatele
   *
   * @param string $vendorId - id dodavatele
   * @param int $manId - id výrobce
   * @return bool
   */
  static function isAllowedByAggregator(string $vendorId, int $manId = 0): bool {
    $allowed = TRUE;

    $blockedVendors = [self::VENDOR_HOMELUX, self::VENDOR_DEXON, self::VENDOR_NOVASERVIS, self::VENDOR_BSACOUSTIC, self::VENDOR_SPOKEY, self::VENDOR_EASYOFFICE, self::VENDOR_MADALBAL, self::VENDOR_NOSRETI, self::VENDOR_EAST, self::VENDOR_PARTNERTELE, self::VENDOR_PENTA, self::VENDOR_TSB, self::VENDOR_MATYSKA, self::VENDOR_IRD, self::VENDOR_MUJPERFEKT, self::VENDOR_WILLI, self::VENDOR_PLACEK];

    if (in_array($vendorId, $blockedVendors)) {
      $allowed = FALSE;
    }

    if ($allowed && $manId === 1703 && $vendorId === self::VENDOR_FAST) {
      $allowed = FALSE;
    }

    return $allowed;
  }

  /**
   * @param int $proId id produktu
   * @param string $proName název produktu
   * @param array $catalogs pole proId => catName
   * @return string
   */
  protected function getProNameByCatalogName(int $proId, string $proName, array $catalogs): string {
    if (!empty($catalogs[$proId])) {
      $catName = $catalogs[$proId];

      if (stripos($catName, 'neoriginální') !== FALSE && !str_contains($proName, 'neoriginální')) {
        $proName .= ' - neoriginální';
      } else if (stripos($catName, '- originální') !== FALSE && !str_contains($proName, ' - originální')) {
        $proName .= ' - originální';
      }
    }
    return $proName;
  }

  protected function formatStringToNumber($value): float {
    $str = (string)$value;
    return (double)str_replace([' ', '.', ','], ['', '', '.'], $str);
  }

  public function jobStart($type, $catCode=NULL): void {
    //zaloguji
    $this->updateLog = new VendorUpdateLog();
    $this->updateLog->vendor=$this->vendor;
    $this->updateLog->status=$this->updateLog::STATUS_RUNNING;
    $this->updateLog->catCode=$catCode;
    $this->updateLog->type=$type;
    $this->updateLog->deleteSource = TRUE;
    $this->updateLog->dataFile = $this->downloadPath . $this->updateLog->type . (!empty($this->updateLog->catCode) ? "-" . $this->getSafeCode($this->updateLog->catCode) : "") . ".xml";
    $this->updateLog->forceUpdate = $this->forceUpdate;
    $this->orm->persistAndFlush($this->updateLog);

    if ($this->forceUpdate) {
      $this->writeLog("log", "Vynucená aktualizace.");
    }

  }

  /**
   * @param $data - textově data - obsah XML
   * @param $filePath - cesta k datovému soubor - existuje na lokále, vštšinou nějaký excel
   * @return bool
   */
  public function writeFileHashCompare($data = NULL, $filePath = NULL): bool {
    if ($data) {
      //poslal data musím nejdčíve uložit do souboru
      $this->updateLog->dataFile = $this->downloadPath . $this->updateLog->type . (!empty($this->updateLog->catCode) ? "-" . $this->getSafeCode($this->updateLog->catCode) : "") . ".xml";
      @unlink($this->updateLog->dataFile);
      file_put_contents($this->updateLog->dataFile, $data);
    } else if ($filePath) {
      $this->updateLog->dataFile = $filePath;
      $this->updateLog->deleteSource = FALSE;
    } else {
     $this->updateLog->dataFile = $this->downloadPath . $this->updateLog->type . (!empty($this->updateLog->catCode) ? "-" . $this->getSafeCode($this->updateLog->catCode) : "") . ".xml";
    }

    if (file_exists($this->updateLog->dataFile)) {
      $this->updateLog->dataHash = hash_file("haval160,4", $this->updateLog->dataFile);
    }

    //porovnám hash s posledním spuštěním
    $by = [
      "vendor" => $this->updateLog->vendor,
      "status" => [$this->updateLog::STATUS_SUCCESS, $this->updateLog::STATUS_SKIPPED],
      "type" => $this->updateLog->type,
      "dataHash!=" => NULL,
    ];

    if ($this->updateLog->catCode) {
      $by["catCode"] = $this->updateLog->catCode;
    }


    if ($this->updateLog->forceUpdate !== TRUE) {
      $updateLogrev = $this->orm->vendorUpdateLog->findBy($by)->orderBy(["start"=>ICollection::DESC])->limitBy(1)->fetch();
      If ($updateLogrev && $updateLogrev->dataHash === $this->updateLog->dataHash) {
        //data jsou stejné jako předchozí spuštění, není třeba dělat aktualizaci

        $this->updateLog->status = $this->updateLog::STATUS_SKIPPED;
        $this->updateLog->memoryUsage = round(memory_get_peak_usage()/1048576,2);
        $this->updateLog->end = new DateTimeImmutable();
        $this->orm->persistAndFlush($this->updateLog);

        if (!empty($this->updateLog->dataFile) && $this->updateLog->deleteSource === TRUE) {
          @unlink($this->updateLog->dataFile);
        }

        return FALSE;
      }
    }

    $this->orm->persistAndFlush($this->updateLog);

    return TRUE;
  }

  public function jobEnd(): void {
    //zaloguji
    if (isset($this->updateLog)) {
      $this->updateLog->vendor = $this->vendor;
      $this->updateLog->status = $this->updateLog::STATUS_SUCCESS;
      $this->updateLog->end = new DateTimeImmutable();
      $this->updateLog->memoryUsage = round(memory_get_peak_usage()/1048576,2);
      $this->orm->persistAndFlush($this->updateLog);

      if (!empty($this->updateLog->dataFile) && $this->updateLog->deleteSource === TRUE) {
        @unlink($this->updateLog->dataFile);
      }
    }
  }
  public function jobLogMessage($message): void {
    //zaloguji
    if (isset($this->updateLog)) {
      $this->updateLog->message = end($this->log);
      $this->orm->persistAndFlush($this->updateLog);
    }
  }

  public function getAccessByDaysCount($days, $onStock) {
    $access = 101;

    if ($days <= 1 || $onStock > 0) {
      $access = (int)$this->config["proAccessOnStock"];
    } else if ($days <= 3) {
      $access = 3;
    } else if ($days <= 7) {
      $access = 7;
    } else if ($days <= 14) {
      $access = 14;
    } else if ($days <= 30) {
      $access = 30;
    } else if ($days <= 99) {
      $access = 99;
    } else if ($days <= 100) {
      $access = 100;
    }
    return $access;
  }

  protected function convToUtf8 ($text) {
    return iconv('Windows-1250', "UTF-8", $text);
  }


}