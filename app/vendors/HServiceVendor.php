<?php
namespace App\vendors;

use App\Orm\Margin;
use <PERSON><PERSON><PERSON>\SimpleXLSX;

class HServiceVendor extends ImportVendor {

  /** @var string */
  protected $type = self::VENDOR_HSERVICE;
  private array $colInfo = [];

  /**
   * naplní pole s cenami produktů
   *
   * @param $item
   * @return array
   */
  private function calculatePrices($item): array {
    //cena ve feedu - připočtu DPH
    $priceFeed = (double)$item['price'];
    $priceCom = round($priceFeed);

    return array(
      'propricecom_web2' => $priceCom,
      'propricecom_web3' => $priceCom,
      'propriced_web2' => (double)$item['price'],
      'propriced_web3' => (double)$item['price'],
    );
  }

  public function downloadFeeds(): bool|string {
	  if (file_exists($this->downloadPath . "/data.xlsx")) {
		  return false;
	  }
	  $this->downloadFeed(to: $this->downloadPath . "/data.xlsx", returnData: false);
	  return true;
  }

  public function runImport() {
    $this->database->onEvent = NULL;

    $impPros  = $this->em->getImport_Products();

    //kontrola zda nejsou v tabulce produkty připraveny pro import
    $cnt = (int)$this->database->fetchSingle('SELECT COUNT(proid) FROM ' . $impPros->table . ' WHERE proready=1 AND proadmid=' . $this->admId);
    if ($cnt > 0) {
      $this->writeLog('err', 'Import nelze dokončit, existují produkty odeslány k importu, které ještě nejsou naimportovany.');
      return;
    }

    $impPros  = $this->em->getImport_Products();
    $impImgs  = $this->em->getImport_Images();
    $impProAttachments  = $this->em->getImport_ProAttachments();

    $this->writeLog('log', 'Import spuštěn.');

    $this->clearImportTables();

	$data = $this->getXlsxData();

	$this->writeLog('log', 'Importuji nové zboží');
    $proCnt = 0;
	$colInfo = $data[0];
	unset($data[0]);

	$existingProducts = $this->database->fetchPairs('SELECT procodeven, proid FROM ' .TABLENAME_PRODUCTS. ' WHERE provenid=%s', self::VENDOR_HSERVICE);

    foreach ($data as $item) {

		$data = $this->prepareData($item, $colInfo, FALSE);

		if (!isset($existingProducts[$data["procodeven"]])) {
		    $data["proadmid"] = $this->admId;
		    $proid = (int)$impPros->insert($data);
		    $proCnt++;

		    /*//odkaz na obrázky
		    $data = array(
		      'imgproid' => $proid,
		      'imgurl' => (string)$item->IMGURL,
		      'imgadmid' => $this->admId,
		    );
		    $impImgs->insert($data);

		    foreach ($item->IMGURL_ALTERNATIVE as $key => $imgUrl) {
		      if ($imgUrl !== NULL) {
		        $data = array(
		          'imgproid' => $proid,
		          'imgurl' => (string)$imgUrl,
		          'imgadmid' => $this->admId,
		        );
		        $impImgs->insert($data);
		      }
		    }

		    //přílohy
		    $manualUrl = (string)$item->MANUALURL;
		    if (!empty($manualUrl)) {
		      $extension = pathinfo($manualUrl, PATHINFO_EXTENSION);

		      $vals = array(
		        'ataproid' => $proid,
		        'ataname' => 'Návod',
		        'atafilename' => $manualUrl,
		        'atatype' => $extension,
		        'ataadmid' => $this->admId,
		      );
		      $impProAttachments->insert($vals);
		    }*/
		}
    }
    $this->writeLog('log', "Hotovo. Aktualizováno $proCnt položek.");
  }

  private function prepareData($item, $colInfo, bool $isEdit = TRUE): array
    {
        $mItem = [];
	    foreach ($colInfo as $index => $columnName) {
	        $mItem[$columnName] = $item[$index] ?? null;
	    }
		$manId = $this->getManufacturer((string)$mItem['manufacturer']);
		$proVenCode = (string)$mItem['code'];
	    $qtyOnStock = (int) $mItem['stock'];
	    $prices = $this->calculatePrices($mItem);

		$ean = Null;
        if (!empty($mItem['ean'])) {
            $ean = (string)$mItem['ean'];
            if ($this->checkEan($ean) != '') {
                $ean = '';
            }
        }

		$data['promanid'] = $manId;
		$data['proname'] = $mItem['name'] . ", " . $proVenCode;
		$data['prodescs'] = (string) $mItem['shortDescription'];
		$data['prodesc'] = (string) $mItem['description'];
		$data['proaccess_web2'] = ($qtyOnStock > 0 ? $this->config["proAccessOnStock"] : 101);
		$data['proaccess_web3'] = ($qtyOnStock > 0 ? $this->config["proAccessOnStock"] : 101);
		$data['proaccessdef'] = ($qtyOnStock > 0 ? $this->config["proAccessOnStock"] : 101);
		$data['proqty_atc'] = $qtyOnStock;

		$basePrice = (double)$mItem['price'];
        $data['propricea_web2'] = Margin::calculateDefaultPrice($basePrice);
        $data['propricea_web3'] = Margin::calculateDefaultPrice($basePrice);
        $data['propricecom_web2'] = $prices['propricecom_web2'];
        $data['propricecom_web3'] = $prices['propricecom_web3'];
        $data['propriced_web2'] = $prices['propriced_web2'];
        $data['propriced_web3'] = $prices['propriced_web3'];

		if (!$isEdit) {
	        $data['procode'] = $this->getProCode($proVenCode);
			$data['procode2'] = $ean;
			$data['provenid'] = self::VENDOR_HSERVICE;
			$data['vendor_id'] = $this->vendor->id;
			$data['procodeven'] = $proVenCode;
	        $data["propricefix_web2"] = 0;
	        $data["propricefix_web3"] = 0;
	        $data['propicname'] = $this->getSafeCode($data["procode"]);
			$data['provatid'] = 0;
			$data['protypid'] = 0;
			$data['protypid2'] = 0;
			$data['protypid3'] = 0;
			$data['provenurl'] = 0;
			$data['prowarranty'] = '24';
	    }

		return $data;
    }

  public function runUpdate() {
      $this->database->onEvent = NULL;
	  $pros = $this->em->getProducts();

    //začátek importu
    $start = $this->database->fetchSingle("SELECT NOW()");
    $skippedProIds = [];

    $this->writeLog('log', 'Aktualizuji zboží.');

    $this->jobStart(__FUNCTION__);

    //stáhnu data feed od dodavatele
    if ($this->writeFileHashCompare($this->downloadFeed()) === FALSE) {
      $this->writeLog('log', 'Předčasně ukončeno, aktualizace není nutná.');
      return;
    }

    $xml = $this->getSimpleXMLElement($this->updateLog->dataFile);

    $proCnt = 0;
    $myCnt = 0;
    $descCnt = 0;
    $nextCnt = mt_rand(40, 70);
    $directUpdate = FALSE;
    foreach ($xml->SHOPITEM as $item) {

      //zjistim jestli produkt neexistuje
      $procode = $this->config["codePrefix"] . $item->ITEM_ID;
      $pro = $this->database->fetch('
        SELECT proid, procode, proaccessdef, proaccess_web2, proaccess_web3, propricefix_web2, propricefix_web3, pronamefix, prodescfix, promanidfix, proodfix, prostatus 
        FROM ' .TABLENAME_PRODUCTS. ' 
        WHERE procode=%s', $procode);

      if ($pro === NULL) {
        continue;
      }

      //pokud je produkt blokovaný v aktualizaci, tak ho přeskočím
      if ($pro->proodfix == 1) {
        $skippedProIds[] = $pro->proid;
        continue;
      }

      $qtyOnStock = (string)$item->DELIVERY_DATE === "0" ? 1 : 0;

      $prices = $this->calculatePrices($item);

      $data = [
        'proname' => (string)$item->PRODUCTNAME . ", " . (string)$item->ITEM_ID,
        'prodescs' => (string)$item->DESCRIPTION,
        'propricecom_web2' => $prices["propricecom_web2"],
        'propricecom_web3' => $prices["propricecom_web3"],
        'propriced_web2' => $prices["propriced_web2"],
        'propriced_web3' => $prices["propriced_web3"],
        'proaccess_web2' => ($qtyOnStock > 0 ? $this->config["proAccessOnStock"] : 101),
        'proaccess_web3' => ($qtyOnStock > 0 ? $this->config["proAccessOnStock"] : 101),
        'proaccessdef' => ($qtyOnStock > 0 ? $this->config["proAccessOnStock"] : 101),
        'proqty_atc' => $qtyOnStock,
      ];

      $descUrl = (string)$item->FULLDESCRIPTIONURL;
      if (!empty($descUrl)) {
        $updateDesc = FALSE;
        if (empty($pro->prodesc) && $directUpdate === FALSE) {
          $updateDesc = TRUE;
          $directUpdate = TRUE;
        } else {
          $myCnt ++;
          if ($myCnt === $nextCnt) {
            $updateDesc = TRUE;
            $directUpdate = FALSE;
          }
        }
        if ($updateDesc) {
          $desc = file_get_contents($descUrl);
          $desc = iconv('Windows-1250', "UTF-8", $desc);
          $data['prodesc'] = nl2br($desc);
          $descCnt ++;
          $myCnt = 0;
          $nextCnt = mt_rand(40, 70);
        }

      } else {
        $data['prodesc'] = nl2br($data["prodescs"]);
      }

      $data = $this->checkDataBeforeUpdate($data, $pro);

      if ($pro->prostatus == 1) {
        $data["prostatus"] = 0;
      }

      //musí dojít k aktuializaci jinak se položka deaktivuje - viz dále
      if (count($data) > 0) {
        $pros->update($pro->proid, $data);
      } else {
        $skippedProIds[] = $pro->proid;
      }

      $proCnt ++;

      //aktualizuji obrázky
      if (!empty($item->IMGURL)) {
        $imgUrl = (string)$item->IMGURL;
        $this->checkProImgFromVendor($pro->proid, (string)$imgUrl);
      }

    }

    //deaktuvuji všechny položky co nebyly aktualizované - nejsou ve feedu a nejsou skladem
    $cnt = $this->deactivateNotUpdatedItems($start, FALSE, $skippedProIds);

    $this->writeLog('log', "Hotovo. Aktualizováno $proCnt položek, $descCnt popisů, deaktivováno " . $cnt . " položek.");

    $this->finalUpdatePrice();

    $this->jobEnd();
  }

  public function updatePrices() {

    $this->database->query("
      update products_web2 set need_recalculate_price=1
      where
          provenid=%s AND (margin_web2 is not null OR margin_web3 is not null) AND
          (propricefix_web2=0 AND propricea_web2 < propriced_web2) OR (propricefix_web3=0 AND propricea_web3 < propriced_web3)", $this->type);

    $this->finalUpdatePrice();
    $this->finalUpdatePrice(TRUE);
  }

  private function getXlsxData(): array {
	  $fileName = $this->downloadPath . "/data.xlsx";
	  $this->downloadFeeds();
	  $xlsx = new SimpleXLSX($fileName);
	  if (!$xlsx->success()) {
		  $this->writeLog('err', $xlsx->error());
	      return[];
      }
	  return $xlsx->rows();
  }
}