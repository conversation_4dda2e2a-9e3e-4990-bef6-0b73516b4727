<?php
namespace App\vendors;

use App\Orm\Margin;
use <PERSON>bi\Exception;

class HServiceVendor extends ImportVendor {

  /** @var string */
  protected $type = self::VENDOR_HSERVICE;

  /**
   * naplní pole s cenami produktů
   *
   * @param $item
   * @return array
   */
  private function calculatePrices($item): array {
    //cena ve feedu s DPH
    $priceFeed = (double)$item->PRICE_VAT;
    $priceCom = round($priceFeed);

    return array(
      'propricecom_web2' => $priceCom,
      'propricecom_web3' => $priceCom,
      'propriced_web2' => (double)$item->PRICE,
      'propriced_web3' => (double)$item->PRICE,
    );
  }

  public function runImport() {
    $this->database->onEvent = NULL;

    $impPros  = $this->em->getImport_Products();

    //kontrola zda nejsou v tabulce produkty připraveny pro import
    $cnt = (int)$this->database->fetchSingle('SELECT COUNT(proid) FROM ' . $impPros->table . ' WHERE proready=1 AND proadmid=' . $this->admId);
    if ($cnt > 0) {
      $this->writeLog('err', 'Import nelze dokončit, existují produkty odeslány k importu, které ještě nejsou naimportovany.');
      return;
    }

    $impPros  = $this->em->getImport_Products();
    $impImgs  = $this->em->getImport_Images();
    $impProAttachments  = $this->em->getImport_ProAttachments();

    $this->writeLog('log', 'Import spuštěn.');

    $this->clearImportTables();

    //stáhnu data feed od dodavatele
    $xml = $this->getVendorXml();

    $this->writeLog('log', 'Importuji nové zboží');
    $proCnt = 0;

    $existingProducts = $this->database->fetchPairs('SELECT procodeven, proid FROM ' .TABLENAME_PRODUCTS. ' WHERE provenid=%s', self::VENDOR_HSERVICE);

    foreach ($xml->SHOPITEM as $item) {

      $data = $this->prepareData($item, FALSE);
      $otherData = $data["other"];
      unset($data["other"]);

      if (!isset($existingProducts[$data["procodeven"]])) {
        $data["proadmid"] = $this->admId;
        $proid = (int)$impPros->insert($data);
        $proCnt++;

        //obrázky pro předimport
        foreach ($otherData["images"] as $url) {
          if (!empty($url)) {
            $vals = array(
              'imgproid' => $proid,
              'imgurl' => (string)$url,
              'imgadmid' => $this->admId,
            );
            $impImgs->insert($vals);
          }
        }
      }
    }
    $this->writeLog('log', "Hotovo. Aktualizováno $proCnt položek.");
  }

  /**
   * připraví data na aktualizaci
   *
   * @param $item
   * @param bool $isEdit
   * @return array
   * @throws Exception
   */
  private function prepareData($item, bool $isEdit = TRUE): array {
    $data = array();

    $data["procodeven"] = (string)$item->CODE;

    if (!$isEdit) {
      $data["procode"] = $this->getProCode($data["procodeven"]);
      $data["propricefix_web2"] = 0;
      $data["propricefix_web3"] = 0;
      $data['propicname'] = $this->getSafeCode($data['procode']);
      $data['need_recalculate_price'] = 1;
    }

    $qtyOnStock = (int)$item->STOCK->AMOUNT;

    //zjistím vat ID
    $vat = (int)$item->VAT;
    $vatId = $this->getVatId($vat);

    //vyrobce
    $data["promanid"] = $this->getManufacturer((string)$item->MANUFACTURER);

    $basePrice = (double)$item->PRICE;
    $mocPrice = (double)$item->PRICE_VAT;

    //EAN
    if (!empty($item->EAN)) {
      $ean = (string)$item->EAN;
      if ($this->checkEan($ean) != '') {
        $ean = '';
      }
      $data["procode2"] = $ean;
    }

    $data['provenid'] = $this->type;
    $data['vendor_id'] = $this->vendor->id;

    $data['proname'] = (string)$item->NAME;
    $data['proname'] .= " " . $data["procodeven"];

    $data['prodescs'] = strip_tags((string)$item->SHORT_DESCRIPTION);
    $data['prodesc'] = (string)$item->DESCRIPTION;

    if (!$isEdit) {
      //ceny nastavuji jen při importu v editace musí aktualizace cen projít procedurou UpdatePrices
      $data['propricecom_web2'] = $mocPrice;
      $data['propricecom_web3'] = $mocPrice;
      $data['propricea_web2'] = Margin::calculateDefaultPrice($basePrice);
      $data['propricea_web3'] = Margin::calculateDefaultPrice($basePrice);
      $data['propriced_web2'] = round($basePrice);
      $data['propriced_web3'] = round($basePrice);
    }

    $data['proaccess_web2'] = $this->getAccess($qtyOnStock);
    $data['proaccess_web3'] = $this->getAccess($qtyOnStock);
    $data['proaccessdef'] = $this->getAccess($qtyOnStock);
    $data['provatid'] = $vatId;
    $data['proqty_atc'] = $qtyOnStock;
    $data['prowarranty'] = 24;

    //předžvýkám ostatní data
    $data["other"] = [];

    $data["other"]["images"] = [];
    if (!empty($item->IMAGES)) {
      $index = 0;
      foreach ($item->IMAGES->IMAGE as $url) {
        if (!empty($url) && $index < 10) {
          $data["other"]["images"][] = (string)$url;
          $index++;
        }
      }
    }

    return $data;
  }

  /**
   * aktualizuje položky dle katalogu
   *
   * @param bool $updateImages
   * @return void
   * @throws Exception
   */
  public function runUpdate(bool $updateImages = FALSE) {
    $pros = $this->em->getProducts();

    $this->database->onEvent = NULL;

    $this->jobStart(__FUNCTION__);

    //stáhnu data feed od dodavatele
    if ($this->writeFileHashCompare($this->downloadFeed()) === FALSE) {
      $this->writeLog('log', 'Předčasně ukončeno, aktualizace není nutná.');
      return;
    }

    $xml = $this->getSimpleXMLElement($this->updateLog->dataFile);

    $proCnt = 0;

    $dateTimeStart = (string)$this->database->fetchSingle("SELECT NOW()");
    $skippedProIds = [];

    foreach ($xml->SHOPITEM as $item) {
      //aktualizuji jen stávající položky co nejsou blokované v aktualizaci
      $pro = $this->loadProductForUpdate((string)$item->CODE);
      if ($pro === NULL) {
        continue;
      }

      $data = $this->prepareData($item);
      $otherData = $data["other"];
      unset($data["other"]);

      $qtyOnStock = (int)$item->STOCK->AMOUNT;
      if ($qtyOnStock <= 0) {
        //položka není na skladě u dodavatele - nastavím default dostupnost 101
        $data['proaccessdef'] = 101;

        if ((int)$pro->proaccess_web2 > 0 && (int)$pro->proaccess_web3 > 0) {
          //není skladem - nastavím dostupnost 101 - nejde zakoupit
          $data['proaccess_web2'] = 101;
          $data['proaccess_web3'] = 101;
        }
      }

      $data = $this->checkDataBeforeUpdate($data, $pro);

      if (count($data) > 0) {
        if ($pros->update($pro->proid, $data)){
          $proCnt++;
        }
      } else {
        $skippedProIds[] = $pro->proid;
      }

      $this->updateProductImages($pro, $otherData["images"]);
    }

    //deaktuvuji všechny položky co nebyly aktualizované - nejsou ve feedu a nejsou skladem
    $cntDisabled = $this->deactivateNotUpdatedItems($dateTimeStart, FALSE, $skippedProIds);

    $this->writeLog('log', "Hotovo. $proCnt aktualizovaných položek. $cntDisabled nebylo ve feedu.");

    $this->jobEnd();
  }

  public function updatePrices() {
    $filePath = $this->downloadPath . "/data.xml";

    $this->jobStart(__FUNCTION__);

    $this->database->onEvent = NULL;

    if (!file_exists($filePath)) {
      $this->writeLog('err', 'Zdrojový XML ceník nebyl nalezen. Aktualizace cen nebyla provedena.');
      return;
    }

    if ($this->writeFileHashCompare(NULL, $filePath) === FALSE && $this->forceUpdate == FALSE) {
      $this->writeLog('log', 'Předčasně ukončeno, aktualizace není nutná.');
      return FALSE;
    }

    $xml = $this->getSimpleXMLElement($filePath);

    $cnt = 0;
    $cntAll = 0;
    $cntSkipped = 0;

    foreach ($xml->SHOPITEM as $item) {
      $cntAll++;

      $pro = $this->loadProductForUpdate((string)$item->CODE);
      if (!$pro) {
        $pro = $this->loadProductForUpdate((string)$item->CODE, TABLENAME_IMPORT_PRODUCTS);
        if (!$pro) {
          $cntSkipped++;
          continue;
        } else {
          $pros = $this->em->getImport_Products();
        }
      } else {
        $pros = $this->em->getProducts();
      }

      $proVat = (int)$this->serverConfig["VATTYPE_" . $pro->provatid];

      $priceBuy = $this->getPriceVat((double)$item->PRICE, $proVat);
      $priceCom = (double)$item->PRICE_VAT;

      $data = array(
        'propricecom_web2' => $priceCom,
        'propricecom_web3' => $priceCom,
        'propriced_web2' => $priceBuy,
        'propriced_web3' => $priceBuy,
      );

      $data = $this->checkDataBeforeUpdate($data, $pro);

      if (count($data) > 0) {
        $cnt++;
        $data['need_recalculate_price'] = 1;
        $data['propricedate2'] = new \DateTime();
        $pros->update($pro->proid, $data);
      }
    }

    $this->writeLog('log',"Hotovo. Položek celkem: $cntAll, aktualizováno: $cnt, přeskočeno: $cntSkipped");

    $this->finalUpdatePrice();
    $this->finalUpdatePrice(TRUE);

    $this->jobEnd();
  }

  private function getAccess(int $qtyOnStock): int {
    //vypnutí skladu
    if ($this->vendor->storeOff) {
      return 101;
    }

    if ($qtyOnStock > 0) {
      return $this->config["proAccessOnStock"];
    } else {
      return 101;
    }
  }
}