<?php
namespace App\vendors;

use App\Orm\Margin;
use <PERSON><PERSON>\Exception;

class MadalbalVendor extends ImportVendor {

  /** @var string */
  protected $type = self::VENDOR_MADALBAL;

  public function updatePrices() {
    $filePath = $this->downloadPath . "/cenik.csv";

    $this->jobStart(__FUNCTION__);

    $this->database->onEvent = NULL;

    if (!file_exists($filePath)) {
      $this->writeLog('err', 'Zdrojový CSV ceník nebyl nalezen. Aktualizace cen nebyla provedena.');
      return;
    }

    if ($this->writeFileHashCompare(NULL, $filePath) === FALSE && $this->forceUpdate == FALSE) {
      $this->writeLog('log', 'Předčasně ukončeno, aktualizace není nutná.');
      return FALSE;
    }

    $rows = \SimpleCSV::import($filePath, false, ";");

    //prvn<PERSON> nechci
    unset($rows[0]);

    $cnt = 0;
    $cntAll = 0;
    $cntSkipped = 0;

    foreach ($rows as $row) {
      if (empty($row[0])) {
        break;
      }

      $cntAll++;

      if (str_starts_with($row[0], '=')) {
        $row[0] = substr($row[0], 1);
      }

      $pro = $this->loadProductForUpdate($row[0]);
      if (!$pro) {
        $pro = $this->loadProductForUpdate($row[0], TABLENAME_IMPORT_PRODUCTS);
        if (!$pro) {
          $cntSkipped++;
          continue;
        } else {
          $pros = $this->em->getImport_Products();
        }
      } else {
        $pros = $this->em->getProducts();
      }

      $proVat = (int)$this->serverConfig["VATTYPE_" . $pro->provatid];

      $row[4] = (double)str_replace(array(" ", ","), array("", "."), $row[4]);
      $priceBuy = $this->getPriceVat($row[4], $proVat) ; //je bez DPH musím připočíst DPH

      //MOC
      $row[3] = (double)str_replace(array(" ", ","), array("", "."), $row[3]);
      //MOC akční
      $row[10] = (double)str_replace(array(" ", ","), array("", "."), $row[10]);
      //recycle fee
      $row[12] = (double)str_replace(array(" ", ","), array("", "."), $row[12]);

      $priceCom = $row[3]; //s DPH
      if ($row[10] > 0) {
        $priceCom = $row[10]; //s DPH
      }

      $data = array(
        'propricecom_web2' => $priceCom,
        'propricecom_web3' => $priceCom,

        'propriced_web2' => $priceBuy,
        'propriced_web3' => $priceBuy,

        'prorecfee' => round($row[12] * (1 + ($proVat / 100)), 2),
      );

      $data = $this->checkDataBeforeUpdate($data, $pro);

      if (count($data) > 0) {
        $cnt++;
        $data['need_recalculate_price'] = 1;
        $data['propricedate2'] = new \DateTime();
        $pros->update($pro->proid, $data);
      }
    }

    $this->writeLog('log',"Hotovo. Položek celkem: $cntAll, aktualizováno: $cnt, přeskočeno: $cntSkipped");

    $this->finalUpdatePrice();
    $this->finalUpdatePrice(TRUE);

    $this->jobEnd();
  }

  public function runImport() {
    $impPros  = $this->em->getImport_Products();
    $impImgs  = $this->em->getImport_Images();
    $impPrps  = $this->em->getImport_ProParams();
    $impProAttachments  = $this->em->getImport_ProAttachments();

    $this->database->onEvent = NULL;

    //kontrola zda nejsou v tabulce produkty připraveny pro import
    $cnt = (int)$this->database->fetchSingle('SELECT COUNT(proid) FROM ' . $impPros->table . ' WHERE proready=1 AND proadmid=' . $this->admId);
    if ($cnt > 0) {
      $this->writeLog('err', 'Import nelze dokončit, existují produkty odeslány k importu, které ještě nejsou naimportovany.');
      return;
    }

    //stáhnu data feed od dodavatele
    $xml = $this->getVendorXml();

    $this->clearImportTables();

    $this->writeLog('log', 'Import zboží.');
    $proCnt = 0;

    //u tohoto dodavatele neřeším zařazení do katalogu při importu, stačí mi defaultní marže


    foreach ($xml->Product as $item) {

      //neaktivní položky nebrat
      if (!(bool)$item->Active) {
        continue;
      }

      $data = $this->prepareData($item, FALSE);
      $otherData = $data["other"];
      unset($data["other"]);

      //importuji jen nové položky
      $cnt = (int)$this->database->fetchSingle('SELECT COUNT(*) FROM ' . TABLENAME_PRODUCTS . ' WHERE provenid=%s', $this->type, ' AND procodeven=%s', $data["procodeven"]);
      if ($cnt > 0) {
        continue;
      }


      $data["proadmid"] = $this->admId;
      $proid = (int)$impPros->insert($data);

      $proCnt++;

      //obrázky pro předimport
      foreach ($otherData["images"] as $url) {
        if (!empty($url)) {
          $vals = array(
            'imgproid' => $proid,
            'imgurl' => (string)$url,
            'imgadmid' => $this->admId,
          );
          $impImgs->insert($vals);
        }
      }

      foreach ($otherData["files"] as $file) {
        $vals = [
          "ataproid" => $proid,
          "ataname" => $file["name"],
          "atacode" => $file["code"],
          'atafilename' => $file["url"],
          'ataadmid' => $this->admId,
          'atatype' => pathinfo($file["url"], PATHINFO_EXTENSION)
        ];
        $impProAttachments->insert($vals);
      }

      foreach ($otherData["parameters"] as $parameter) {
        $vals = array(
          'prpproid' => $proid,
          'prpname' => (string)$parameter["name"],
          'prpvalue' => (string)$parameter["value"],
          'prpadmid' => $this->admId,
        );
        $impPrps->insert($vals);
      }
    }

    $this->writeLog('log', "Hotovo. $proCnt nových položek.");

    $this->forceUpdate = TRUE;

    $this->updatePrices();
  }

  /**
   * aktualizuje položky dle katalogu
   *
   * @param bool $updateImages
   * @return void
   * @throws Exception
   */
  public function runUpdate(bool $updateImages = FALSE) {
    $pros = $this->em->getProducts();

    $this->database->onEvent = NULL;

    $this->jobStart(__FUNCTION__);

    //stáhnu data feed od dodavatele
    if ($this->writeFileHashCompare($this->downloadFeed()) === FALSE) {
      $this->writeLog('log', 'Předčasně ukončeno, aktualizace není nutná.');
      return;
    }

    $xml = $this->getSimpleXMLElement($this->updateLog->dataFile);

    $proCnt = 0;

    $dateTimeStart = (string)$this->database->fetchSingle("SELECT NOW()");
    $skippedProIds = [];

    foreach ($xml->Product as $item) {
      //aktualizuji jen stávající položky co nejsou blokované v aktualizaci
      $pro = $this->loadProductForUpdate((string)$item->ProductCode);
      if ($pro === NULL) {
        continue;
      }

      $data = $this->prepareData($item);
      $otherData = $data["other"];
      unset($data["other"]);

      if (!(bool)$item->Active) {
        //položka není aktivní u dodavatele - nastavím default dostupnost 101
        $data['proaccessdef'] = 101;

        if ((int)$pro->proaccess_web2 > 0 && (int)$pro->proaccess_web3 > 0) {
          //není skaldem - nastavím dostupnos 101 - nejde zakoupit
          $data['proaccess_web2'] = 101;
          $data['proaccess_web3'] = 101;
        }
      }

      $data = $this->checkDataBeforeUpdate($data, $pro);

      if (count($data) > 0) {
        if ($pros->update($pro->proid, $data)){
          $proCnt++;
        }
      } else {
        $skippedProIds[] = $pro->proid;
      }

      $this->updateProductImages($pro, $otherData["images"]);
      $this->updateProductFiles($pro, $otherData["files"]);
      $this->updateProductParameters($pro, $otherData["parameters"]);

    }

    //deaktuvuji všechny položky co nebyly aktualizované - nejsou ve feedu a nejsou skladem
    $cntDisabled = $this->deactivateNotUpdatedItems($dateTimeStart, FALSE, $skippedProIds);

    $this->writeLog('log', "Hotovo. $proCnt aktualizovaných položek. $cntDisabled nebylo ve feedu.");

    $this->jobEnd();
  }

  /**
   * připraví data na aktualizaci
   *
   * @param $item
   * @param bool $isEdit
   * @return array
   * @throws Exception
   */
  private function prepareData($item, bool $isEdit=TRUE): array {
    $data = array();

    $data["procodeven"] = (string)$item->ProductCode;

    if (!$isEdit) {
      $data["procode"] = $this->getProCode($data["procodeven"]);
      $data["propricefix_web2"] = 0;
      $data["propricefix_web3"] = 0;
      $data['propicname'] = $this->getSafeCode($data['procode']);
      $data['need_recalculate_price'] = 1;
    }

    $qtyOnStock = $this->getQty($item);

    //zjistím vat ID
    $vat = (string)$item->Vat;
    $vat = (int)str_replace("%", "", $vat);
    $vatId = $this->getVatId($vat);

    //vyrobce
    $data["promanid"] = $this->getManufacturer((string)$item->Brand);

    $basePrice = (double)$item->SpecialPrice;
    $mocPrice = round((double)$item->EndUserVAT);

    //EAN
    if (!empty($item->Logistics->EAN)) {
      $ean = (string)$item->Logistics->EAN;
      if ($this->checkEan($ean) != '') {
        $ean = '';
      }
      $data["procode2"] = $ean;
    }

    $data['provenid'] = $this->type;
    $data['vendor_id'] = $this->vendor->id;

    $data['proname'] = (string)$item->Name;
    $data['proname'] .= " " . $data["procodeven"];

    $data['prodescs'] = strip_tags((string)$item->Description);
    $data['prodesc'] = (string)$item->Description;
    foreach ($item->ExtraDescriptions->ExtraDescription as $desc) {
      if (!empty($desc)) {
        $data['prodesc'] .= "<p>" . (string)$desc . "</p>";
      }
    }

    if (!$isEdit) {
      //ceny nastavuji jen při importu v editace musí aktualizace cen projít procedurou UpdatePrices
      $data['propricecom_web2'] = $mocPrice;
      $data['propricecom_web3'] = $mocPrice;
      $data['propricea_web2'] = Margin::calculateDefaultPrice($basePrice);
      $data['propricea_web3'] = Margin::calculateDefaultPrice($basePrice);
      $data['propriced_web2'] = round($basePrice);
      $data['propriced_web3'] = round($basePrice);
      $data['prorecfee'] = $this->calculateItemRecFee($item);
    }

    $data['proaccess_web2'] = $this->getAccess($item);
    $data['proaccess_web3'] = $this->getAccess($item);
    $data['proaccessdef'] = $this->getAccess($item);
    $data['provatid'] = $vatId;
    $data['proqty_atc'] = $qtyOnStock;
    $data['prowarranty'] = (int)$item->Guarantee;

    if (!empty($item->Logistics->Weight)) {
      $data['proweight'] = (double)$item->Logistics->Weight;
    }

    //maximální rozměr
    $data['prowidth'] = 0;
    if (!empty($item->Logistics->Width)) {
      $data['prowidth'] = (double)$item->Logistics->Width;
    }
    if (!empty($item->Logistics->Length) && $data['prowidth'] < (double)$item->Logistics->Length) {
      $data['prowidth'] = (double)$item->Logistics->Length;
    }
    if (!empty($item->Logistics->Height) && $data['prowidth'] < (double)$item->Logistics->Height) {
      $data['prowidth'] = (double)$item->Logistics->Height;
    }

    //zjistím jestli tam není odkaz na youtube video
    foreach ($item->DocumentsPackage->Document as $url) {
      $url = (string)$url;
      If (str_contains($url, 'youtube')) {
        $arr = explode("/", trim($url, "/"));
        $videoId = $arr[count($arr) - 1];
        $data['provideo'] = $videoId;
      }
    }

    //předžvýkám ostatní data
    $data["other"] = [];

    $index = 0;
    $data["other"]["images"] = [];
    if (!empty($item->Images)) {
      foreach ($item->Images->Image as $url) {
        if (!empty($url)) {
          if ($index > 9) {
            break;
          }
          $data["other"]["images"][] = (string)$url;
          $index++;
        }
      }
    }

    //předžvýkám seznam dokumentů k aktualizaci
    $data["other"]["files"] = [];
    if (!empty($item->DocumentsPackage->Manual)) {
      $data["other"]["files"][] = [
        "name" => "Manuál",
        "code" => "manual",
        "url" => (string)$item->DocumentsPackage->Manual,
      ];
    }

    if (!empty($item->DocumentsPackage->Document)) {
      foreach ($item->DocumentsPackage->Document as $url) {
        $url = (string)$url;
        if (str_contains($url, $data["procodeven"])) {
          $data["other"]["files"][] = [
            "name" => "Dokument",
            "code" => "dokument",
            'url' => $url,
          ];
        }
      }
    }

    //předžvýkám parametry
    $data["other"]["parameters"] = [];
    if (!empty($item->Properties)) {
      foreach ($item->Properties->Property as $property) {
        $atributes = $property->attributes();
        $data["other"]["parameters"][] = [
          "name" => (string)$atributes["name"],
          "value" => (string)$atributes["value"],
        ];
      }
    }

    return $data;
  }

  private function getAccess($item): int {
      //vypnutí skladu
      if ($this->vendor->storeOff) {
        return 101;
      }

      $qtyOnStock = $this->getQty($item);

      if ($qtyOnStock > 0) {
        return $this->config["proAccessOnStock"];
      } else {
        //pokud je datum dodání,počítám dny
        $dateStr = (string)$item->AvailDate;
        if (!empty($dateStr)) {
          list($day, $month, $year) = explode(".", $dateStr);
          $now = time(); // or your date as well
          $delDate = strtotime("$year-$month-$day");
          $dateDiff = $delDate - $now;
          $days = (int)round($dateDiff / (60 * 60 * 24)) + (int)$this->config["proAccessOnStock"];
          if ($days <= 3) {
            return 3;
          } else if ($days <= 7) {
            return 7;
          } else if ($days <= 14) {
            return 14;
          } else if ($days <= 30) {
            return 30;
          } else if ($days <= 99) {
            return 99;
          } else if ($days <= 100) {
            return 100;
          } else if ($days >= 101) {
            return 101;
          }
        }
      }


      return 101;
    }

    private function getQty($item): int {
      //vypnutí skladu
      if ($this->vendor->storeOff) {
        return 0;
      }

      return (int)$item->Amount;
    }

    private function calculateItemRecFee($item): ?float {
      $vat = (string)$item->Vat;
      $vat = (int)str_replace("%", "", $vat);
      $fee = (double)$item['rema'];
      if ($fee > 0) {
        return round($fee * (1 + ($vat/100)), 2);
      } else {
        return NULL;
      }
    }

}