<?php
umask(0);
$params = array();
$routeSecured = TRUE;
if ($_SERVER["SERVER_NAME"] == '127.0.0.1' || $_SERVER["SERVER_NAME"] == 'localhost' || $_SERVER["SERVER_NAME"] == 'hracky.test' || $_SERVER["SERVER_NAME"] == 'shopcom.test' || $_SERVER["SERVER_NAME"] == 'sccom.test' || $_SERVER["SERVER_NAME"] == 'web.sccom.orb.local') {
    define('IS_LOCALHOST', TRUE);
} else {
    define('IS_LOCALHOST', FALSE);
}

define('WWW_DIR', __DIR__. '/../www');
define('LIBS_DIR', __DIR__. '/../libs');

define('VAT_CHANGED_FROM', '2019-12-05');

// musi byt v nazvu URL eshopu bez www
define('SERVER_NAME_WEB1', 'hrackycom.cz');
define('SERVER_NAME_WEB2', 'shopcom.cz');
define('SERVER_NAME_WEB3', 'sccom.cz');

switch ($_SERVER["SERVER_NAME"]) {
    case 'hrackycomcz.ekramek.cz':
    case 'www.hrackycom.cz':
    case 'hracky.test':

        if (strpos($_SERVER["REQUEST_URI"], '/administrace') === FALSE) {
            header("HTTP/1.1 301 Moved Permanently");
            header("Location: https://www.shopcom.cz/hracky-k5270");
            exit();
        }

        define('SERVER_ID', 'web1');
        define('TABLE_SUFIX', '_'.SERVER_ID);
        define('SERVER_NAME', SERVER_NAME_WEB1);
        define('SERVER_STORES', '1');  //!!! aktualizuj i funkci getEnumServerIdStore() v modelu
        define('SERVER_STORES_QTYFREE', '1');
        define('SERVER_DELIVERY_EXCLUDES', '40,64,69');
        define('SERVER_DELIVERY_EXCLUDE_DELFREE', '9,92');
        define('SERVER_PAYMENT_EXCLUDES', '55,56,57,58,59,60,61,62,63');
        define('SERVER_PRODUCTS_EXCLUDES', 'proaccess_web1=0');
        define('SERVER_CATALOG_EXCLUDES', '');
        define('SERVER_ORDERSFILL', 'auto'); //manual/auto
        $routeSecured = ($_SERVER["SERVER_NAME"] == 'www.hrackycom.cz');
        break;

    case 'shopcom.test':
    case 'shopcomcz.ekramek.cz':
    case 'www.prislusenstvicom.cz':
    case 'www.shopcom.cz':
    case 'shopcom.cz':
    case 'www.shopcom.fun':
    case 'shopcom.fun':
    case 'localhost':
    case 'sccom.qwerin.cz':

    case 'www.sccomcz31.localhost':
    case '************':
    case '127.0.0.1':
    case 'web.sccom.orb.local':

        define('SERVER_ID', 'web2');
        define('TABLE_SUFIX', '_'.SERVER_ID);
        define('SERVER_NAME', SERVER_NAME_WEB2);
        define('SERVER_STORES', '2,3,4,5');  //!!! aktualizuj i funkci getEnumServerIdStore() v modelu
        define('SERVER_STORES_QTYFREE', '2,3,4,5');
        define('SERVER_DELIVERY_EXCLUDES', '40,64,69,158');
        define('SERVER_DELIVERY_EXCLUDE_DELFREE', '9,92');
        define('SERVER_PAYMENT_EXCLUDES', '');
        define('SERVER_PRODUCTS_EXCLUDES', '');
        define('SERVER_CATALOG_EXCLUDES', '');
        define('SERVER_ORDERSFILL', 'auto'); //manual/auto;
        $routeSecured = ($_SERVER["SERVER_NAME"] == 'www.shopcom.cz');
        break;

    case 'sccom.test':
    case 'sccomcz.ekramek.cz':
    case 'www.sccom.cz':
    case 'sccom.cz':
    case 'www.sccom.fun':
    case 'sccom.fun':

        define('SERVER_ID', 'web3');
        define('TABLE_SUFIX', '_'.SERVER_ID);
        define('SERVER_NAME', SERVER_NAME_WEB3);
        define('SERVER_STORES', '2,3,4,5'); //!!! aktualizuj i funkci getEnumServerIdStore() v modelu
        define('SERVER_STORES_QTYFREE', '2,3,4,5');
        define('SERVER_DELIVERY_EXCLUDES', '1,2,3,4,7,10,6,121,126');
        define('SERVER_DELIVERY_EXCLUDE_DELFREE', '9,92');
        define('SERVER_PAYMENT_EXCLUDES', '56,58,63,103,108,115');
        define('SERVER_PRODUCTS_EXCLUDES', "");
        define('SERVER_CATALOG_EXCLUDES', '13212');
        define('SERVER_ORDERSFILL', 'auto'); //manual/auto
        $routeSecured = ($_SERVER["SERVER_NAME"] == 'www.sccom.cz');
        break;
    default:
        define('SERVER_ID', 'web3');
        define('TABLE_SUFIX', '_'.SERVER_ID);
        define('SERVER_NAME', SERVER_NAME_WEB3);
        define('SERVER_STORES', '2,3,4,5'); //!!! aktualizuj i funkci getEnumServerIdStore() v modelu
        define('SERVER_STORES_QTYFREE', '2,3,4,5');
        define('SERVER_DELIVERY_EXCLUDES', '1,2,3,4,7,10,6,121,126');
        define('SERVER_DELIVERY_EXCLUDE_DELFREE', '9,92');
        define('SERVER_PAYMENT_EXCLUDES', '56,58,63,103,108,115');
        define('SERVER_PRODUCTS_EXCLUDES', "");
        define('SERVER_CATALOG_EXCLUDES', '13212');
        define('SERVER_ORDERSFILL', 'auto'); //manual/auto
        $routeSecured = ($_SERVER["SERVER_NAME"] == 'www.sccom.cz');
}

define('TABLENAME_CONFIG', 'config'.TABLE_SUFIX);
define('TABLENAME_USERS', 'users'.TABLE_SUFIX);
define('TABLENAME_USERS_LOG', 'users_log'.TABLE_SUFIX);
define('TABLENAME_PAGES', 'pages'.TABLE_SUFIX);
define('TABLENAME_ORDERS', 'orders'.TABLE_SUFIX);
define('TABLENAME_ORDSERVICES', 'ordservices'.TABLE_SUFIX);
define('TABLENAME_ORDERS_LOG', 'orders_log'.TABLE_SUFIX);
define('TABLENAME_ORDERS_SYSLOG', 'orders_syslog'.TABLE_SUFIX);
define('TABLENAME_ORDITEMS', 'orditems'.TABLE_SUFIX);
define('TABLENAME_ORDATTACHMENTS', 'ordattachments'.TABLE_SUFIX);
define('TABLENAME_CREDITNOTES', 'creditnotes'.TABLE_SUFIX);
define('TABLENAME_CREDITNOTES_LOG', 'creditnotes_log'.TABLE_SUFIX);
define('TABLENAME_CRNITEMS', 'crnitems'.TABLE_SUFIX);
define('TABLENAME_CASHDESKS', 'cashdesks'.TABLE_SUFIX);
define('TABLENAME_DISCOUNTS', 'discounts'.TABLE_SUFIX);
define('TABLENAME_MENUINDEXS', 'menuindexs'.TABLE_SUFIX);
define('TABLENAME_MENUS', 'menus'.TABLE_SUFIX);
define('TABLENAME_NEWS', 'news'.TABLE_SUFIX);
define('TABLENAME_PRODUCTS_SALESTAT', 'products_salestat'.TABLE_SUFIX);
define('TABLENAME_PRODUCTS_STAT', 'products_stat'.TABLE_SUFIX);
define('TABLENAME_WATCHDOGS', 'watchdogs'.TABLE_SUFIX);
define('TABLENAME_REVIEWSHEUREKA', 'reviewsheureka'.TABLE_SUFIX);
define('TABLENAME_REVIEWSHEUREKASHOP', 'reviewsheurekashop'.TABLE_SUFIX);
define('TABLENAME_COUPONS', 'coupons'.TABLE_SUFIX);


if (SERVER_ID == 'web3') {
    define('TABLENAME_PRODUCTS', 'products_web2');
    define('TABLENAME_PROATACHMENTS', 'proattachments_web2');
    define('TABLENAME_PROPARAMS', 'proparams_web2');
    define('TABLENAME_MANUFACTURERS', 'manufacturers_web2');
    define('TABLENAME_CATALOGS', 'catalogs_web2');
    define('TABLENAME_CATPRICES', 'catprices_web2');
    define('TABLENAME_CATPLACES', 'catplaces_web2');
    define('TABLENAME_PRODUCTS_FIELDS', 'products_fields_web2');

    define('TABLENAME_IMPORT_PRODUCTS', 'import_products_web2');
    define('TABLENAME_IMPORT_PROATACHMENTS', 'import_proattachments_web2');
    define('TABLENAME_IMPORT_IMAGES', 'import_images_web2');
    define('TABLENAME_IMPORT_IMAGES_UPDATE', 'import_images_update_web2');
    define('TABLENAME_IMPORT_CATPLACES', 'import_catplaces_web2');
    define('TABLENAME_IMPORT_PROPARAMS', 'import_proparams_web2');
    define('TABLENAME_IMPORT_PRODUCTS_FIELDS', 'import_products_fields_web2');

} else {
    define('TABLENAME_PRODUCTS', 'products'.TABLE_SUFIX);
    define('TABLENAME_PROATACHMENTS', 'proattachments'.TABLE_SUFIX);
    define('TABLENAME_PROPARAMS', 'proparams'.TABLE_SUFIX);
    define('TABLENAME_MANUFACTURERS', 'manufacturers'.TABLE_SUFIX);
    define('TABLENAME_CATALOGS', 'catalogs'.TABLE_SUFIX);
    define('TABLENAME_CATPRICES', 'catprices'.TABLE_SUFIX);
    define('TABLENAME_CATPLACES', 'catplaces'.TABLE_SUFIX);
    define('TABLENAME_PRODUCTS_FIELDS', 'products_fields'.TABLE_SUFIX);

    define('TABLENAME_IMPORT_PRODUCTS', 'import_products'.TABLE_SUFIX);
    define('TABLENAME_IMPORT_PROATACHMENTS', 'import_proattachments'.TABLE_SUFIX);
    define('TABLENAME_IMPORT_IMAGES', 'import_images'.TABLE_SUFIX);
    define('TABLENAME_IMPORT_IMAGES_UPDATE', 'import_images_update'.TABLE_SUFIX);
    define('TABLENAME_IMPORT_CATPLACES', 'import_catplaces'.TABLE_SUFIX);
    define('TABLENAME_IMPORT_PROPARAMS', 'import_proparams'.TABLE_SUFIX);
    define('TABLENAME_IMPORT_PRODUCTS_FIELDS', 'import_products_fields'.TABLE_SUFIX);

}
$catalogNotLikeExcludes = "";
if (!empty(SERVER_CATALOG_EXCLUDES)) {
    $arr = explode(',', trim(SERVER_CATALOG_EXCLUDES, ','));

    $arrExcl = array();
    foreach ($arr as $id) {
        
         if($id=="13212") { 
         
         $arrExcl[] = "(catrootid <> '$id')";
    } else {
        $arrExcl[] = "(catpathids NOT LIKE '%|$id|%')";
      }
    }
    if (count($arrExcl) > 0) {
        $catalogNotLikeExcludes = implode(' AND ', $arrExcl);
    }
}
define('SERVER_CATALOG_NOTLIKE_EXCLUDES', $catalogNotLikeExcludes);

$params['tableSufix'] = TABLE_SUFIX;
$params['serverId'] = SERVER_ID;

define('TABLENAME_DELIVERYMODES', 'deliverymodes');

define('USE_STORE', TRUE);

define('TEMP_DIR', WWW_DIR . '/../temp'.TABLE_SUFIX);