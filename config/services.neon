services:
	- App\Router\RouterFactory::createRouter
	- App\Models\ParamService(%parameters%)
	- App\Models\EntityManager
	- App\Classes\UserAuthenticator
	- App\Classes\UserAuthorizator::create
	- App\Services\ExampleService(%example.apiKey%)

extensions:
	dibi: Dibi\Bridges\Nette\DibiExtension22
	nextras.orm: Nextras\Orm\Bridges\NetteDI\OrmExtension
	dbal: Nextras\Dbal\Bridges\NetteDI\DbalExtension
	gpwebpay: Pixidos\GPWebPay\DI\GPWebPayExtension

nextras.orm:
    model: App\Orm\Orm