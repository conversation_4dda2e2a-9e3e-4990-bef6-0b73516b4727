<?php

namespace Orm;

use App\Orm\FioTransaction;
use App\Orm\Product;
use Tester\Assert;
use Tester\TestCase;

require __DIR__ . '/../boostrap.php';

class OrderTest extends TestCase
{
    use \DatabaseSetup;
    public function testMakeFio()
    {
        $order=$this->orm->order->getById(10000);
        $fio=new FioTransaction();
        $fio->ip="*******";
        $fio->order=$order;
        $fio->srcode=1;
        $this->orm->persistAndFlush($fio);

        $this->orm->clear();
        $order=$this->orm->order->getById(10000);
        foreach ($order->fioTransactions as $fioTransaction){
            Assert::equal($fioTransaction->ip,"*******");
            $this->orm->removeAndFlush($fioTransaction);
        }
        Assert::equal($order->fioTransactions->countStored(),0);

    }

}

(new OrderTest())->run();