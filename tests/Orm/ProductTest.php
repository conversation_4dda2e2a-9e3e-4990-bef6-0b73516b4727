<?php

namespace cases\model;

use App\Orm\Orm;
use App\Orm\Product;
use Tester\Assert;
use Tester\TestCase;

require __DIR__ . '/../boostrap.php';

class ProductTest extends TestCase
{
    use \DatabaseSetup;

    public function testGetPrice()
    {
        Assert::match('a', 'a');
        $product=$this->orm->product->getById(1);
        $web=$this->orm->web->getById(2);
        $product->fixPriceWeb2=false;

       $product->setPrice(50,true,Product::PRICE_A, $web);
       Assert::equal(50.0,$product->getPrice(true,$web,Product::PRICE_A));

        $product->setPrice(51,true,Product::PRICE_B, $web);
        Assert::equal(51.0,$product->getPrice(true,$web,Product::PRICE_B));

        $product->setPrice(52,true,Product::PRICE_C, $web);
        Assert::equal(52.0,$product->getPrice(true,$web,Product::PRICE_C));

        $product->setPrice(53,true,Product::PRICE_D, $web);
        Assert::equal(53.0,$product->getPrice(true,$web,Product::PRICE_D));

        $product->setPrice(53,true,Product::PRICE_E, $web);
        Assert::equal(53.0,$product->getPrice(true,$web,Product::PRICE_E));

        $product->setPrice(54,true,Product::PRICE_COM, $web);
        Assert::equal(54.0,$product->getPrice(true,$web,Product::PRICE_COM));
        $this->orm->persist($product);
    }

    public function testFixPrice(){
        $product=$this->orm->product->getById(1);
        $web=$this->orm->web->getById(2);
        $product->setPrice(50,true,Product::PRICE_A, $web);
        $product->fixPriceWeb2=true;
        $product->setPrice(55,true,Product::PRICE_A, $web);
        Assert::equal(50.0,$product->getPrice(true,$web,Product::PRICE_A));
    }

    public function testVats(){
        $product=new Product();
        $web=$this->orm->web->getById(2);
        $product->fixPriceWeb2=false;
        $product->vatType=$product::VAT_BASE;
        $product->setPrice(100,false,Product::PRICE_A, $web);
        Assert::equal(121.0,$product->getPrice(true,$web,Product::PRICE_A));
        Assert::equal(100.0,$product->getPrice(false,$web,Product::PRICE_A));

        $product->vatType=$product::VAT_LOW;
        $product->setPrice(100,false,Product::PRICE_A, $web);
        Assert::equal(115.0,$product->getPrice(true,$web,Product::PRICE_A));
        Assert::equal(100.0,$product->getPrice(false,$web,Product::PRICE_A));
    }

    public function testSetPrice()
    {
        Assert::match('a', 'a');
    }

    public function testMargin(){
        $web2=$this->orm->web->getById(2);
        $web3=$this->orm->web->getById(3);

        $product=$this->orm->product->getById(263943);
        $priceAWeb2=$product->priceAWeb2;
        $priceAWeb3=$product->priceAWeb3;
        $product->setPrice(1,false,Product::PRICE_A, $web2);
        $product->setPrice(1,false,Product::PRICE_A, $web3);
        $product->needRecalculatePrice=true;
        $product->recalculatePrice();
        Assert::equal($priceAWeb2,$product->getPrice(true,$web2,Product::PRICE_A));
        Assert::equal($priceAWeb3,$product->getPrice(true,$web3,Product::PRICE_A));
    }

}

(new ProductTest())->run();
