{block #content}
 <script type="text/javascript">
  $(function() {
    $("#tabs").tabs();
  });

 $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'procode');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>


 <h3>{block #title}Doobjednat{/block}</h3>

 {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label code /}: {input code 'id'=>'procode', size=>20}
  {label name /}: {input name class=>'autocomplete', 'id'=>'proname', size=>60}
  {label venid /}: {input venid}
  {input search}
  {input clear}
  </fieldset>
  {/form}


 <div id="tabs">
   <ul>
      <li><a href="#tab_remaind">Chybí/podstav ({count($dataRowsRem)} položek)</a></li>
      <li><a href="#tab_notfilled">Objednáno a není skladem ({count($dataRows)} položek)</a></li>
      <li><a href="#tab_wasisnot">Bylo a není ({count($wasIsNotRows)} položek)</a></li>
   </ul>
 
   <div id="tab_remaind">
   <table class="grid">
      <tr>
        <th></th>
        <th></th>
        <th colspan="{if $serverId == "web1"}2{else}3{/if}" style="text-align: center;">Chybí</th>
        <th></th>
      </tr><tr>
        <th>Kód</th>
        <th>Název</th>
        {if $serverId == "web1"}
        <th>{$serverNames["web1"]}</th>
        {else}
        <th>{$serverNames["web2"]}</th>
        <th>{$serverNames["web3"]}</th>
        {/if}
        <th>Celkem</th>
        <th></th>
      </tr>
      {foreach $dataRowsRem as $row}
      <tr>
        <td><a href="{plink Product:edit, $row->proid}">{$row->procode}</a></td>
        <td>{$row->proname}</td>
        {if $serverId == "web1"}
        <td>{$row->qtyremweb1}</td>
        {else}
        <td>{$row->qtyremweb2}</td>
        <td>{$row->qtyremweb3}</td>
        {/if}
        <td>{$row->qtyrem}</td>
        <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat zboží"/></a></td>
      </tr>
      {/foreach}
   </table>
   </div> 
 
 
   <div id="tab_notfilled">
   <table class="grid">
      <tr>
        <th></th>
        <th></th>
        <th colspan="{if $serverId == "web1"}2{else}3{/if}" style="text-align: center;">Chybí</th>
        <th></th>
      </tr><tr>
        <th>Kód</th>
        <th>Název</th>
        {if $serverId == "web1"}
        <th>{$serverNames["web1"]}</th>
        {else}
        <th>{$serverNames["web2"]}</th>
        <th>{$serverNames["web3"]}</th>
        {/if}
        <th>Celkem</th>
        <th></th>
      </tr>
      {foreach $dataRows as $row}
      <tr>
        <td><a href="{plink Product:edit, $row->proid}">{$row->procode}</a></td>
        <td>{$row->proname}</td>
        {if $serverId == "web1"}
        <td>{$row->oriqtyremweb1}</td>
        {else}
        <td>{$row->oriqtyremweb2}</td>
        <td>{$row->oriqtyremweb3}</td>
        {/if}
        <td>{$row->oriqtyrem}</td>
        <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat zboží"/></a></td>
      </tr>
      {/foreach}
   </table>
   </div> 
   
   <div id="tab_wasisnot">
   <table class="grid">
      <tr>
        <th>Kód</th>
        <th>Název</th>
        <th colspan="2"> </th>
      </tr>
      {foreach $wasIsNotRows as $row}
      <tr>
        <td><a href="{plink Product:edit, $row->proid}">{$row->procode}</a></td>
        <td>{$row->proname}</td>
        <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat zboží"/></a></td>
        <td><a href="{plink deleteStoreAbsent, $row->proid}"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" alt="vymazat záznam"/></a></td>
      </tr>
      {/foreach}
   </table>
   </div>
   
 </div>
  
{/block}