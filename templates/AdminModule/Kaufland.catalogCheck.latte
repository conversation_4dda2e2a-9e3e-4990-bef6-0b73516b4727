{default $pageTitle       = 'Kaufland'}
{varType App\Orm\Product[] $ormProducts}
{varType App\Orm\KauflandCatalog $kauflandCatalog}

{block #content}
<main role="main">

  <section class="section">

  {if isset($ormProducts)}
   <div class="row">
    <div class="col-md-12 mt-3">
      <div class="card ">
        <div class="card-body table-responsive p-0">
          <h3 class="m-2">Problematické položky pro {$kauflandCatalog->catalog->catname}</h3>
          <p>{$kauflandCatalog->catalog->catpath} <br> Kaufland: {$kauflandCatalog->name}</p>

          <table class="table">
          <tr>
            <th>Kód</th>
            <th>Název</th>
            <th></th>
            <th></th>
            <th></th>
          </tr>
        {foreach $ormProducts as $row}
          {ifset $errors[$row->id]}
          <tr>
            <td>{$row->code}</td>
            <td>{$row->name}</td>
            <td>
              {foreach $errors[$row->id] as $error}
                {$error["text"]}{if !empty($error["explanation"])}<i><br>{$error["explanation"]}</i>{/if}
                {if $error["isParam"] == 1}
                  <br>
                  [ <a href="{plink Param:edit, $kauflandCatalog->catalog->id}" target="kaufland">přidat do kategorie</a> ]
                  [ <a href="{plink Product:edit, $row->id}#tabs_param" target="kaufland">upravit položku</a> ]
                  <hr>
                {/if}
                <br>
              {/foreach}
            </td>
          </tr>
          {/ifset}

        {/foreach}
        </table>
        </div>
      </div>
      <p>Pokud je parametr typu "Picture" nahrajte přílohu o požadovaném obsahu jako přílohu u produktu a název přílohy uveďte název parametru.</p>
    </div>
  </div>
  {/if}

  </section>

</main>