
{block #content}
  <h3>{block #title}Import / export {$serverNames[$serverId]}{/block}</h3>

  {ifset $log}
  {foreach $log as $row}
    {$row}<br>
  {/foreach}
  {/ifset}

  <h3>Párování plateb Sberbank</h3>
  {control pairSberbankForm}

  <h3>Export emailů zákazníků</h3>
  {control exportEmailForm} 
  
  <h3>Import {$serverNames[$serverId]}</h3>
  
  <p>{$imagesCnt} obrázků připravených pro import. {if $imagesCnt > 0}<a href="{plink imagesImport}">Spustit import obrázků</a>.{/if}</p>
  
  {control importXmlForm} 
  
  <h3>Export zboží {$serverNames[$serverId]}</h3>
  {control exportForm}
  {if !empty($xmlExportFile)}
  <p>Poslední vygenerovaný XML soubor z data {$xmlExportFileDate} stáhnete <a href="{$baseUrl}/{$xmlExportFile}">zde</a> (kliknout pravým myšítkem na zde a Uložit odkaz jako ... ) </p>
  {/if}
  
  <h3>Aktualizace vybraných polí {$serverNames[$serverId]}</h3>
  {control updateFieldsForm} 
  {*
  <h3>Oprava zařazení do katalogu podle kategorií {$serverNames[$serverId]}</h3>
  {control updateCatalogForm} 
  *}
  <h3>Exporty do katalogů zboží (XML feed)</h3>
  <p>
  <strong>Zbozi.cz: </strong>{plink //:Front:Export:products, 'id'=>'zbozicz'}<br />
  <strong>Heureka.cz: </strong>{plink //:Front:Export:products, 'id'=>'heurekacz'}<br />
  <strong>Heureka.cz - dostupnostní feed: </strong>{plink //:Front:Export:products, 'id'=>'heurekacz-acc'}<br />
  <strong>Google.com CZK: </strong>{plink //:Front:Export:products, 'id'=>'googlecom'} <br />
  <strong>Google.com EUR: </strong>{plink //:Front:Export:products, 'id'=>'googlecomeur'} <br />
  <strong>AFFILLIATE: </strong>{plink //:Front:Export:products, 'id'=>'affiliate'} <br />
  <br />
  <strong>Sitemapa: </strong>{plink //:Front:Export:sitemap}<br />
  <br />
  <strong>Noční dávka: </strong>{plink //:Front:Batch:nightBatch 'k'=>'942a13a8cb5840'}
  </p>
  
  
  <h3>Aktualizace odběrných míst</h3>
  <p>
  <strong>Zásilkovna: </strong><a href="{plink //:Front:Batch:UpdateZasilkovnaBranches}">zde</a><br />
  <strong>Balíkovna: </strong><a href="{plink //:Front:Batch:UpdateBalikovnaBranches}">zde</a><br />
  <strong>PPL: </strong><a href="{plink //:Front:Batch:UpdatePplBranches}">zde</a><br />
  </p>
{/block}