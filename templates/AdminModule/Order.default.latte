{block #content}
  <h3>{block #title}Seznam Objednávek{/block}</h3>
  <p><a href="{$baseUrl}/batch/pair-payments/?k=942a13a8cb5840">Spustit párování plateb</a></p>
  {form searchForm}
  <fieldset>
  <legend><PERSON>y<PERSON>edávání</legend>
  {label id /} {input id}
  {label code /} {input code}
  {label invcode /} {input invcode}
  {label name /} {input name}
  {label mail /} {input mail}
  {label admin /} {input admin}
  {label malid /} {input malid}
  <br />
  {label ismall /} {input ismall}
  {label isheureka /} {input isheureka}
  {label iskaufland /} {input iskaufland} |
  {label status /} {input status}
  {label datestatus /} {input datestatus}
  {label imei /} {input imei}
  {label procode /} {input procode}
  {label provenid /} {input provenid}
  {label paynotfin /} {input paynotfin}
  <br />
  {input datefield}
  {label datefrom /} {input datefrom}
  {label dateto /} {input dateto}
  {label delid /} {input delid}
  {label paycode /} {input paycode}
  {input notclosed} {label notclosed /}
  {input onstock_ours} {label onstock_ours /}
  <br />
  {label referer /} {input referer}
  {input heureka} {label heureka /}
  {input isac} {label isac /}
  {input isinsurance} {label isinsurance /}
  {input notfilled} {label notfilled /}
  {input auttrans} {label auttrans /}
  {input underpurchase} {label underpurchase /}
  {input pagerows size=>2} {label pagerows /}
  {label orderby /} {input orderby} {input orderbytype}
  {input search}
  {input clear}
  </fieldset>
  {/form}
  {include @ordersList.latte}
  {control paginator}

  {*import česká pošta*}
  <h4>Import balíků ČP do eshopu</h4>
  <p>soubor musí být ve fromátu CSV (oddělovač středník)</p>
  {control postImportParcelsForm}

{/block}
