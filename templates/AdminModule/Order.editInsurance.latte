
{block #content}
<h3>{block #title}Objednávka č. {$dataRow->ordcode} ({$dataRow->ordiname} {$dataRow->ordilname} | {$dataRow->ordstname} {$dataRow->ordstlname}) - editace pojištění {/block}</h3>
{form orderInsuranceForm}
{php $form->render('errors') }
    <table class="grid">
    {foreach $ordItems as $row}
    <thead>
        <tr>
            <th class="name">{_'Název'}</th>
            <th>{_'Kusy'}</th>
            <th>{_'Cena/kus'}</th>
            <th>{_'Cena celkem'}</th>
        </tr>
    </thead>

    <tr style="font-weight: bold">
        <td>{$row->proname}</td>
        <td>{$row->oriqty}</td>
        <td style="text-align: right">{$row->oriprice|formatPriceAdmin}</td>
        <td style="text-align: right">{($row->oriprice*$row->oriqty)|formatPriceAdmin}</td>
    </tr>
    {if isset($ordInsItems[$row->oriid])}
    {foreach $ordInsItems[$row->oriid] as $irow}
        <tr>
            <td>{$irow->oriname}</td>
            <td>{$irow->oriqty}</td>
            <td style="text-align: right">{$irow->oriprice|formatPriceAdmin}</td>
            <td style="text-align: right">{($irow->oriprice*$irow->oriqty)|formatPriceAdmin}</td>
        </tr>
    {/foreach}
    {/if}
    {ifset $form[$row->oriid]}
    <tr valign="top">
        <td colspan="6">
            <strong>Upravit nastavení pojištění:</strong>
            <table>
                <tr valign="top">
                {foreach $form[$row->oriid]->getComponents() as $cont}
                {if $cont->name !== "orisn"}
                <td style="border: hidden">
                <div>
                    {php echo $form[$row->oriid][$cont->name]["inpid"]->getControl() }
                </div>
                </td>
                {/if}
                {/foreach}
                </tr>
            </table>
            Sériové čísla: {php echo $form[$row->oriid]["orisn"]->getControl()->size(100) }<br>Pokud více kusů, oddělte jednotlivé SN čárkou
        </td>
    </tr>
    {/ifset}
    {/foreach}
    </table>
    {if
        empty($dataRow->ordiname) ||
        empty($dataRow->ordilname) ||
        empty($dataRow->ordistreet) ||
        empty($dataRow->ordistreetno) ||
        empty($dataRow->ordicity) ||
        empty($dataRow->ordipostcode) ||
        empty($dataRow->ordtel) ||
        empty($dataRow->ordmail)
    }
    <p class="flash err">Nejsou vyplněny všechny kontaktní údaje zákazníka. Bez těchto údajů nepůjde vytvořit smlouva u pojišťovny.</p>
    {/if}

    {if empty($dataRow->orditype) || empty($dataRow->ordibirthdate)}
    <p class="flash err">Není vyplněno pohlaví zákazníka a datum narození. Bez těchto údajů nepůjde vytvořit smlouva u pojišťovny.</p>
    {/if}

    {if empty($dataRow->ordinvcode)}
    <p class="flash err">Není vytvořena faktura.</p>
    {/if}

    {if !empty($dataRow->ordinscode)}
    <p class="flash err">POZOR! K objednávce je již vygenerované pojištění s těmito kódy: {$dataRow->ordinscode}. Negenerujte další pojištění!</p>
    {/if}

    {php echo $form['createContract']->getControl() }{label createContract /}<br>
    {php echo $form['save']->getControl() } &lt; <a href="{plink edit $dataRow->ordid}">zpět na detail bez úprav</a>
{/form}
{/block}