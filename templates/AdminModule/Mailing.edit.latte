{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'proid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

  <h3>{block #title} Editace mailování {/block}</h3>
  {if $id>0}<a href="{plink :Front:Mailing:detail, $id, 1, 'b929b889afd416e8ae24532402f8d159'}">Náhled</a>{/if}
  {form editForm}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
  </ul>
  <table>
  <tr class="required"><th>{label mamtarget /}</th><td>{input mamtarget}</td></tr>
  <tr class="required"><th>{label mamsubject /}</th><td>{input mamsubject}</td></tr>
  <tr class="required"><th>{label mambody /}</th><td>{input mambody}</td></tr>
  <tr><th>{label mamdate /}</th><td>{input mamdate} <small>Zadejte ve formátu dd.mm.rrrr</small></td></tr>
  {*<tr><th>{label mamcouponvalue /}</th><td>{input mamcouponvalue}</td></tr>*}

  <tr><th>{label mammanid /}</th><td>{input mammanid}</td></tr>
  <tr><th>{label mamvenid /}</th><td>{input mamvenid}</td></tr>

  <tr><th colspan="2">
  <p>Produkty:</p>
  <table class="grid">
  {for $i=1;$i<=12;$i++}
    {var $color = "white";}

    <tr style="background: {$color};"><td>{$i}.</td> <td>Id: {=$form["products"][$i]['proid']->control->readonly('readonly')->size(5) }</td><td> Název: {=$form["products"][$i]['proname']->control->size(60) } </td></tr>
  {/for}
  </table>
  </th></tr>
  <tr><th>{label mamstatus /}</th><td>{input mamstatus}</td></tr>

  </table>
  {input save}

  {/form}

  {if $id>0}
  <h3>Testovací mailování</h3>
  {control mailTestForm}
  {/if}
{/block}
