{block #content}

{if $setProvozId}
<p> P<PERSON><PERSON><PERSON>ut provozovnu:
{foreach $enum_provozid as $pId => $pName}
<a href="{plink this, "sProvozId"=> $pId}">{$pName}</a> |
{/foreach}
</p>
{/if}

{ifset $provozId}
 <h3>{block #title}Pokladna {$enum_provozid[$provozId]} {$date|date:'d.m.Y'}{/block}</h3>
 <p><a href="{plink edit 0}">Nová operace</a> {form printForm} Tisk denního reportu: {input provozid} {input date} {input submit} {/form}</p>


      <table>
        <tr>
          <th>Hodnota pokladny: </th><td style="text-align: right">{$cashDeskSum|formatPrice}</td>
        </tr>
        <tr>
          <th>Platby kartou: </th><td style="text-align: right">{$byCardSum|formatPrice}</td>
        </tr>
      </table>


 <table class="grid">

<h3>Hotovost - objednávky</h3>
<table class="grid">
  <tr>
    <th>Datum</th>
    <th>Částka</th>
    <th>Obchodník</th>
    <th style="font-size: 12px;border: 1px solid black">Objednávka/Dobropis</th>
  </tr>

{foreach $cashDeskItems as $row}
  <tr>
    <td>{$row->caddatec|date:'d.m.Y H:i:s'}</td>
    <td style="text-align: right">{$row->cadprice|formatPrice}</td>
    <td>{$enum_admid[$row->cadadmid]}</td>
    <td>{if $row->cadordid > 0}<a href="{plink Order:default, 'sCode' => $row->ordcode}" target="_blank">{$row->ordcode}</a>{elseif $row->cadcrnid > 0}<a href="{plink Order:default, 'sCode' => $row->crncode}" target="_blank">{$row->crncode}</a>{/if}</td>
  </tr>
{/foreach}

</table>

<h3>Platby kartou objednávky</h3>
<table class="grid">
  <tr>
    <th>Datum</th>
    <th>Částka</th>
    <th>Obchodník</th>
    <th>Objednávka</th>
  </tr>
  {foreach $byCardItems as $row}
    <tr>
      <td>{$row->orddatec|date:'d.m.Y H:i:s'}</td>
      <td style="text-align: right">{$row->ordpricevat|formatPrice}</td>
      <td>{$row->admname}</td>
      <td><a href="{plink Order:default, 'sCode' => $row->ordcode}" target="_blank">{$row->ordcode}</a></td>
    </tr>
  {/foreach}
</table>

{else}
<h3>Uživatel nemá nastavenou provozovnu</h3>
{/ifset}
{/block}