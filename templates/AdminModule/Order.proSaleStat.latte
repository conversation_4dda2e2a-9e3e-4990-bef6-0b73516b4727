

{block #content}
  <script type="text/javascript">
  var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatIdProductDefault.js"></script>

  <h3>{block #title} Statistika prodejnosti zboží za období{/block}</h3>

  {form proSaleStatSearchForm}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
  </ul>
  <fieldset>
  <legend>Vyhledávání</legend>
  {label status /} {input status}
  {label datestatus /} {input datestatus}
  {input datefield}
  {label datefrom /} {input datefrom}
  {label dateto /} {input dateto}
  <br />
  {*label orderby /} {input orderby} {input orderbytype*}
  {label provenid /} {input provenid}

  {label catid /}: {input catid size=>3}
  <a href="#" onclick="return clearAc('search');"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a>
  {input catname size=>40}
  {input search}
  {input clear}
  </fieldset>
  {/form}

  <table class="grid">
  <tr>
    <th>Katalogové č.</th>
    <th>Název</th>
    <th>Výrobce</th>
    <th>Dodavatel</th>
    <th>Skladem</th>
    <th>Průměrná cena</th>
    <th>Prodáno kusů</th>
    <th>Dost.</th>
    <th>Status</th>
    <th></th>
  </tr>
  {foreach $rows as $row}
  <tr>
    <td>{$row->procode}</td>
    <td>{$row->proname}</td>
    <td>{$row->manname}</td>
    <td>{$row->provenid}</td>
    <td>{$row->proqty}</td>
    <td>{$row->oripriceavg}</td>
    <td>{$row->itemscount}</td>
    <td>{$enum_proaccess[$row->proaccess]}</td>
    <td>{$enum_ordstatus[$row->ordstatus]}</td>
    <td><a href="{plink Product:edit, $row->proid}" target="_blank"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a></td>
  </tr>
  {/foreach}
  </table>
  {control paginator}
  <p>Celkem {count($rows)} záznamů</p>

  {if $filteIsEmpy}
  <p style="color: red; font-size: large">Vyberte ve filtru co chcete hledat. Výsledky se řadí podle počtu prodaných kusů sestupně.</p>
  {/if}
{/block}
