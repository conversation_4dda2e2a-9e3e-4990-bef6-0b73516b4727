{block #content}
 <h3>{block #title}Skladové pohyby {$product->proname} ({$product->procode}){/block}</h3>
 <table class="grid">
   {foreach $stoOpers as $row}
     {if $iterator->isFirst()}
     <tr>
     <th>Datum</th>
     <th>Dodavatel</th>
     <th>Sklad</th>
     <th>Počet</th>
     <th>Typ</th>
     <th></th>
     </tr>
     {/if}
     <tr>
     <td>{$row->stideldate|date:'d.m.Y'}</td>
     <td>{$row->venname}</td>
     <td>{$row->stoname}</td>
     <td>{$row->stiqty}</td>

     {if !empty($row->web1_oriordid)}
     <td>prodej {$serverNames["web1"]}</td>
     <td><a href="https://www.{$serverNames["web1"]}/{plink Order:edit, $row->web1_oriordid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat objednávku"/></a></td>
     {elseif !empty($row->web2_oriordid)}
     <td>prodej {$serverNames["web2"]}</td>
     <td><a href="https://www.{$serverNames["web2"]}/{plink Order:edit, $row->web2_oriordid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat objednávku"/></a></td>
     {elseif !empty($row->web3_oriordid)}
     <td>prodej {$serverNames["web3"]}</td>
     <td><a href="https://www.{$serverNames["web3"]}/{plink Order:edit, $row->web3_oriordid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat objednávku"/></a></td>
     {elseif !empty($row->stiwoff)}
     <td>odpis</td>
     <td>{$row->stinote}</td>
     {elseif !empty($row->stisopid)}
     <td>přesun</td>
     <td></td>
     {elseif !empty($row->stittiid)}
     <td>přesun</td>
     <td><a href="{plink StoreTransfer:edit, $row->ttisttid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat skladový přesun"/></a></td>
     {elseif !empty($row->sticopid)}
     <td>reklamace</td>
     <td><a href="{plink Complaint:edit, $row->sticopid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat reklamaci"/></a></td>
     {else}
     <td>dodávka</td>
     <td><a href="{plink Delivery:edit, $row->deidelid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat dodávku"/></a></td>
     {/if}

     </tr>
     {if $iterator->isLast()}
     {/if}
   {/foreach}
   </table>
  {control paginator} 
{/block}