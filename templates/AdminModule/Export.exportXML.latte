<?xml version="1.0"}
<?mso-application progid="Excel.Sheet"}
<ss:Workbook xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet">
        <ss:Styles>
                <ss:Style ss:ID="0">
                        <ss:Font ss:Bold="1"/>
                        <ss:Alignment ss:Horizontal="Center" ss:WrapText="0"/>
                        <ss:Interior ss:Color="#B8CCE4" ss:Pattern="Solid"/>
                </ss:Style>
                <ss:Style ss:ID="1">
                        <ss:Alignment ss:WrapText="0"/>
                        <ss:NumberFormat ss:Format="0"/>
                </ss:Style>
                <ss:Style ss:ID="2">
                        <ss:Alignment ss:WrapText="0"/>
                        <ss:NumberFormat ss:Format="Fixed"/>
                </ss:Style>
                <ss:Style ss:ID="3">
                        <ss:Alignment ss:WrapText="0"/>
                </ss:Style>
        </ss:Styles>
        <ss:Worksheet ss:Name="Sheet1">
                <ss:Table>
                        <ss:Row>
                                {foreach $colsdesc as $col}
                                <ss:Cell ss:StyleID="0"><ss:Data ss:Type="String">{$col['desc']}</ss:Data></ss:Cell>
                                {/foreach}
                        </ss:Row>
                        <ss:Row>
                                {foreach $cols as $col}
                                <ss:Cell ss:StyleID="0"><ss:Data ss:Type="String">{$col['name']}</ss:Data></ss:Cell>
                                {/foreach}
                        </ss:Row>
                        {foreach $data as $row}
                        <ss:Row>
                                {foreach $cols as $col}
                                <ss:Cell ss:StyleID="0"><ss:Data ss:Type="String">{$row[$col['name']]}</ss:Data></ss:Cell>
                                {/foreach}
                        </ss:Row>
                        {/foreach}
                </ss:Table>
        </ss:Worksheet>
</ss:Workbook>