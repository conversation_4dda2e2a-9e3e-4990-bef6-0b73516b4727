{block #content}
<script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'proid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

 <h3>{block #title}Dodávky{/block}</h3>
 <p>[ <a href="{plink edit}">přidat novou dodávku</a> ]</p>
 {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label venid /} {input venid}
  {label status /} {input status} {label proid /} {input proid} Název zboží:<input type="text" class="autocomplete" id="proname" />
  {input specmode} {label specmode /}
    <br />
  {input search}
  {input clear}
  </fieldset>
  {/form}
 <table class="grid">
  <tr>
    <th>ID</th>
    <th>Kód</th>
    <th>Nákupní fa</th>
    <th>Sklad</th>
    <th>Dodavatel</th>
    <th>Datum</th>
    <th>Status</th>
    <th colspan="2"></th>
  </tr>
  {foreach $dataRows as $row}

    {php
    $style = "";
    if ($row->delstatus == 0) {
      $style = "#FFFF80";
    } else if ($row->delstatus == 1) {
      $style = "#80FF80";
    } else if ($row->delstatus == 2) {
      $style = "#BBFF33";
    }
    }
    <tr {if !empty($style)} style="background-color: {$style|noescape}"{/if}>
      <td>{$row->delid}</td>
      <td>{$row->delvencode}</td>
      <td>{$row->delinvcode}</td>
      <td>{$enum_delstoid[$row->delstoid]}</td>
      <td>{$row->venname}</td>
      <td>{if !empty($row->deldate)}{$row->deldate|date:'d.m.Y'}{/if}</td>
      <td>{$enum_delstatus[$row->delstatus]}</td>
      <td><a href="{plink Delivery:edit, $row->delid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
      <td><a target="invoicePrint" href="{plink Delivery:printProtocol, $row->delid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a></td>
    </tr>
  {/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}