{block #content}
  <h3>{block #title} Editace výrobce {/block}</h3>
  <p>[ <a href="{plink Manufacturer:edit, 0}">přidat nového výrobce</a> ]</p> 
  {control editForm}

  <h3>Ceny dle katalogu a výrobce</h3>

  {form mcpAddForm}
    {label mcpcatid /} {input mcpcatid}
    <input type="text" id="catname" size="100">
    <a href="#" onclick="return clearAc();"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a><br>
    {label mcppricefrom /} {input mcppricefrom}
    {label mcppriceto /} {input mcppriceto}
    {label mcpmargin_web2 /} {input mcpmargin_web2}
    {label mcpmargin_web3 /} {input mcpmargin_web3}
    {input submit}
  {/form}

  {foreach $catalogPrices as $row}
    {if $iterator->first}
    <table class="grid">
      <tr>
        <th>Katalog</th>
        <th>Cena od</th>
        <th>Cena do</th>
        <th>Marže shopcom.cz</th>
        <th>Marže sccom.cz</th>
        <th></th>
      </tr>
    {/if}
      <tr>
        <td>{str_replace('|', ' > ', $row->catpath)}</td>
        <td>{$row->mcppricefrom|formatPrice}</td>
        <td>{$row->mcppriceto|formatPrice}</td>
        <td>{$row->mcpmargin_web2}%</td>
        <td>{$row->mcpmargin_web3}%</td>
        <td><a href="{plink deleteMcp $row->mcpid, $row->mcpmanid}"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" /></a> </td>
      </tr>
    {if $iterator->last}
    </table>
    {/if}
  {/foreach}
  <script type="text/javascript">
  var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatId.js"></script>
{/block}
