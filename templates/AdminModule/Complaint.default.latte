{block #content}
<script>
  function noteSH(id) {
     if ($('#'+id).is(':visible')) {
      $('#'+id).hide();
     } else {
       $('#'+id).show();
     }
     return false;
  }
  </script>


 <h3>{block #title}Reklamace{/block}</h3>
 <p>[ <a href="{plink searchSource}">přidat novou reklamaci - vyhledat nákup</a> ] [ <a href="{plink edit}">přidat novou reklamaci bez předplnění</a> ]</p>
 {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label code /} {input code}
  {label name /} {input name}
  {label imei /} {input imei}
  {label srcid /} {input srcid}
  {label typid /} {input typid}<br />
  {label serid /} {input serid}
  {label status /} {input status} {label datefrom /} {input datefrom} {label dateto /} {input dateto}
  {input notclosed} {label notclosed /}
  {input search}
  {input clear}
  </fieldset>
  {/form}

  <form method="get" action="{plink batch}">
  <input type="checkbox" id="checkAll"> <PERSON><PERSON><PERSON><PERSON>rtnout/odškrtnou vše
 <table class="grid">
  <tr>
    <th></th>
    <th>Zdroj</th>
    <th>Kód</th>
    <th>Produkt</th>
    <th>S/N</th>
    <th>Typ</th>
    <th></th>
    <th>Status</th>
    <th>Přijato</th>
    <th>Dnů od přijetí</th>
    <th>Servis</th>
    <th></th>
    <th>Jméno</th>
    <th></th>
    <th>Uzavřeno</th>
    <th></th>
    <th colspan="2">Protokol</th>
    <th colspan="2">PRO SERVIS</th>
  </tr>
  {php $sumRepCus = 0}
  {php $sumRepVal = 0}


  {php


    $cnt = 0;
  }

  {foreach $dataRows as $row}
    {php
      $style = "";
      $cnt++;
      if ($row->datediff > 25 && $row->copstatus != 9) {
        $style = "#FCA781";
      }

      if ($row->coptypid == 5) {
        $sumRepCus += $row->coppricerep;
        $sumRepVal += $row->coppriceval;
      }

    }
    <tr {if !empty($style)} style="background-color: {$style|noescape}"{/if}>
      <td><input class="ordid_chk" name="copid[]" type="checkbox" value="{$row->copid}"></td>
      <td>{$enum_copsrcid[$row->copsrcid]}</td>
      <td>{$row->copcode}</td>
      <td>{$row->copproname|truncate:100}</td>
      <td>{$row->copsn}{if !empty($row->copsnnew)}<br>{$row->copsnnew}{/if}</td>
      <td {if $row->coptypid == 5}style="background: #40b5ff"{/if}>{$enum_coptypid[$row->coptypid]}</td>
      <td>{if !empty($row->copdescrep)}<a href="#" onclick="return noteSH('note_{$row->copid}');"><img src="{$baseUrl}/img/admin/magnifier.png" width="16" height="16" /></a><div style="display:none" id="note_{$row->copid}">{$row->copdescrep|breakLines}</div>{/if}</td>
      <td>{$enum_copstatus[$row->copstatus]}</td>
      <td>{$row->copdateacc|date:'d.m.Y'}</td>
      <td>{if $row->dateclosediff > 0}{$row->dateclosediff}{else}{$row->datediff}{/if}</td>
      <td>{ifset $enum_venid[$row->copservisid]}{$enum_venid[$row->copservisid]}{/ifset}</td>
      <td>{if !empty($row->copparcodevendor)}
          <a href="{plink getUrlTrace, $row->copparcodevendor}">
          <img src="{$baseUrl}/img/admin/car.png" width="16" height="16" title="Už existuje balík - sledovat balík" />
          </a>
          {elseif !empty($row->copparcode2vendor)}
          <img src="{$baseUrl}/img/admin/bigsize.png" width="16" height="16" title="Už existuje balík - zjistěte stav" />
          {/if}
      </td>
      <td>{$row->copname} {$row->coplname}</td>
      <td>{if !empty($row->copparcodecustomer)}
          <a href="{plink getUrlTrace, $row->copparcodecustomer}">
          <img src="{$baseUrl}/img/admin/car.png" width="16" height="16" title="Už existuje balík - sledovat balík" />
          </a>
          {elseif !empty($row->copparcode2customer)}
          <img src="{$baseUrl}/img/admin/bigsize.png" width="16" height="16" title="Už existuje balík - zjistěte stav" />
          {/if}
      </td>
      <td>{if !empty($row->copdateclose)}{$row->copdateclose|date:'d.m.Y'}{/if}</td>
      <td><a href="{plink Complaint:edit, $row->copid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
      <td><a target="complaintPrint" href="{plink Complaint:print, $row->copid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a></td>
      <td><a href="{plink Complaint:print, $row->copid, 0, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a></td>
      <td><a target="complaintPrint" href="{plink Complaint:print, $row->copid, 1}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a></td>
      <td><a href="{plink Complaint:print, $row->copid, 1, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a></td>
    </tr>
  {/foreach}

   <tr>
    <td colspan="20"><strong>Celkem {$cnt} položek </strong>    </td>
  </tr>

  </table>
    <p style="font-weight: bold">Suma cena opravy zákazník:{$sumRepCus|formatPrice} | náklady: {$sumRepVal|formatPrice} </p>
  <input type="submit" name="submit_cp_ser" value="Odeslat Česká Pošta - servis">
  <input type="submit" name="check_post_ser" value="Kontrola stavu - servis">
  <input type="submit" name="print_post_ser" value="Tisk štítků - servis"><br>

  <input type="submit" name="submit_cp_usr" value="Odeslat Česká Pošta - zákazník">
  <input type="submit" name="check_post_usr" value="Kontrola stavu zákazník">
  <input type="submit" name="print_post_usr" value="Tisk štítků - zákazník"><br>

  </form>

  <script>
    $("#checkAll").click(function(){
      $('.ordid_chk').not(this).prop('checked', this.checked);
    });
  </script>