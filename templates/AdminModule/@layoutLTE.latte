{php
  if ($presenter->name != 'Admin:Product' && $presenter->action == 'edit') {
    $thisLink = $presenter->link('default');
  } else {
    $thisLink = $presenter->link('this');
  }
}

<!DOCTYPE html>

<html lang="cs">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{ifset title}{include title|stripHtml} | {/ifset} ADMINISTRACE {$presenter->config["SERVER_NAME"]}</title>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css" integrity="sha384-zCbKRCUGaJDkqS1kPbPd7TveP5iyJE0EjAuZQTgFLD2ylzuqKfdKlfG/eSrtxUkn" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">



   </head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">

    <nav class="main-header navbar navbar-expand navbar-white navbar-light ">
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="https://www.shopcom.cz{$thisLink}" class="nav-link">{$serverNames["web2"]}</a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="https://www.sccom.cz{$thisLink}" class="nav-link">{$serverNames["web3"]}</a>
            </li>
        </ul>

    </nav>


    <aside class="main-sidebar sidebar-dark-primary elevation-4">

        <a href="{$baseUrl}/administrace" class="brand-link">

            <span class="brand-text font-weight-light">WEB Admin</span>
        </a>

        <div class="sidebar">

            <div class="user-panel mt-3 pb-3 mb-3 d-flex">

                <div class="info">
                    <a href="#" class="d-block">{$admin->admname} ({$admin->admmail})</a>
                </div>
            </div>


            <nav class="mt-2">
                <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                    <li class="nav-item">
                        <a href="/administrace/" class="nav-link">
                            <i class="nav-icon fas fa-th"></i>
                            <p>
                                Stará administrace
                            </p>
                        </a>
                    </li>
                    <li n:class="$presenter->isLinkCurrent('CatalogNew:*') ? menu-open,nav-item">
                        <a href="#" n:class="$presenter->isLinkCurrent('CatalogNew:*') ? active,nav-link">
                            <i class="nav-icon far fa-folder-open"></i>
                            <p>
                                Katalog
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a  n:class="$presenter->isLinkCurrent() ? active,nav-link" n:href="CatalogNew:images">
                                    <i class="fas fa-images nav-icon"></i>
                                    <p>Obrázky</p>
                                </a>
                            </li>

                        </ul>
                    </li>
                    <li n:class="$presenter->isLinkCurrent('Param:*') ? menu-open,nav-item">
                        <a href="#" n:class="$presenter->isLinkCurrent('Param:*') ? active,nav-link">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <p>
                               Parametry
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a  n:class="$presenter->isLinkCurrent() ? active,nav-link" n:href="Param:edit">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Editace</p>
                                </a>
                            </li>

                        </ul>
                    </li>
                    <li class="nav-item">
                        <a n:href="Coupon:campaigns" n:class="$presenter->isLinkCurrent() ? active,nav-link">
                            <i class="nav-icon  fas fa-digital-tachograph"></i>
                            <p>
                                Slevové kupóny
                            </p>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a n:href="Coupon:banner" n:class="$presenter->isLinkCurrent() ? active,nav-link">
                            <i class="nav-icon fas  fa-chalkboard"></i>
                            <p>
                                Bannery
                            </p>
                        </a>
                    </li>
                    <li n:class="$presenter->isLinkCurrent('Ecomail:*') ? menu-open,nav-item">
                        <a n:href="Ecomail:default" n:class="$presenter->isLinkCurrent() ? active,nav-link">
                            <i class="nav-icon  fas fa-digital-tachograph"></i>
                            <p>
                                Ecomail
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a n:href="Ecomail:default"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Seznamy adresátů</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a n:href="Ecomail:addList"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Nový seznam adresátů</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a n:href="Ecomail:campaignList"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Kampaně</p>
                                </a>
                            </li>

                        </ul>
                    </li>
                    <li n:class="$presenter->isLinkCurrent('RecycleFee:*') ? menu-open,nav-item">
                        <a n:href="RecycleFee:default" n:class="$presenter->isLinkCurrent() ? active,nav-link">
                            <i class="nav-icon  fas fa-digital-tachograph"></i>
                            <p>
                                Recyklační poplatky
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a n:href="RecycleFee:default"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Seznam příspěvků</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a n:href="RecycleFee:edit 0"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Nový příspěvek</p>
                                </a>
                            </li>
                        </ul>
                    </li>


                    <li n:class="$presenter->isLinkCurrent('Kaufland:*') ? menu-open,nav-item">
                        <a n:href="Kaufland:default" n:class="$presenter->isLinkCurrent() ? active,nav-link">
                            <i class="nav-icon  fas fa-digital-tachograph"></i>
                            <p>
                                Kaufland
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a n:href="Kaufland:catalog"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Katalog</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a n:href="Kaufland:default"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Zalistování položek</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a n:href="Kaufland:orders"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Objednávky</p>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a n:href="Kaufland:subscriptions"  n:class="$presenter->isLinkCurrent() ? active,nav-link">
                                    <i class="far fa-circle nav-icon"></i>
                                    <p>Registrace eventů</p>
                                </a>
                            </li>
                        </ul>
                    </li>

                </ul>
            </nav>

        </div>

    </aside>

    <div class="content-wrapper">

        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1  n:ifset=title class="m-0">{include title}</h1>
                    </div>
                    <div class="col-sm-6"  n:ifset=breadcrumb>{include breadcrumb}</div></div>
            </div>
        </div>


        <div class="content">
            <div class="container-fluid">

                {foreach $flashes as $flash}
                  <div class="alert alert-{$flash->type==='err'?'danger':'primary'}">
                    {$flash->message}
                  </div>
                {/foreach}

                {include content}


            </div>
        </div>

    </div>


    <aside class="control-sidebar control-sidebar-dark">

        <div class="p-3">
            <h5>Title</h5>
            <p>Sidebar content</p>
        </div>
    </aside>


    <footer class="main-footer">

        <div class="float-right d-none d-sm-inline">
           {block footer}{/block}
        </div>

        <strong>Databáze: {$dbName}</strong> Verze: {$appVersion==""?"develop":$appVersion}
    </footer>
</div>


<div class="modal" id="confirmModal" style="display: none; z-index: 1050;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body" id="confirmMessage">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" id="confirmOk">Ok</button>
                <button type="button" class="btn btn-default" id="confirmCancel">Cancel</button>
            </div>
        </div>
    </div>
</div>

{block scripts}
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct" crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"
            integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU=" crossorigin="anonymous"></script>

    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>

    <script src="https://cdn.jsdelivr.net/gh/contributte/live-form-validation@master/live-form-validation.js"></script>
    <script src="https://unpkg.com/naja@2/dist/Naja.min.js"></script>
    <script src="https://kit.fontawesome.com/e1be37927b.js" crossorigin="anonymous"></script>

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/i18n/cs.js"></script>

<script>
    function confirmDialog(message, event){
        var fClose = function(){
            modal.modal("hide");
        };
        var modal = $("#confirmModal");
        modal.modal("show");
        $("#confirmMessage").empty().append(message);
        $("#confirmOk").unbind().one('click', fClose);
        $("#confirmCancel").unbind().one('click',  function (event) {
            event.currentTarget.submit();
            console.log(event);
        } ).one("click", fClose);
    }

    LiveForm.setOptions({
        controlErrorClass: 'is-invalid',
        showMessageClassOnParent: false,
        wait: 500
    });

    $(function () {

        naja.initialize();
        naja.uiHandler.addEventListener('interaction', (event) => {
            const { element } = event.detail;
            const question = element.dataset.confirm;
            if (question && ! window.confirm(question)) {
                event.preventDefault();
            }else{
                const hide = element.dataset.hide;
                if(hide){
                    $(hide).hide();
                }
            }

        });

        Nette.initOnLoad();

        //Initialize Select2 Elements
        $('.select2').select2();

        $(document).on('select2:open', () => {
            document.querySelector('.select2-search__field').focus();
        });

        //Initialize Select2 Elements
        $('.select2Products').select2({
            ajax: {
                url: {plink select2Products!},
                dataType: 'json',
                delay: 250,
                // Additional AJAX parameters go here; see the end of this chapter for the full code of this example
            },
            placeholder: "-- vyber --",
            minimumInputLength: 3,
            language: "cs",
            allowClear: true

        });
        $('.select2Catalogs').select2({
            ajax: {
                url: {plink select2Catalogs!},
                dataType: 'json',
                delay: 250,
                // Additional AJAX parameters go here; see the end of this chapter for the full code of this example
            },
            placeholder: "-- vyber --",
            minimumInputLength: 3,
            language: "cs",
            allowClear: true

        });
    });
</script>
{/block}

</body>
</html>
