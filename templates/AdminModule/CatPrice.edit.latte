{block #content}
  <h3>{block #title} Editace nastavení cen dle katalogu {/block}</h3>

  {form editForm}
  <table>
    <tr>
      <th>{label cprcatid class=>'required' /}</th>
      <td>{input cprcatid size=>3}
      <input type="text" id="catname" size="80" value="{$catPath}">
      <a href="#" onclick="return clearAc();"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a></td>
    <tr>
       <th>{label cprserid class=>'required' /}</th>
       <td>{input cprserid}</td>
    </tr>
    <tr>
       <th>{label cprvenid class=>'required' /}</th>
       <td>{input cprvenid}</td>
      </tr>
    <tr>
       <th>{label cprpricecat class=>'required' /}</th>
       <td>{input cprpricecat}</td>
      </tr>
    <tr>
       <th>{label cprmargin class=>'required' /}</th>
       <td>{input cprmargin}</td>
      </tr>
    <tr>
      <th>{label cprmargintype class=>'required' /}</th>
      <td>{input cprmargintype}</td>
    <tr>
      <th></th>
      <td>{input cprmarginfix} {label cprmarginfix /}
    </tr>
    <tr>
      <th>{label cprstatus class=>'required' /}</th>
      <td>{input cprstatus}</td>
    <tr>
    <tr>
      <th></th>
      <td>{input submit}
    </tr>
  </table>
  {/form}

  <script type="text/javascript">
  var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatId.js"></script>
{/block}
