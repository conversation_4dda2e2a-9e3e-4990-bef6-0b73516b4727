{varType App\Orm\Product $product}
{block content}
  <script type="text/javascript">
    var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatId.js"></script>

  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2
    });
  });
  </script>

  <script type="text/javascript">
    var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteParams.js"></script>
  </script>


  {default $dataRow = false}
  {var $tab =$presenter->getParameter('tab')}
  <h3>{block #title}{if $dataRow} {$dataRow->proname} {else} Nová položka {/if}{/block}</h3>
  {if $dataRow}
    <p><a href="{plink :Front:Product:detail, $dataRow->proid, getProKey($dataRow)}" target="_blank">Detail zboží ve veřejn<PERSON></a>{if $dataRow->promall == 1} | <a href="{plink :Front:Mall:updateProduct, $dataRow->procode}">Vynutit MALL aktualizaci</a>{/if}</p>
  {/if}
  <script type="text/javascript">
  $(document).ready(function() {
    $(function() {
      $("#tabs").tabs();
    });
  });
  </script>

  <div id="tabs">
   <ul>
      <li><a href="#tabs_editmain">Základní údaje</a></li>
      <li><a href="#tabs_editcatalog">Katalog</a></li>
      <li><a href="#tabs_editdesc">Dlouhý popis</a></li>
      <li><a href="#tabs_pic">Obrázky</a></li>
      <li><a href="#tabs_attachment">Přílohy</a></li>
      <li><a href="#tabs_param">Parametry</a></li>
      <li><a href="#tabs_options">Příslušenství</a></li>
      <li><a href="#tabs_seo">SEO</a></li>
      <li><a href="#tabs_store">Sklad</a></li>
     {if !empty($dataRow->procodegro)}<li><a href="#tabs_group">Skupina produktů</a></li>{/if}
   </ul>
   {form productEditForm}
   {php $form->render('errors') }

   <div id="tabs_editmain">
    <table>
      <tr>
        <th>{label procode class=>'required' /}</th>
        <td>{input procode}</td>
      </tr><tr>
        <th>{label procode2 /}</th>
        <td>{input procode2}
        {if !empty($dataRow->procode2)}
        {if $eanCnt > 1} Duplicitní EAN kód <a href="{plink Product:default 'sCode2'=>$dataRow->procode2}" target="ean">{$dataRow->procode2}</a> {/if} | {if !empty($eanCheck)} {$eanCheck} {/if}
        {/if}
        </td>
      </tr><tr>
        <th>{label procodeven /}</th>
        <td>
          {input procodeven}{input provenid}{label proqty_atc /}{input proqty_atc}<br>
            {input proodfix} {label proodfix /}
            {if !empty($dataRow->provenid)}
            {if ($dataRow->provenid == 'bakr')}<a href="http://www.bakr.cz/default.asp?cls=stoitems&sticode={$dataRow->procodeven}" target="bakr">Vyhledat na stránkách Bakru</a>{/if}
            {if ($dataRow->provenid == 'ird')}<a href="http://www.irdcz-shop.cz/default.asp?cls=stoitems&sticode={$dataRow->procodeven}" target="ird">Vyhledat na stránkách IRD</a>{/if}
            {if ($dataRow->provenid == 'atc')}<a href="https://www.atcomp.cz/zbozi.aspx?kod={$dataRow->procodeven}" target="atc">Vyhledat na stránkách ATC</a>{/if}
            {if ($dataRow->provenid == 'novaservis')}<a href="http://www.novaservis.cz/search/?q={$dataRow->procodeven}&first_result" target="atc">Vyhledat na stránkách Novaservis</a>{/if}
          {/if}
        </td>
      </tr><tr>
        <th>{label procodegro /}</th>
        <td>{input procodegro}</td>
      </tr><tr>
        <th>{label proname class=>'required' /}</th>
        <td>{input proname}<br>{input pronamefix} {label pronamefix /} | {input propicfix} {label propicfix /} | {input prodescfix} {label prodescfix /}</td>
      </tr><tr>
         <th>{label pronames /}</th>
         <td>{input pronames}<small>Název pro zbožové agregátory (heureka, zbozi.cz, atd.) - pokud nevyplníte použije se Název zboží</small></td>
       </tr>{*<tr>
        <th>{label pronameadd /}</th>
        <td>{input pronameadd}<small>Dodatek do názvu pro zbožové agregátory (heureka, zbozi.cz, atd.)</small></td>
      </tr>*}
      <tr>
        <th>Dárky:<br><small>Vyplňte kódy dárků oddělené čárkou</small></th>
        <td>
        {if $serverId == 'web1'}
          {$serverNames["web1"]}:{input progifts_web1}{foreach $proGiftsList_web1 as $row} <a href="{plink edit $row->proid}">{$row->proname}</a> {if !$iterator->last} | {/if}{/foreach}
        {else}
          {$serverNames["web2"]}: {input progifts_web2}{foreach $proGiftsList_web2 as $row} <a href="{plink edit $row->proid}">{$row->proname}</a> {if !$iterator->last} | {/if}{/foreach}<br>
          {$serverNames["web3"]}: {input progifts_web3}{foreach $proGiftsList_web3 as $row} <a href="{plink edit $row->proid}">{$row->proname}</a> {if !$iterator->last} | {/if}{/foreach}
        {/if}
        </td>
      </tr>

      <tr>
        <th>Sleva:</th>
        <td>
          {$serverNames["web2"]}: {input progiftdisc_web2 size=>"5"}{input progiftprodiscunit_web2} {input progiftprodisc_web2} {label progiftprodisc_web2 /}<br>
          {$serverNames["web3"]}: {input progiftdisc_web3 size=>"5"}{input progiftprodiscunit_web3} {input progiftprodisc_web3} {label progiftprodisc_web3 /}
        </td>
      </tr>

      <tr>
        <th>Typ dárku</th>
        <td>
          {label progifttype /}: {input progifttype}
        </td>
      </tr>

      <tr>
        <th>Balení:<br></th>
        <td>
        {if $serverId == 'web1'}
          {$serverNames["web1"]}:{input proservices_web1}
        {else}
          {$serverNames["web2"]}: {input proservices_web2}<br>
          {$serverNames["web3"]}: {input proservices_web3}
        {/if}
        </td>
      </tr>

      <tr>
        <th>{label promanid class=>'required' /}</th>
        <td>{input promanid}<br>{input promanidfix} {label promanidfix /}</td>
      </tr><tr>
        <th>&nbsp;</th>
        <td>
          {*{input protypid} {label protypid /}<br>*}
          {input protypid2} {label protypid2 /}<br>
          {*{input protypid3} {label protypid3 /}<br>*}
          {*{input protypid4} {label protypid4 /}<br>*}
          {input protypid5} {label protypid5 /}<br>
          {input protypid6} {label protypid6 /}
        </td>
      </tr>

      {if $serverId == "web1"}
      <tr>
        <th>{label propricecom_web1 /}</th>
        <td>{input propricecom_web1}</td>
      </tr>
      <tr>
        <th>{label propricea_web1 class=>'required' /}</th>
        <td>{input propricea_web1}</td>
      </tr>
      <tr>
        <th>{label propriceb_web1 /}</th>
        <td>{input propriceb_web1}</td>
      </tr>
      <tr>
        <th>{label propricec_web1 /}</th>
        <td>{input propricec_web1}</td>
      </tr>
      <tr>
        <th>{label propriced_web1 /}</th>
        <td>{input propriced_web1}</td>
      </tr>
      <tr>
        <th>{label propricee_web1 /}</th>
        <td>{input propricee_web1}</td>
      </tr>
      <tr>
        <th></th>
        <td>{input propricefix_web1} {label propricefix_web1 /}</td>
      </tr>
      <tr>
        <th>{label proqtymin_web1 /}</th>
        <td>{input proqtymin_web1}</td>
      </tr>
      {elseif $serverId == "web2" || $serverId == "web3"}

      <tr>
        <th>{label propricecom_web2 /}</th>
        <td>{input propricecom_web2} {if !empty($product->priceEU)}priceEU: {$product->priceEU|formatPrice}{/if}</td>
      </tr>
      <tr>
        <th>{label propricea_web2 class=>'required' /}</th>
        <td>{input propricea_web2}</td>
      </tr>
      <tr>
        <th>{label propriceb_web2 /}</th>
        <td>{input propriceb_web2}</td>
      </tr>
      <tr>
        <th>{label propricec_web2 /}</th>
        <td>{input propricec_web2}</td>
      </tr>
      <tr>
        <th>{label propriced_web2 /}</th>
        <td>{input propriced_web2} - Nakupní</td>
      </tr>
      <tr>
        <th>{label propricee_web2 /}</th>
        <td>{input propricee_web2}</td>
      </tr>
      <tr>
        <th>{label proqtymin_web2 /}</th>
        <td>{input proqtymin_web2}</td>
      </tr>
        <tr>
        <th></th>
        <td>{input propricefix_web2} {label propricefix_web2 /}</td>
      </tr>
      <tr>
        <th>{label propricecom_web3 /}</th>
        <td>{input propricecom_web3} {if !empty($product->priceEU)}priceEU: {$product->priceEU|formatPrice}{/if}</td>
      </tr>
      <tr>
        <th>{label propricea_web3 class=>'required' /}</th>
        <td>{input propricea_web3}</td>
      </tr>
      <tr>
        <th>{label propriceb_web3 /}</th>
        <td>{input propriceb_web3}</td>
      </tr>
      <tr>
        <th>{label propricec_web3 /}</th>
        <td>{input propricec_web3}</td>
      </tr>
      <tr>
        <th>{label propriced_web3 /}</th>
        <td>{input propriced_web3} - Nakupní</td>
      </tr>
      <tr>
        <th>{label propricee_web3 /}</th>
        <td>{input propricee_web3}</td>
      </tr>
      <tr>
        <th>{label proqtymin_web3 /}</th>
        <td>{input proqtymin_web3}</td>
      </tr>
      <tr>
        <th></th>
        <td>{input propricefix_web3} {label propricefix_web3 /}</td>
      </tr>
      <tr>
        <th>{label prorecfee /}</th>
        <td>{input prorecfee}{ifset $recycleFeeByCatalog} <small>Dle katalogu: {$recycleFeeByCatalog|formatPrice:2}</small>{/ifset}</td>
      </tr>
      <tr>
        <th>Doprava zdarma:</th>
        <td>
          {label prodelfree_web2 /} {input prodelfree_web2} <br>
          {label prodelfree_web3 /} {input prodelfree_web3}
        </td>
      </tr>
      <tr>
        <th></th>
        <td>{input prodelcheckoff} {label prodelcheckoff /}</td>
      </tr>
      {/if}
      {if $id > 0}
      <tr>
        <th>Marže</th>
        <td>
         <table class="grid">
          <tr><th>eshop</th><th>Typ</th><th>%</th><th>Značka</th><th>Cena Od</th><th>Cena Do</th><th></th></tr>
         <tr n:if="$product->marginWeb2">
            <td>shopcom.cz</td>
            <td>{$product->marginWeb2->getTextType()}</td>
            <td>{$product->marginWeb2->margin} %</td>
            {if $product->marginWeb2->manufacturer}
            <td>{$product->marginWeb2->manufacturer->name}</td>
             <td>{$product->marginWeb2->priceFrom} kč</td>
             <td>{$product->marginWeb2->priceTo} kč</td>
             {else} <td></td><td></td><td></td>
           {/if}
             <td>
            <a n:href="Catalog:edit $product->marginWeb2->catalog->id">
            <img src="/img/admin/edit.png" width="16" height="16" alt="editovat">
            </a>
            </td>
          </tr>
           <tr n:if="$product->marginWeb3">
            <td>sccom.cz</td>
            <td>{$product->marginWeb3->getTextType()}</td>
            <td>{$product->marginWeb3->margin} %</td>
            {if $product->marginWeb3->manufacturer}
            <td>{$product->marginWeb3->manufacturer->name}</td>
             <td>{$product->marginWeb3->priceFrom} kč</td>
             <td>{$product->marginWeb3->priceTo} kč</td>
              {else} <td></td><td></td><td></td>
           {/if}
            <td>
            <a n:href="Catalog:edit $product->marginWeb3->catalog->id">
            <img src="/img/admin/edit.png" width="16" height="16" alt="editovat">
            </a>
            </td>
          </tr>
          </table>
        </td>
      </tr>
      <tr>
        <th></th>
        <td>
         <a n:href="recalculate!" class="btn btn-secondary btn-sm">Přepočítat</a>
        </td>
      </tr>
      {/if}
      <tr>
        <th>Průměrná nákupní cena na skladu</th>
        <td>{$productStorePrice|formatPrice}</td>
      </tr>
      <tr>
        <th>{if $id > 0}<strong>Stav skladu celkem: </strong>{/if}</th>
        <td>
        {if $serverId == "web1"}
          {if $id > 0}{$dataRow->proqty_web1} ks | {/if}
          Dostupnost {$serverNames["web1"]}: {if isset($form["proaccess_web1"])} {input proaccess_web1}{else}{$enum_proaccess[$dataRow->proaccess_web1]}{/if}<br>
        {elseif $serverId == "web2" || $serverId == "web3"}
          {if $id > 0}{$dataRow->proqty_web2} ks | {/if}
          Dostupnost {$serverNames["web2"]}: {if isset($form["proaccess_web2"])} {input proaccess_web2}{else}{$enum_proaccess[$dataRow->proaccess_web2]}{/if}<br>
          {if $id > 0}{$dataRow->proqty_web3} ks | {/if}
          Dostupnost {$serverNames["web3"]}: {if isset($form["proaccess_web3"])} {input proaccess_web3}{else}{$enum_proaccess[$dataRow->proaccess_web3]}{/if}<br>
        {/if}
        </td>
      </tr>
      {if $id > 0}
      <tr valign="top">
        <td style="border: 1px solid;">
        {if isset($stoFree)}
        {foreach $stoFree as $row}
          {if $iterator->isFirst()}
          <strong>Volné kusy po skladech:</strong><br />
          {/if}
          {$row->stoname}: {$row->stiqtyfree} ks
          {if !$iterator->isLast()}
          <br />
          {else}
          {/if}
        {/foreach}
        {/if}
        {if !empty($dataRow->provenid)}
        <br>Dodavatel: {$dataRow->proqty_atc} ks
        {/if}
        </td>
        {if isset($deliveries)}
        {foreach $deliveries as $row}
          {if $iterator->isFirst()}
        <td style="border: 1px solid;">
          <strong>Zboží na cestě:</strong><br />
          {/if}
          sklad: {$row->stoname}, dod.: {$row->venname}, {$row->deiqty} ks {if !empty($row->deldateexpected)}({$row->deldateexpected|date:'d.m.Y'}){/if}
          {if !$iterator->isLast()}
          <br />
        </td>
          {/if}
        {/foreach}
        {/if}
      </tr>
      {/if}
      <tr>
        <th>Cena za proklik:</th>
        <td>
          <table class="grid">
            <tr>
              <th>Heuréka</th>
              <th>Zboží</th>
              <th>Zboží vyhledávání</th>
            </tr><tr>
              <td>{input procpcheureka}</td>
              <td>{input procpczbozi}</td>
              <td>{input procpczbozis}</td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <th>{label procpcheurekafix /}:</th>
        <td>{input procpcheurekafix}</td>
      </tr>
      <tr>
        <th>{label progooglenakupy /}:</th>
        <td>{input progooglenakupy}</td>
      </tr>
      <tr>
        <th>{label promall /}:</th>
        <td>{input promall}</td>
      </tr>
      <tr>
        <th>{label prokaufland /}:</th>
        <td>{input prokaufland}</td>
      </tr>
      <tr>
        <th>{label proheko /}:</th>
        <td>{input proheko}</td>
      </tr>
      <tr>
        <th>{label proheurekaoff /}:</th>
        <td>{input proheurekaoff}</td>
      </tr>
      <tr>
        <th>{label protanganicaoff /}:</th>
        <td>{input protanganicaoff}</td>
      </tr>
      <tr>
        <th>{label prfheurekaoverenooff /}:</th>
        <td>{input prfheurekaoverenooff}</td>
      </tr>
      <tr>
        <th>{label proadultonly /}:</th>
        <td>{input proadultonly}</td>
      </tr>
      <tr>
        <th>{label proaccessdef /}</th>
        <td>{input proaccessdef}</td>
      </tr><tr>
        <th>{label proorigin /}</th>
        <td>{input proorigin}</td>
      </tr><tr>
         <th>{label provideo /}</th>
         <td>{input provideo}</td>
       </tr><tr>
         <th>{label prowarranty /}</th>
         <td>{input prowarranty}<small>Textově délka záruky, např. "24 mesíců"</small></td>
       </tr><tr>
         <th>{label proweight /}</th>
         <td>{input proweight}<small>Kg</small></td>
       </tr><tr>
         <th>{label prowidth /}</th>
         <td>{input prowidth}<small>cm</small></td>
       </tr>

      <tr>
        <th>{label provatid class=>'required' /}</th>
        <td>{input provatid}</td>
      </tr><tr>
        <th>{label prospecmode /}</th>
        <td>{input prospecmode}</td>
      </tr><tr>
        <th>{label proisac /}</th>
        <td>{input proisac}</td>
      </tr><tr>
        <th>{label proused /}</th>
        <td>{input proused}</td>
      </tr><tr>
         <th>{label prostatus /}</th>
         <td>{input prostatus} {if !empty($dataRow->proadmblockdate)} Datum posledního adminbloku: {$dataRow->proadmblockdate|date:'d.m.Y'}{/if}</td>
       </tr><tr>
        <th>&nbsp;</th>
        <td>{input tabs_editmain}</td>
      </tr>
     </table>
   </div>
   <div id="tabs_editcatalog">

     <table>
     {if isset($proCatalogs)}
     {foreach $proCatalogs as $row}
       <tr>
        <td>{php echo $form['cat_'.$row->catid]->control }</td>
        <th>{php echo $form['cat_'.$row->catid]->getLabel() }</th>
      </tr>
     {/foreach}
     {/if}
       <tr>
         <td colspan="2">{input cat_0 size=>3} <input id="catname" type="text" size="100"></td>
       </tr><tr>
        <th>&nbsp;</th>
        <td>{input tabs_editcatalog}</td>
      </tr>
     </table>
   </div>
   <div id="tabs_editdesc">
     <table>
       <tr>
        <th>{label prodesc /}</th>
        <td>{input prodesc}</td>
      </tr><tr>
        <th>{label prodescs /}</th>
        <td>{input prodescs}</td>
      </tr><tr>
        <th>&nbsp;</th>
        <td>{input tabs_editdesc}</td>
      </tr>
     </table>
   </div>
   <div id="tabs_seo">
     <table>
       <tr>
         <th>{label prokey /}</th>
         <td>{input prokey}<small>Pokud ponecháte prázdné, generuje se z názvu zboží</small></td>
       </tr><tr>
         <th>{label protitle /}</th>
         <td>{input protitle}<small>Zadávejte pokud chcete jiné TITLE nez je název zboží</small></td>
       </tr><tr>
         <th>{label prokeywords /}</th>
         <td>{input prokeywords}<small>Zadávejte výčet klíčových slov, které nejsou v názvu zboží oddělený čárkou</small></td>
       </tr>
       <tr>
         <th>{label prosearchkeywords /}</th>
         <td>{input prosearchkeywords}<small>Zadávejte výčet klíčových slov, které nejsou v názvu zboží oddělený mezerou</small></td>
       </tr>
       <tr>
         <th>{label prodescription /}</th>
         <td>{input prodescription} <p class="charsRemaining"></p></td>
       </tr><tr>
         <th>&nbsp;</th>
         <td>{input tabs_seo}</td>
      </tr>
     </table>
   </div>
   <div id="tabs_pic">
     {if isset($proSamePicNames)}
       {foreach $proSamePicNames as $row}
         {if $iterator->isFirst()}
         <p><strong>Nalezena duplicita názvů obrázků:</strong><br />
         {/if}
         <a href="{plink 'edit', $row->proid}">{$row->procode} {$row->proname}</a><br />
         {if $iterator->isLast()}
         </p>
         {/if}
       {/foreach}
     {/if}
     {* vypisu obrazky *}
     {if isset($images)}
     {php
       $sid = ($serverId == 'web1' ? 'web1' : "web2");
     }
     {foreach $images as $pos => $image}
       {if ($pos != 11)}
       <img title="{$image["title"]}" src="{$baseUrl}/pic/{$sid}/product/list/{$image["filename"]}?{time()}" />
       {else}
       <img title="{$image["title"]}" src="{$baseUrl}/pic/{$sid}/product/slider/{$image["filename"]}?{time()}" />
       {/if}
       {if ($pos != 0 && $pos != 11)} <a href="{plink 'deleteImage', $id, $image["filename"]}"><img src="{$baseUrl}/img/admin/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat {$image["title"]}" /></a>{/if}
     {/foreach}
     {/if}
     <table>
       <tr>
         <th>{label propicname /}</th>
         <td>{input propicname}<small>.jpg</small></td>
       </tr><tr>
         <th>{label pic0 /}</th>
         <td>{input pic0}<small>Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.</small></td>
       </tr><tr>
         <th>{label pics /}</th>
         <td>{input pics}<small>Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku 200x284px jinak bude obrázek oříznutý.</small></td>
       </tr><tr>
         <th>{label pic1 /}</th>
         <td>{input pic1}</td>
       </tr><tr>
         <th>{label pic1position /}</th>
         <td>{input pic1position}<small>Nahráním nového obrázku na obsazenou pozici ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.</small></td>
       </tr><tr>
         <th>&nbsp;</th>
         <td>{input tabs_pic}</td>
      </tr>
     </table>
   </div>

   <div id="tabs_attachment">
     {if isset($attachments)}
     {* vypisu priloh *}
     {foreach $attachments as $row}
       <a href="{$baseUrl}/files/{$row["atafilename"]}?{time()}">{$row["ataname"]}</a>
       <a href="{plink 'deleteFile', $row["ataid"], $id}"><img src="{$baseUrl}/img/admin/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat {$row["ataname"]}" /></a><br />
     {/foreach}
     {/if}
     <table>
       <tr>
         <th>{label attAdd /}</th>
         <td>{input attAdd}</td>
       </tr><tr>
         <th>{label ataname /}</th>
         <td>{input ataname}</td>
       </tr><tr>
         <th>&nbsp;</th>
         <td>{input tabs_attachment}</td>
      </tr>
     </table>
   </div>

   <div id="tabs_param">
   {include Product.param.latte, form: $form}
     <table>
       <tr>
         <th>{label param_name_0 /}</th>
         <td>{input param_name_0}</td>
       </tr><tr>
         <th>{label param_value_0 /}</th>
         <td>{input param_value_0}</td>
       </tr>
       <tr>
         <th>{label param_unit_0 /}</th>
         <td>{input param_unit_0}</td>
       </tr>
       {if isset($form["params_edit"])}
       {foreach $form["params_edit"]->controls as $key => $component}
         <tr>
          <th>{label $component/}</th>
          <td>{input $component} {if $key == 'param_value'}<a class="trShow" href="#">jiná hodnota</a> | <a href="{plink 'deleteParam', $id, (int)$component->parent->name}">vymazat</a>{/if}</td>
         </tr>
       {/foreach}
       {/if}
       {if isset($form["params_new"])}
       {foreach $form["params_new"]->controls as $key => $component}
         <tr>
          <th>{label $component/}</th>
          <td>{input $component} {if $key == 'param_value'}<a class="trShow" href="#">jiná hodnota</a>{/if}</td>
         </tr>
       {/foreach}
       {/if}
       <tr>
         <th>&nbsp;</th>
         <td>{input tabs_param}</td>
      </tr>
     </table>
   </div>

   <div id="tabs_options">
     <strong>Kategorie s příslušenstvím:</strong>
     <table>

       {if isset($proAcsCats)}
       {foreach $proAcsCats as $cid}
         <tr>
          <td>{php echo $form['prfcsc_'.$cid]->control }</td>
          <th>{php echo $form['prfcsc_'.$cid]->getLabel() }</th>
        </tr>
       {/foreach}
       {/if}
         <tr>
           <td colspan="2">Nová kategorie s příslušenstvím <input id="csccatname" type="text" size="100">{input prfcsc_0 size=>3}</td>
         </tr>
       <tr>
         <th>{php echo $form["options"]["proaccsstring"]->getLabel() }</th>
         <td>{php echo $form["options"]["proaccsstring"]->control }</td>
       </tr>
       {foreach $form["options"]->controls as $key => $item}
         {if substr($key, 0, 11)=='procode_new'}
         <tr>
           <th>{php echo $form["options"][$key]->getLabel() }</th>
           <td>{php echo $form["options"][$key]->control }</td>
         </tr>
         {elseif $key!='proaccsstring'}
         <tr>
           <td>&nbsp;</td>
           <th>{php echo $form["options"][$key]->control } {php echo $form["options"][$key]->getLabel() }</th>
         </tr>
         {/if}
       {/foreach}

       <tr>
         <th>&nbsp;</th>
         <td>{input tabs_options}</td>
      </tr>
     </table>
   </div>

   <div id="tabs_store">
   {ifset $dataRow->proqty}
   <strong>Posledních 20 skladových pohybů</strong>
   <table class="grid">
   {foreach $stoOpers as $row}
     {if $iterator->isFirst()}
     <tr>
     <th>Datum</th>
     <th>Dodavatel</th>
     <th>Sklad</th>
     <th>Počet</th>
     <th>Typ</th>
     <th></th>
     </tr>
     {/if}
     <tr>
     <td>{$row->stideldate|date:'d.m.Y'}</td>
     <td>{$row->venname}</td>
     <td>{$row->stoname}</td>
     <td>{$row->stiqty}</td>

     {if !empty($row->web1_oriordid)}
     <td>prodej {$serverNames["web1"]}</td>
     <td><a href="https://www.{$serverNames["web1"]}/{plink Order:edit, $row->web1_oriordid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat objednávku"/></a></td>
     {elseif !empty($row->web2_oriordid)}
     <td>prodej {$serverNames["web2"]}</td>
     <td><a href="https://www.{$serverNames["web2"]}/{plink Order:edit, $row->web2_oriordid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat objednávku"/></a></td>
     {elseif !empty($row->web3_oriordid)}
     <td>prodej {$serverNames["web3"]}</td>
     <td><a href="https://www.{$serverNames["web3"]}/{plink Order:edit, $row->web3_oriordid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat objednávku"/></a></td>
     {elseif !empty($row->stiwoff)}
     <td>odpis</td>
     <td>{$row->stinote}</td>
     {elseif !empty($row->stisopid)}
     <td>přesun</td>
     <td></td>
     {elseif !empty($row->stittiid)}
     <td>přesun</td>
     <td><a href="{plink StoreTransfer:edit, $row->ttisttid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat skladový přesun"/></a></td>
     {elseif !empty($row->sticopid)}
     <td>reklamace</td>
     <td><a href="{plink Complaint:edit, $row->sticopid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat reklamaci"/></a></td>
     {else}
     <td>dodávka</td>
     <td><a href="{plink Delivery:edit, $row->deidelid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat dodávku"/></a></td>
     {/if}

     </tr>
     {if $iterator->isLast()}
     {/if}
   {/foreach}
   </table>
     <p><a href="{plink Store:opers $id}" target="stoopers">Vypsat všechny pohyby ...</a> </p>

   {foreach $deliveries as $row}
     {if $iterator->isFirst()}
     <strong>Zboží na cestě</strong>
     <table class="grid">
     <tr>
       <th>Datum</th>
       <th>Sklad</th>
       <th>Počet</th>
       <th></th>
     </tr>
     {/if}
     <tr>
       <td>{$row->deldate|date:'d.m.Y'}</td>
       <td>{$row->stoname}</td>
       <td>{$row->deiqty}</td>
       <td><a href="{plink Delivery:edit, $row->delid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
     </tr>
     {if $iterator->isLast()}
     </table>
     {/if}
   {/foreach}
   {/ifset}
   </div>
   <div id="tabs_group">
     {foreach $productsGroup as $row}
       {if $iterator->isFirst()}
         <table class="grid">
         <tr>
           <th>Kód</th>
           <th>Název</th>
           <th></th>
         </tr>
       {/if}
       <tr>
         <td>{$row->procode}</td>
         <td>{$row->proname}</td>
         <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
       </tr>
       {if $iterator->isLast()}
         </table>
       {/if}
     {/foreach}
   </div>
   <table>
     <tr>
       <td>{input save}</td>
       {if $id > 0}<td>{input saveAsNew}</td>{/if}
     </tr>
   </table>
   {/form}
</div>
<script>
  $('.trHide').closest('tr').hide();
  $('.trShow').click(function() {
    $(this).closest('tr').next('tr').toggle();
    return false;
  });
</script>
<script type="text/javascript" src="{$baseUrl}/js/textarea_maxlen.js"></script>

{ifset $dataRow->prodatec}
<p>
  Vytvořeno: {$dataRow->prodatec|date:'d.m.Y H:i:s'} |
  změněno: {$dataRow->prodateu|date:'d.m.Y H:i:s'} |
  {if $dataRow->provenid=='atc'}
  cena: {$dataRow->propricedate2|date:'d.m.Y H:i:s'} |
  dostupnost: {$dataRow->proaccessdate2|date:'d.m.Y H:i:s'} |
  {/if}
  <a href="{plink //:Front:Export:products, 'heurekacz', $dataRow->proid}" target="_blank">Heureka feed</a> |
  <a href="{plink //:Front:Export:products, 'zbozicz', $dataRow->proid}" target="_blank">Zbozi feed</a> |
  <a href="{plink //:Front:Export:products, 'srovnamecz', $dataRow->proid}" target="_blank">Srovname.cz feed</a>
</p>
{/ifset}
{/block}