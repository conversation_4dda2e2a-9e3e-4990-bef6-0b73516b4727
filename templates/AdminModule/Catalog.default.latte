{varType App\Orm\Catalog[] $rootCatalogs}
{varType App\Orm\Catalog|null $selectCatalog}
{varType string $baseUrl}
{varType int|null $mId}
{block content}
  <script type="text/javascript">
  var basePath = {$baseUrl};

  $( function() {
      $("#catname_search").autocomplete({
          source: basePath + "/administrace/product/search-ac",
          minLength: 3,
          select: function (event, ui) {
            location.href = basePath + "/administrace/catalog/edit/" + ui.item.id;
          }
      });
  });
  </script>

<h3>{block title}Katalog{/block}</h3>

  Hledat kategorii: <input type="text" name="catname" id="catname_search" size="100"><br>

<a n:href="updateCatalogHaveProduct!">Aktualizace počtu produktu</a>

  <div id="menu-top">
  {foreach $rootCatalogs as $catalog}
    <span>
      <a n:href="default mId => $catalog->id">{$catalog->catname}
      <small style="background: mediumspringgreen">{$catalog->getCatGiftLabel()}</small>
      <small style="background: #d4921e">{$catalog->getCatMarginLabel()}</small>
      </a>
    </span>
  {/foreach}
  </div>

  {ifset $selectCatalog}
    {control rootCatalogEditForm}


  {block cataloglist}
    {foreach $selectCatalog->catalogsAll as $catalog}
  <ul>
    <li>[{$catalog->catorder}] - {l}{$catalog->countProduct}{r} {if $catalog->status==App\Orm\Catalog::STATUS_ACTIVE}<strong>{$catalog->catname}</strong>{else}<s>{$catalog->catname}</s>{/if}
    <a n:href="Catalog:edit, $catalog->id"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" /></a>
    <a n:href="Catalog:edit, 0, catmasid=>$catalog->id"><img src="{$baseUrl}/img/admin/add.png" width="16" height="16" /></a>
    <strong style="background: mediumspringgreen">{$catalog->getCatGiftLabel()}</strong>
    <strong style="background: #d4921e">{$catalog->getCatMarginLabel()}</strong>
    <a n:href="Catalog:delete, $catalog->id, $mId" onclick="return DeleteConfirm('katalog {$catalog->catname} včetně podkategorií');"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" /></a>
      {if count($catalog->catalogsAll)} {include this, selectCatalog => $catalog}{/if}
    </li>
  </ul>
    {/foreach}
  {/block}
{/ifset}

