{varType App\Orm\Catalog[] $catalogs}
{varType ?App\Orm\Catalog $rootCatalog}
{varType ?App\Orm\Catalog $selectCatalog}
{varType App\Orm\Param[] $params}

{block title}Parametry{/block}
{block breadcrumb}
    <ol class="breadcrumb float-sm-right">
        <li class="breadcrumb-item"><a href="/administrace/">Domu</a></li>
        <li class="breadcrumb-item active">Parametry</li>
    </ol>
{/block}
{block content}
<div class="row">

    <div class="col-lg-2">
        <div class="card">
            <div class="card-header">
                <h5 class="m-0">Root kategorie</h5>
            </div>
            <div class="card-body">
              <a n:foreach="$catalogs as $catalog" n:href="Param:edit catalogId:$catalog->id" class="btn  btn-block {if isset($rootCatalog) && $rootCatalog->id==$catalog->id}btn-primary{else}btn-default{/if} "> {$catalog->catname}</a>
            </div>
        </div>
    </div>
    <div class="col-lg-3" n:ifset="$rootCatalog">
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h5 class="m-0">{$rootCatalog->catname}</h5>
            </div>
            <div class="card-body">

                    {var $loopCatalog=$rootCatalog}
                    {block cataloglist}
                    <ul>
                        <li n:foreach="$loopCatalog->catalogs as $catalog">
                            <a n:href="Param:edit catalogId:$catalog->id" n:class="$selectCatalog->id!=$catalog->id?text-dark">{$catalog->catname}</a>
                             {if count($catalog->catalogsAll)} {include this, loopCatalog => $catalog}{/if}
                        </li>
                    </ul>
                    {/block}

            </div>
        </div>
    </div>
    <div class="col-lg-6" n:if="$selectCatalog"  >
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h5 class="m-0">{foreach $selectCatalog->getTree() as $one} <a n:href="Param:edit catalogId:$one->id" n:class="$iterator->last?font-weight-bold"> {$one->catname }</a> {sep} / {/sep} {/foreach}</h5>
            </div>
            <div class="card-body" n:snippet="catalogParams"  data-sortable="{link sortParams!}">
                {foreach $params as $param}

                <div class="card card-outline card-{=$selectCatalog->params->has($param)?'info':'warning'} ui-sortable-handle" id="param{$param->id}">
                    <div class="card-header">
                        <h3 class="card-title">{$param->name} {=$param->unit? ' ('.$param->unit.')':''}</h3>
                        <div class="card-tools">
                            <ul class="nav nav-pills ml-auto">



                                <li class="nav-item ml-2">
                                    {$param::NAME_TYPE[$param->valueType]}
                                </li>
                                <li class="nav-item ml-2">
                                  {*  <button type="button" data-toggle="modal" data-target="#editNameModal" class="btn  btn-outline-warning " data-id="{$param->id}" data-what="param" data-val="{$param->name}" title="Editace názvu"><i class="fas fa-edit"></i>
                                    </button>*}
                                     <li class="nav-item  ml-2">
                                                                        <div class="">
                                                                            <label class="control-label"
                                                                                   for="luigiBoxParam{$param->id}"><input type="checkbox"
                                                                                   class="form-control form-control-sm luigiBoxParam"
                                                                                   id="luigiBoxParam{$param->id}"
                                                                                   data-idparam="{$param->id}"
                                                                                   {if $param->luigiBox}checked{/if}>
                                                                            Luigi</label>
                                                                        </div>
                                                                    </li>
                <li class="nav-item  ml-2">
                                    <a n:href="Param:Detail $param->id" class="btn  btn-outline-warning "><i class="fas fa-edit"></i></a>

                                </li>
                                <li class="nav-item ml-2">
                                    <a data-naja-history="off" class="ajax  btn btn-outline-danger" data-hide="#param{$param->id}" data-confirm="xxx" href="#"> <i class="fas fa-times"></i></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body" style="width: 32rem;"  n:if="$param->valueType===$param::TYPE_SELECT">
                    <table class="table table-bordered w-100 table-sm ml-5">
                        <tbody>
                        <tr n:foreach="$param->paramValues as $value" id="paramValue{$value->id}">
                            <td>{$value->val}</td>
                            <td class="text-center" style="width: 55px"> <button type="button" data-toggle="modal" data-target="#editNameModal" class="btn btn-xs btn-outline-warning " data-id="{$value->id}" data-what="paramValue" data-val="{$value->val}" title="Editace názvu"><i class="fas fa-edit"></i>
                                </button></td>
                            <td class="text-center" style="width: 55px">
                                <a data-naja-history="off" class="ajax btn btn-xs btn-outline-danger" data-hide="#paramValue{$value->id}" data-confirm="Smazat parametr" href="#">
                                    <i class="fas fa-times"></i></a>
                            </td>
                        </tr>

                        </tbody>
                    </table>
                    </div>
                    <div class="card-footer" n:if="$param->valueType===$param::TYPE_SELECT">
                        <div class="form-inline">

                            <div class="form-group mx-sm-3 mb-2">
                                <label for="inputAddParamValue" class="sr-only">Parametr</label>
                                <input type="text" class="form-control" id="inputAddParamValue{$param->id}" data-idparamval="{$param->id}" placeholder="Hodnota">
                            </div>
                            <button type="submit" class="btn btn-primary mb-2 btnAddParamValue" data-idparamval="{$param->id}">
                                Přidat
                            </button>
                        </div>
                    </div>
                </div>
                {/foreach}

            </div>
             <div class="card-footer">
             <h4>Přidaní existujicího parametru</h4>
             <input type="text" class="form-control">
             <h4 class="mt-3">Přidaní nového parametru</h4>
                    {control  addParamForm}
             <h4 class="mt-3">Import podle produktu v kategorii</h4>
             <a n:href="importParamFromProduct! $selectCatalog->id" class="btn btn-warning">Importovat</a>
                </div>
        </div>
    </div>

</div>


    <div class="modal fade" id="editNameModal" tabindex="-1" aria-labelledby="editNameModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Editace Názvu</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input class="form-control" type="text" id="inputEditName" placeholder="Default input">
                    <input type="hidden" id="what">
                    <input type="hidden" id="id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Zavřit</button>
                    <button type="button" id="saveEditName" class="btn btn-primary" data-dismiss="modal">Uložit</button>
                </div>
            </div>
        </div>
    </div>


{/block}
{block scripts}
{include parent}
 <script>

        function initSortable() {


           $('*[data-sortable]').sortable(
                {
                    update: function (event, ui) {
                        var data = $(this).sortable('toArray');
                        data = encodeURIComponent(data);
                        console.dir(data);
                        naja.makeRequest('POST', $(this).data("sortable"), { sortData : data})
                    }
                });

        }



      $(function () {
        initSortable();
            $('body').on('click', '.btnAddParamValue', function () {

                naja.makeRequest('POST', {link addParamValue!}, {
                    val: $('input[data-idparamval=' + $(this).data("idparamval") + ']').val(),
                    idparam: $(this).data("idparamval")
                });


            });

             $('#editNameModal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget)
                var recipient = button.data('what')
                var modal = $(this)
                modal.find('#id').val(button.data('id'))
                modal.find('#what').val(button.data('what'))
                modal.find('#inputEditName').val(button.data('val'))
            });

             $('body').on('click', '#saveEditName', function () {

                naja.makeRequest('POST', {link editName!}, {
                    what: $('#what').val(),
                    idelement: $('#id').val(),
                    val: $('#inputEditName').val()
                },
                {
                    history:false
                });

            });
              $('body').on('change', '.luigiBoxParam', function () {
                if ($(this).is(":checked")) {
                    var val = 1;
                } else {
                    var val = 0;
                }

                naja.makeRequest('POST', {link setLuigi!}, { idparam : $(this) . data('idparam'), val: val});
            });





 });
     </script>
{/block}