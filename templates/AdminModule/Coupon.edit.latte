{block #content}
  <script type="text/javascript">
  var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatId.js"></script>

  <script>
      $(function() {
          $( ".proAutocomlete" ).autocomplete({
              source: {$baseUrl} + "/administrace/product/autocomplete-products",
              minLength: 2,
              select: function( event, ui ) {
                  if (ui.item) {
                      var id = this.id;
                      var newId = "";
                      this.value = ui.item.value;
                      newId = id.replace('proname', 'proid');
                      $("#"+newId).val(ui.item.id);
                      $("#"+id).val(ui.item.label);
                  }
                  return false;
              }
          });
      });
  </script>

  <h3>{block #title}Slevový kupón{/block}</h3>
  Název kategorie: <input type="text" id="catname" size="100" {if isset($catPath)}value="{$catPath}"{/if}>
  <a href="#" onclick="return clearAc();"> <img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a>
  <br>
  {control editForm}
{/block}
