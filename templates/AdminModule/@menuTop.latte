{var $orderActive = (
  $presenter->name == 'Admin:Order' ||
  $presenter->name == 'Admin:CreditNote' ||
  $presenter->name == 'Admin:OrderArchiv' ||
  $presenter->name == 'Admin:DeliveryMode' ||
  $presenter->name == 'Admin:Eet' ||
  ($presenter->name == 'Admin:Enumcat' && $presenter->getParameter('typid') == 1) ||
  $presenter->name == 'Admin:Discount' ||
  $presenter->name == 'Admin:Coupon' ||
  $presenter->name == 'Admin:OrdService' ||
  $presenter->name == 'Admin:Cashdesk'
)}
{var $productActive = (
  $presenter->name == 'Admin:Product' ||
  $presenter->name == 'Admin:Insurance' ||
  $presenter->name == 'Admin:RecycleFee' ||
  $presenter->name == 'Admin:Manufacturer'
)}
{var $CatalogActive = (
  $presenter->name == 'Admin:Catalog' ||
  $presenter->name == 'Admin:CatPrice'
)}
{var $importActive = (
  $presenter->name == 'Admin:Import' ||
  $presenter->name == 'Admin:ImportVendor' ||
  $presenter->name == 'Admin:Mall' ||
  $presenter->name == 'Admin:Kaufland'
)}
{var $stockActive = (
  $presenter->name == 'Admin:Delivery' ||
  $presenter->name == 'Admin:DelItemsImeis' ||
  $presenter->name == 'Admin:Vendor' ||
  $presenter->name == 'Admin:StoreOper' ||
  $presenter->name == 'Admin:StoreTransfer' ||
  $presenter->name == 'Admin:Store'
)}

{* menu se zobrazi jen prihlasenym *}
{if $identity->isLoggedIn()}

<div>
{php
  if ($presenter->name != 'Admin:Product' && $presenter->action == 'edit') {
    $thisLink = $presenter->link('default');
  } else {
    $thisLink = $presenter->link('this');
  }
}

</div>


<div id="menu-top">
  {if $admin->admrole != 'prodavac'}<span style="background-color: #e62f3f;"><a href="https://www.hrackycom.cz{$thisLink}">{$serverNames["web1"]}</a></span>{/if}
  <span style="background-color: #c19c41;"><a href="https://www.shopcom.cz{$thisLink}">{$serverNames["web2"]}</a></span>
  <span style="background-color: #529bce;"><a href="https://www.sccom.cz{$thisLink}">{$serverNames["web3"]}</a></span>
  {if $admin->admid > 0}
      <a href="{plink Admin:edit $admin->admid}">Přihlášen/a: {$admin->admname} ({$admin->admmail})</a>
  {/if}

  <br>
  <br>
  <span><a n:class="isLinkCurrent() ? current" n:href="Admin:default">Úvod</a></span>
  {if $identity->isAllowed('Admin:Catalog')}<span><a n:class="isLinkCurrent('Catalog:*') ? current" n:href="Catalog:default">Katalog</a></span>{/if}
  {if $identity->isAllowed('Admin:Product', 'default')}<span><a n:class="isLinkCurrent('Product:*') ? current" n:href="Product:default">Zboží</a></span>{/if}
  {if $identity->isAllowed('Admin:Param', 'default')}<span><a  n:href="Param:edit">Parametry</a></span>{/if}
  {if $identity->isAllowed('Admin:Mailing')}<span><a n:class="isLinkCurrent('Mailing:*') ? current"  n:href="Mailing:default">Mailování</a></span>{/if}
  {if $identity->isAllowed('Admin:Order', 'default')}<span><a  n:class="isLinkCurrent('Order:*') ? current"  n:href="Order:default">Objednávky</a></span>{/if}
  {if $identity->isAllowed('Admin:Delivery')}<span><a n:class="isLinkCurrent('Delivery:*') ? current" n:href="Delivery:default">Sklad</a></span>{/if}
  {if !$identity->isAllowed('Admin:Delivery')}
  {if $identity->isAllowed('Admin:StoreTransfer')}<span><a n:class="isLinkCurrent() ? current" n:href="StoreTransfer:default">Skladové přesuny</a></span>{/if}
  {/if}
  {if $identity->isAllowed('Admin:New')}<span><a  n:class="isLinkCurrent() ? current" n:href="New:default">Novinky</a></span>{/if}
  {if $identity->isAllowed('Admin:Complaint')}<span><a  n:class="isLinkCurrent() ? current" n:href="Complaint:default">Reklamace</a></span>{/if}
  {if $identity->isAllowed('Admin:User')}<span><a  n:class="isLinkCurrent('User:*') ? current" n:href="User:default">Zákazníci</a></span>{/if}
  {if $identity->isAllowed('Admin:MenuIndex')}<span><a n:class="isLinkCurrent('MenuIndex:*') ? current" n:href="MenuIndex:default">Reklama úvodka</a></span>{/if}
  {if $identity->isAllowed('Admin:Menu')}<span><a n:class="isLinkCurrent('Menu:*') ? current" n:href="Menu:default">Menu</a></span>{/if}
  {if $identity->isAllowed('Admin:Page')}<span><a   n:class="isLinkCurrent('Page:*') ? current"  n:href="Page:default">Stránky/Bloky</a></span>{/if}
  {if $identity->isAllowed('Admin:Import')}<span><a   n:class="isLinkCurrent('Import:*') ? current"  n:href="Import:default">Import/Export</a></span>{/if}
  {if $identity->isAllowed('Admin:Accounting')}<span><a  n:class="isLinkCurrent('Accounting:*') ? current" n:href="Accounting:default">Účetnictví</a></span>{/if}
  {if $identity->isAllowed('Admin:Config')}<span><a  n:class="isLinkCurrent('Admin:Config') ? current"  n:href="Config:default">Nastavení</a></span>{/if}
  {if $identity->isAllowed('Admin:Dictionary')}<span><a  n:class="isLinkCurrent('Admin:Dictionary') ? current" n:href="Dictionary:default">Překlady</a></span>{/if}
  <span><a n:href="Admin:edit $admin->admid">Můj účet</a></span>
  <span><a n:href="Admin:logout">Odhlásit se</a></span>
</div>

{* submenu stranky *}
{if $CatalogActive}
<div id="submenu">
  <span><a {if $presenter->isLinkCurrent("Catalog:default")} class="current"{/if} href="{plink Catalog:default}">Výpis stromu</a></span>
  <span><a {if $presenter->isLinkCurrent("Catalog:edit, 0")} class="current"{/if} href="{plink Catalog:edit, 0}">Nová kořenová úroveň</a></span>
  <span><a {if $presenter->isLinkCurrent("CatPrice:default")} class="current"{/if} href="{plink CatPrice:default}">Ceny dle katalogu</a></span>
</div>
{/if}

{* submenu zbozi *}
{if $productActive}
<div id="submenu">
  {if $identity->isAllowed('Admin:Product', 'default')}<span><span><a {if $presenter->isLinkCurrent("Product:default")}class="current"{/if} href="{plink Product:default}">Seznam zboží</a></span>{/if}
  {if $identity->isAllowed('Admin:Product', 'edit')}<span><span><a {if $presenter->isLinkCurrent("Product:edit, 0")}class="current"{/if} href="{plink Product:edit, 0}">Založit nové zboží</a></span>{/if}
  {if $identity->isAllowed('Admin:Manufacturer', 'default')}<span><a {if $presenter->isLinkCurrent("Manufacturer:default")}class="current"{/if} href="{plink Manufacturer:default}">Výrobci</a></span>{/if}
  {if $identity->isAllowed('Admin:Product', 'eanCheck')}<span><a {if $presenter->isLinkCurrent("Product:eanCheck")}class="current"{/if} href="{plink Product:eanCheck}">Kontrola EANu</a></span>{/if}
  {if $identity->isAllowed('Admin:Product', 'basketConfig')}<span><a {if $presenter->isLinkCurrent("Product:basketConfig")}class="current"{/if} href="{plink Product:basketConfig}">Nastavení košíku</a></span>{/if}
  {if $identity->isAllowed('Admin:Insurance', 'default')}<span><a {if $presenter->isLinkCurrent("Insurance:default")}class="current"{/if} href="{plink Insurance:default}">Pojištění</a></span>{/if}
  {if $identity->isAllowed('Admin:Product', 'SearchStats')}<span><a {if $presenter->isLinkCurrent("Product:SearchStats")}class="current"{/if} href="{plink Product:SearchStats}">Statistika hledání</a></span>{/if}
  {if $identity->isAllowed('Admin:RecycleFee', 'default')}<span><a {if $presenter->isLinkCurrent("RecycleFee:default")}class="current"{/if} href="{plink RecycleFee:default}">Recyklační příspěvky</a></span>{/if}
</div>
{/if}

{* submenu sklad *}
{if $stockActive}
<div id="submenu">
  {if $identity->isAllowed('Admin:Delivery')}<span><a {if $presenter->isLinkCurrent("Delivery:default")}class="current"{/if} href="{plink Delivery:default}">Dodávky</a></span>{/if}
  {if $serverId != 'web1'}{if $identity->isAllowed('Admin:StoreTransfer')}<span><a {if $presenter->isLinkCurrent("StoreTransfer:default")}class="current"{/if} href="{plink StoreTransfer:default}">Skladové přesuny</a></span>{/if}{/if}
  <!--{if $identity->isAllowed('Admin:StoreOper')}<span><a {if $presenter->isLinkCurrent("StoreOper:default")}class="current"{/if} href="{plink StoreOper:default}">Skladové přesuny</a></span>{/if}-->
  {if $identity->isAllowed('Admin:DelItemsImeis')}<span><a {if $presenter->isLinkCurrent("DelItemsImeis:default")}class="current"{/if} href="{plink DelItemsImeis:default}">IMEI</a></span>{/if}
  {if $identity->isAllowed('Admin:Store')}<span><a {if $presenter->isLinkCurrent("Store:writeOff")}class="current"{/if} href="{plink Store:writeOff}">Odpis ze skladu</a></span>{/if}
  {if $identity->isAllowed('Admin:Store')}<span><a {if $presenter->isLinkCurrent("Store:default")}class="current"{/if} href="{plink Store:default}">Sklady</a></span>{/if}
  {if $identity->isAllowed('Admin:Store')}<span><a {if $presenter->isLinkCurrent("Store:productsNotFilled")}class="current"{/if} href="{plink Store:productsNotFilled}">Doobjednat</a></span>{/if}
  {if $identity->isAllowed('Admin:Store')}<span><a {if $presenter->isLinkCurrent("Store:storeContent")}class="current"{/if} href="{plink Store:storeContent}">Obsah skladů</a></span>{/if}
  {if $identity->isAllowed('Admin:Store')}<span><a {if $presenter->isLinkCurrent("Store:storeContentToDate")}class="current"{/if} href="{plink Store:storeContentToDate}">Obsah skladu k datu</a></span>{/if}
  {if $identity->isAllowed('Admin:Vendor')}<span><a {if $presenter->isLinkCurrent("Vendor:default")}class="current"{/if} href="{plink Vendor:default}">Dodavatelé</a></span>{/if}
</div>
{/if}

{* submenu objednavky *}
{if $orderActive}
<div id="submenu">
{if $identity->isAllowed('Admin:Order', 'default')}<span><a n:class="isLinkCurrent('Order:*') ? current" n:href="Order:default">Seznam objednávek</a></span>{/if}
{if $identity->isAllowed('Admin:DeliveryMode', 'default')}<span><a {ifCurrent DeliveryMode:default}class="current"{/ifCurrent} href="{plink DeliveryMode:default}">Způsoby dodání</a></span>{/if}
{if $identity->isAllowed('Admin:Order', 'quick')}<span><a {ifCurrent Order:quick}class="current"{/ifCurrent} href="{plink Order:quick}">Rychlá objednávka</a></span>{/if}
{if $identity->isAllowed('Admin:Order', 'stats')}{if $admin->admid == 2 || $admin->admid == 3 || $admin->admid == 112 || $admin->admid == 5}<span><a {ifCurrent Order:stats}class="current"{/ifCurrent} href="{plink Order:stats}">Statistika</a></span>{/if}{/if}
{if $identity->isAllowed('Admin:Order', 'proSaleStat')}{if $admin->admid == 2 || $admin->admid == 3 || $admin->admid == 112 || $admin->admid == 5}<span><a {ifCurrent Order:proSaleStat}class="current"{/ifCurrent} href="{plink Order:proSaleStat}">Statistika prodejnosti po položkách</a></span>{/if}{/if}
{if $identity->isAllowed('Admin:Discount')}<span><a {ifCurrent Discount:default}class="current"{/ifCurrent} href="{plink Discount:default}">Slevy</a></span>{/if}
{if $identity->isAllowed('Admin:CreditNote', 'default')}<span><a {ifCurrent CreditNote:default}class="current"{/ifCurrent} href="{plink CreditNote:default}">Dobropisy</a></span>{/if}
{if $identity->isAllowed('Admin:Coupon')}<span><a n:class="isLinkCurrent('Coupon:*') ? current" n:href="Coupon:campaigns">Slevové kupóny</a></span>{/if}
{if $identity->isAllowed('Admin:Eet')}<span><a n:class="isLinkCurrent('Eet:*') ? current" href="{plink Eet:default}">EET</a></span>{/if}
{if $identity->isAllowed('Admin:Cashdesk', "default")}<span><a n:class="isLinkCurrent('Cashdesk:*') ? current" href="{plink Cashdesk:default}">Pokladna</a></span>{/if}
{if $identity->isAllowed('Admin:OrdService')}<span><a n:class="isLinkCurrent('OrdService:*') ? current" href="{plink OrdService:default}">Doplňkové služby</a></span>{/if}
</div>
{/if}

{* submenu stranky *}
{if isLinkCurrent('Page:*')}
<div id="submenu">
  <span><a n:class="isLinkCurrent() ? current"  href="{plink Page:default}">Seznam textových stránek</a></span>
  <span><a n:class="isLinkCurrent() ? current"  href="{plink Page:edit, 0}">Nová stránka</a></span>
  <span><a n:class="isLinkCurrent() ? current" href="{plink Page:defaultBlock}">Seznam textových bloků</a></span>
  <span><a  n:class="isLinkCurrent() ? current"  href="{plink Page:edit 'id'=>0, 'pagblock'=>1}">Nový blok</a></span>
</div>
{/if}

{* submenu nastaveni *}

{if isLinkCurrent('Config:*')}
<div id="submenu">
  <span><a {if $presenter->isLinkCurrent("Config:default")}class="current"{/if} href="{plink Config:default}">Nastavení</a></span>
  {*<span><a {if $presenter->isLinkCurrent("Config:enums")}class="current"{/if} href="{plink Config:enums}">Číselníky</a></span>*}
  <span><a {if $presenter->isLinkCurrent("Admin:list")}class="current"{/if} href="{plink Admin:list}">Správci</a></span>
  <span><a {if $presenter->isLinkCurrent("User:edit, 1")}class="current"{/if} href="{plink User:edit, 1}">Firma do 31.7.2018</a></span>
  <span><a {if $presenter->isLinkCurrent("User:edit, 10678")}class="current"{/if} href="{plink User:edit, 10678}">Firma od 1.8.2018</a></span>
</div>
{/if}

{* submenu nastaveni *}
{if $presenter->isLinkCurrent("Mailing:*") || $presenter->isLinkCurrent("Ecomail:*")}
<div id="submenu">
  <span><a {if $presenter->isLinkCurrent("Ecomail:default")}class="current"{/if} href="{plink Ecomail:default}">Ecomail</a></span>
  <span><a {if $presenter->isLinkCurrent("Mailing:default")}class="current"{/if} href="{plink Mailing:default}">Původní mailování</a></span>
</div>
{/if}

{/if}

{* submenu import  *}
{if $importActive}
  <div id="submenu">

    {if $identity->isAllowed('Admin:Import')}<span><a n:class="isLinkCurrent('Import:*') ? current" href="{plink Import:default}">Import / export</a></span>{/if}

    {php
      $vendorsInMenu = ["nosreti", "atc", "fast", "bal", "bakr", "willi", "dexon", "homelux", "ird", "cattara", "autohaus", "novaservis", "bsacoustic", "partnertel", "niceboy", "spokey", "easyoffice", "madalbal", "east", "penta", "tsb", "matyska", "mujperfekt", "placek", "hservice"];
    }

    {foreach $vendorsInMenu as $vendorId}
      {if $identity->isAllowed('Admin:ImportVendor')}<span {if $vendors[$vendorId]->store_off == 1}style="background: #e58d61"{/if}><a {if $presenter->isLinkCurrent("ImportVendor:default")}class="current"{/if} href="{plink ImportVendor:default "type"=>$vendorId}">{$vendorId|firstUpper}</a> {include #hSign vendorId: $vendorId}</span>{/if}
    {/foreach}

    {if $identity->isAllowed('Admin:ImportVendor')}<span><a {if $presenter->isLinkCurrent("ImportVendor:products")}class="current"{/if} href="{plink ImportVendor:products}">Editace naimportovaného zboží</a></span>{/if}

    {if $identity->isAllowed('Admin:Mall')}<span style="background: #e58d61"><a {if $presenter->isLinkCurrent("Mall:default")}class="current"{/if} href="{plink Mall:default}">Mall</a></span>{/if}
    {if $identity->isAllowed('Admin:Kaufland')}<span style="background: #e58d61"><a {if $presenter->isLinkCurrent("Kaufland:default")}class="current"{/if} href="{plink Kaufland:default}">Kaufland</a></span>{/if}
  </div>
{/if}

{if $identity->isLoggedIn()}
<form style="background: #6c6c6c; color: white"  action="{plink Order:fillOrderStore}" method="get">
  Vyskladňovací objednávka: <input type="text" name="ordcode">
</form>
{/if}

{define #hSign}
  {if !App\vendors\ImportVendorBase::isAllowedByAggregator($vendorId)}
    <small><s title="vyřazeno z Heureka ověřeno">H</s></span></small>
  {/if}
{/define}