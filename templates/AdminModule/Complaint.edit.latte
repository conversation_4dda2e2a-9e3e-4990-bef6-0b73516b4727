{block content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'proid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

  <h3>{block #title} Editace reklamace {/block}</h3>
  {if !empty($dataRow->copinvcode)}<p>Prodejní faktura: <a target="invoicePrint" href="{plink printInvoice, $dataRow->copid, 'P'}">{$dataRow->copinvcode} <img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>  | <a href="{plink printInvoice, $dataRow->copid, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a></p>{/if}
  <p>{ifset $delivery}<a href="{plink Delivery:edit $delivery->delid}" target="_blank">Dodávka {if !empty($delivery->delinvcode)} | fa: {$delivery->delinvcode}{/if}{if !empty($vendor->venname)} | dod: {$vendor->venname}{/if}</a>{/ifset}</p>
  <p>
  <a href="{plink default}">Zpět na seznam</a> |
  {if $id > 0 && $dataRow->coporiid > 0}<a href="{plink goOrder $dataRow->copid}">Na objednávku</a>{/if}
  {if $id > 0}
  | Protokol: <a target="complaintPrint" href="{plink Complaint:print, $dataRow->copid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>
  <a href="{plink Complaint:print, $dataRow->copid, 0, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a>
   | Pro servis: <a target="complaintPrint" href="{plink Complaint:print, $dataRow->copid, 1}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>
  <a href="{plink Complaint:print, $dataRow->copid, 1, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a>
  {/if}
  </p>
  {if $id > 0}
  <form action="{plink changeStatus}" method="get">
    <input type="hidden" name="copid" value="{$id}">

    {foreach $_GET as $key => $value}
      {php
        $key = htmlspecialchars($key);
        $value = htmlspecialchars($value);
      }
      <input type='hidden' name='{$key}' value='{$value}'/>
    {/foreach}

    <select name="newstatus">
      {foreach $enum_copstatus as $key => $name}
        <option value="{$key}" {if $key===$dataRow->copstatus}selected{/if}>{$name}</option>
      {/foreach}
    </select>
    <input type="submit" value="Změnit status">
  </form>
  {/if}

  {control editForm}
  {control addNoteForm}

  {if count($attachments) > 0}
    <h3>Přílohy</h3>
    {foreach $attachments as $file}
      {if $iterator->first}
      <table class="table table-condensed table-hover table-bordered">
      {/if}
        <tr>
          <td><a href="{plink getFile $file["fullPath"] . $file["fileName"]}">{$file["fileName"]}</a></td>
          <td><a href="{plink deleteFile $dataRow->copid, $file["fullPath"] . $file["fileName"]}">[ vymazat ]</a></td>
        </tr>
      {if $iterator->last}
      </table>
      {/if}
    {/foreach}
  {/if}

  {ifset $statusLog}
  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie reklamace</h3>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->orldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_copstatus[$row->orlstatus]}</td>
    <td>{$row->orladmname}</td>
    <td>{$row->orlnote|noescape}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  {/ifset}
{/block}
