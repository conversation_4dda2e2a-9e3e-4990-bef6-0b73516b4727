{block #content}
  {if $needRecalculateCnt > 0}<p><PERSON><PERSON><PERSON> {$needRecalculateCnt} položek čeká na přepočtení ceny</p>{/if}
  <h3>{block #title}Import od dodavatele {if !empty($type)}{$type|firstUpper}{/if}{/block}</h3>

  {if !empty($type)}
  <p style="font-size: medium">
    <a href="{plink //:Front:ImportVendor:updatePrices, $type, 1}&forceUpdate=1">Aktualizovat ceny</a> |

    {if $type == 'penta'}
      <a href="{plink //:Front:ImportVendor:updatePricesEu, $type, 1}&forceUpdate=1">Aktualizovat ceny EU</a> |
    {/if}
    <a href="{plink //:Front:ImportVendor:updateAccess, $type, 1}&forceUpdate=1">Aktualizovat stavy skladu</a> |
    <a href="{plink //:Front:ImportVendor:update, $type, 1}&forceUpdate=1">Aktualizovat položky</a>
    {if $type === 'atc'}
    | <a href="{plink //:Front:ImportVendor:removedAtc, $type, "", 0, 1}">Zkontrolovat vyřazené položky</a>

    | <a href="{plink //:Front:ImportVendor:paramAtc, $type,1}">Import Parametru</a>
     | <a href="{plink //:Front:ImportVendor:paramAtcValue, $type,1}">Import Hodnot parametru</a>
    | <a href="{plink //:Front:ImportVendor:paramAtcCatalog, $type,1}">Import Parametru do kategorii</a>
    {/if}
    {if $type === 'bal'}
    | <a href="{plink //:Front:ImportVendor:UpdateProDesc, $type, 1}&forceUpdate=1">Aktualizovat popisky </a>
    {/if}
    | <a href="{plink forceUpdatePrices, $type}">Vynutit aktualizaci ceny všech položek</a>
  </p>
  {/if}

  <p>
    <strong>Počet naimportovaných produktů připravených k úpravám: </strong> {$productsCount}<br>
    <strong>Počet obrázků ve frontě: </strong> {$imagesCount} (obrázky se přenesou společně s produkty)<br>
  </p>

  {if !empty($type)}
      {if $type==='fast'}
        <h3>Aktualizovat z datového souboru (*.xlsx)</h3>
        <p>Formát importního souboru ve formátu *.xlsx. Pokud máte *.xls je třeba před importem otevřít ceník a "Uložit jako ..." .xlsx.</p>
        {control updateFastForm}
      {elseif $type==='bal'}
        <h3>Aktualizovat ceny z datového souboru  (*.csv)</h3>
        {control updateBalCzForm}
      {elseif $type==='cattara'}
          <br>
          <strong>Spustit import pro kategorii:</strong>
          <table>
          <tr>
            <td>
              <a href="{plink importByCatalog, 'auto'}">Autodoplňky</a> |
              <a href="{plink //:Front:ImportVendor:updateByCatalog, $type, 'auto', 1}" title="aktualizuje údaje o zboží jen v ostrém">&#10149;</a>
            </td>
          </tr><tr>
            <td>
              <a href="{plink importByCatalog, 'camp'}">Cattara (camping, outdoor)</a> |
              <a href="{plink //:Front:ImportVendor:updateByCatalog, $type, 'camp', 1}" title="aktualizuje údaje o zboží jen v ostrém">&#10149;</a>
            </td>
          </tr><tr>
            <td>
              <a href="{plink importByCatalog, 'nara'}">Nářadí</a> |
              <a href="{plink //:Front:ImportVendor:updateByCatalog, $type, 'nara', 1}" title="aktualizuje údaje o zboží jen v ostrém">&#10149;</a>
            </td>
          </tr><tr>
            <td>
              <a href="{plink importByCatalog, 'gast'}">Gastro</a> |
              <a href="{plink //:Front:ImportVendor:updateByCatalog, $type, 'gast', 1}" title="aktualizuje údaje o zboží jen v ostrém">&#10149;</a>
            </td>
          </tr>
          </table>
        <br>
        <strong>Aktualizace cen (*.xlsx):</strong> Pozor! Ceník bude uložen a ceny se budou na jeho základě aktualizovat, dokud zde nenahrajete aktuálnější ceník. Dodavatel dodává ceník v *.xls,je třeba převést do *xlsx
        {control uploadPricelistForm}
      {elseif $type==='niceboy'}
        <h4><strong>Aktualizace cen (*.xlsx):</strong> Pozor! Ceník bude uložen a ceny se budou na jeho základě aktualizovat, dokud zde nenahrajete aktuálnější ceník.</h4>
        {control uploadPricelistForm}
      {elseif $type==='madalbal'}
        <h4><strong>Aktualizace cen (*.csv):</strong> Pozor! Ceník bude uložen a ceny se budou na jeho základě aktualizovat, dokud zde nenahrajete aktuálnější ceník.</h4>
        {control uploadPricelistForm}

      {elseif $type==='partnertel' || $type==='tsb'}
        <h4><strong>Hledat položku ve feedu dodavatele</h4>
        {control searchItemInXmlForm}
      {/if}

    {if $type !== 'fast' && $type !== 'cattara'}
    <p style="font-size: medium">
      1. Stáhnout aktuální importní data <a href="{plink download}">stáhnout</a><br>
      2. Importovat stažené data:
      {if $dataExists}
        {if $type == 'atc' || $providerType=='i6' || $type=='partnertel' || $type=='tsb' || $type=='matyska' || $type=='placek'}
          {if!empty($catalogs)}
            {if $providerType!=='i6'}
              <a href="{plink import}">spustit základní import</a><br>
            {/if}
            {foreach $catalogs as $code=>$name}
              {if $iterator->first}
              <br>
              <strong>Spustit import pro kategorii:</strong>
              <table>
              {/if}
                <tr>
                  <td>
                    <a href="{plink importByCatalog, $code}">{if empty($name)}nevyplněn{else}{$name}{/if}</a> |
                    <a href="{plink //:Front:ImportVendor:updateByCatalog, $type, $code, 1}&forceUpdate=1" title="aktualizuje údaje o zboží jen v ostrém">&#10149;</a>
                    {if $type === 'atc'}
                    | <a href="{plink //:Front:ImportVendor:updateWeightByCatalog, $type, $code, 1}&forceUpdate=1" title="aktualizuje hmotnost zboží jen v ostrém">&#9878;</a>
                    | <a href="{plink //:Front:ImportVendor:removedAtc, $type, $code, 1000, 1}&forceUpdate=1" title="Zkontrolovat vyřazené položky">&#9745;</a>
                    {/if}
                  </td>
                </tr>
              {if $iterator->last}
              </table>
              {/if}
            {/foreach}
          {else}
            <strong>data nebyla ještě stažena</strong>
          {/if}
        {elseif $dataExists}
          <a href="{plink import}">spustit import</a>
        {/if}
      {else}
        <strong>data nebyla ještě stažena</strong>
      {/if}
    </p>
    {/if}
  {/if}
  <hr>
  <a href="{plink //:Admin:ImportVendor:crons, $type}">Historie aktualizací</a>

{if $badPrice && count($badPrice) > 0}
  <h3>Ceny pod nákupku - max 20 položek</h3>
  <table class="grid">
    <tr>
      <th>Kód</th>
      <th>Název</th>
      <th>Dodavatel</th>
      <th>Cena A shopcom</th>
      <th>Cena C shopcom</th>
      <th>Cena D shopcom</th>
      <th>Cena A sccom</th>
      <th>Cena C sccom</th>
      <th>Cena D sccom</th>
    </tr>
{foreach $badPrice as $item}
<tr>
  <td>{$item->procode}</td>
  <td><a href="{plink Product:edit $item->proid}">{$item->proname}</td>
  <td>{$item->provenid}</td>
  <td>{$item->propricea_web2}</td>
  <td>{$item->propricec_web2}</td>
  <td>{$item->propriced_web2}</td>
  <td>{$item->propricea_web3}</td>
  <td>{$item->propricec_web3}</td>
  <td>{$item->propriced_web3}</td>
</tr>
{/foreach}
  </table>
{/if}