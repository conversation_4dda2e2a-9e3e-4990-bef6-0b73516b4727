{varType App\Orm\Catalog $catalog}
{block content}
  <script type="text/javascript">
  var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatId.js"></script>

  <h3>{block #title}Editace katalogu{/block}</h3>
  {if isset($imagePath) }<img src="{$baseUrl}/{$imagePath}" />{/if}
  Nadřízená úroveň kategorie:
  <input type="text" id="catname" size="100" {if isset($catPath)}value="{$catPath}"{/if}>
  <a href="#" onclick="return clearAc();"> <img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a>
  <br>

  Kategorie s příslušenstvím:
  <input type="text" id="catname2" size="100" {if isset($catPath2)}value="{$catPath2}"{/if}>
  <a href="#" onclick="return clearAc2();"> <img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a>
  <br>

  Kategorie Mall:
  <input type="text" id="catnamemall" size="100" {if isset($catPathMall)}value="{$catPathMall}"{/if}>
  <a href="#" onclick="return clearAc2();"> <img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a>
  <br>

  {control catalogEditForm}

  <h3>Ceny dle katalogu a výrobce</h3>
  <p>Ceny uvádějte s DPH.</p>
  {form mcpAddForm}
    {label manufacturer /} {input manufacturer}
    {label priceFrom /} {input priceFrom}
    {label priceTo /} {input priceTo}
    {label margeWeb2 /} {input margeWeb2}
    {label margeWeb3 /} {input margeWeb3}
    {input submit}
  {/form}


    <table class="grid" n:if="$catalog->getMarginManufacturer()->count()">
      <tr>
        <th>Výrobce</th>
        <th>Cena od</th>
        <th>Cena do</th>
        <th>Web</th>
        <th>Marže</th>
        <th></th>
      </tr>
      <tr n:foreach="$catalog->getMarginManufacturer() as $margin">
        <td>{$margin->manufacturer->name}</td>
        <td>{$margin->priceFrom|formatPrice}</td>
        <td>{$margin->priceTo|formatPrice}</td>
        <td>{$margin->web->name}</td>
        <td>{$margin->margin}%</td>
        <td><a n:href="deleteMcp $margin->id, $catalog->id"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" /></a> </td>
      </tr>
    </table>





    <h4>Nastavení marží v katalogové cestě</h4>
    <table class="grid">
      <tr>
        <th>Katalogová cesta</th>
        <th>Marže dle značky</th>
        <th></th>
      </tr>

      <tr n:foreach="$catalog->getTree() as $catalogParrent">
        <td>{$catalogParrent->catpath}</td>
        <td>
          {if $catalogParrent->getMarginManufacturer()->count()}

              <table class="grid" >
                <tr n:foreach=" $catalogParrent->getMarginManufacturer() as $margin">
                  <td>{$margin->manufacturer->name}</td>
                  <td>od {$margin->priceFrom|formatPrice}</td>
                  <td>do {$margin->priceTo|formatPrice}</td>
                  <td>{$margin->web->name}</td>
                  <td>{$margin->margin}%</td>
                </tr>
              </table>

          {else}
          nevyplněno
          {/if}
        </td>
        <td>
          <a href="{plink Catalog:edit, $catalogParrent->id}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" /></a>
        </td>
      </tr>
    </table>



  <script type="text/javascript" src="{$baseUrl}/js/textarea_maxlen.js"></script>
