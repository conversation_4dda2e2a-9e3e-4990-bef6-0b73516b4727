{varType App\Orm\ServicePrice[] $prices}
{varType array $enum_insVarId}
{block #title}Pojištění{/block}
{block #content}
  <h3>Pojištění</h3>
  <a href="{plink editPriceList}">Editovat ceník</a>


{form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label serviceid /} {input serviceid}

  {label insvarid /} {input insvarid}
    <br />
  {input search}
  {input clear}
  </fieldset>
  {/form}

{foreach $prices as $price}
  {if $iterator->first}
  <table  class="grid">
    <tr>
      <th>Katalog</th>
      <th>Varianta</th>
      <th>Od</th>
      <th>Do</th>
      <th>Prodejce</th>
      <th>Zákazník</th>
    </tr>
  {/if}
    <tr>
      <td>{$price->service->name}</td>
      <td>{$enum_insVarId[$price->variant]}</td>
      <td style="text-align: right">{$price->priceFrom|formatprice}</td>
      <td style="text-align: right">{$price->priceTo|formatprice}</td>
      <td style="text-align: right">{$price->priceServiceBuy|formatprice}</td>
      <td style="text-align: right">{$price->priceServiceSell|formatprice}</td>
    </tr>
  {if $iterator->last}
  </table>
  {/if}
{/foreach}
{/block}
