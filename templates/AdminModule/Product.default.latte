{block #content}
  <script type="text/javascript">
  var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatIdProductDefault.js"></script>

  <h3>{block #title}Seznam zboží{/block}</h3>
  <p><a href="{plink Product:recalcSaleStat}">Přepočítat statistiky prodejnosti</a></p>
  {form searchForm}
<ul class="errors" n:if="$form->hasErrors()">
        <li n:foreach="$form->errors as $error">{$error}</li>
      </ul>
  <fieldset>
  <legend>Vyhledávání</legend>
  {label code /}: {input code}

  {label name /}: {input name size=>30}
  {label manid /}: {input manid style=>"width: 90px"} {label prccat /}: {input prccat} {label access /}: {input access} {input pricecom} {label pricecom/}<br />
  <span style="padding: 5px; border: solid black 1px">
  Fix:
  {input pricefix:} {label pricefix}cena{/label}
  {input pricefixtemp:} {label pricefixtemp}dočasná cena{/label}
  {input namefix:} {label namefix}název{/label}
  {input manidfix:} {label manidfix}výrobce{/label}
  {input odfix:} {label odfix}dodavatele{/label}
  {input picfix:} {label picfix}obrázek{/label}
  {input descfix:} {label descfix}popis{/label}
  </span>
   &nbsp; {label typid /} {input typid} | {label delfree /}: {input delfree} | {label venid /}: {input venid} | {label status /}: {input status}<br />
  {label catid /}: {input catid size=>3}
  <a href="#" onclick="return clearAc('search');"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a>
  {input catname size=>40} | {input cpcheureka} {label cpcheureka /} | {input googleprice} {label googleprice /} |
  Řazení: {input orderby} {input orderbytype} {label pagerows /}: {input pagerows size=>2} {input search}
  {input clear}<br>
    <span style="padding: 5px; border: solid black 1px">{label gifts /}: {input gifts} {input havegifts} | {label discount /}: {input discount} {label discountunit /}: {input discountunit} | {input havediscount}</span> {input notincat} {label notincat/} | {label kaufland/}: {input kaufland} | {label delcheckoff/}: {input delcheckoff} | {label createdfrom/}: {input createdfrom} do {input createdto}
    <br><span style="padding: 5px; border: solid black 1px">Heuréka {label heko/}: {input heko} {label overenooff /}: {input overenooff } | {label heurekaoff/}: {input heurekaoff}</span>  | {label tanganicaoff/}: {input tanganicaoff}  | {label proadultonly/}: {input proadultonly} | {input saleadept} {label saleadept/} | {input saleignore} {label saleignore/} | {input issetovervalued} {label issetovervalued /} | {input issetdatedisc} {label issetdatedisc /}
  </fieldset>
  {/form}

{control paginator}

{form listEditForm}
<input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše
<div id="batchPriceChange">
<span style="padding: 5px; border: solid black 1px">
{label newprice /} {input newprice} |
{label priceperrem /} {input priceperrem}% |
{label priceperadd /} {input priceperadd}% | {label pricechange /} {input pricechange}Kč | {label propricefixtemp /} {input propricefixtemp} | {input prccat} | {label setovervalued /} {input setovervalued}
</span>
{label qtymin /} {input qtymin}ks | {label manid /}: {input manid style=>"width: 90px"}<br>
<span style="padding: 5px; border: solid black 1px">
  Fix:
  {label propricefix /} {input propricefix}
  {label promanidfix /} {input promanidfix}
  {label pronamefix /} {input pronamefix}
  {label proodfix /} {input proodfix}
  {label propicfix /} {input propicfix}
  {label prodescfix /} {input prodescfix}
</span>
{label status /}: {input status style=>"width: 50px"} | {label proaccess /}: {input proaccess style=>"width: 90px"} | {label prokaufland /} {input prokaufland} | {label prodelcheckoff /} {input prodelcheckoff}<br>
<span style="padding: 5px; border: solid black 1px">Heuréka {label proheko /} {input proheko} | {label prfheurekaoverenooff /} {input prfheurekaoverenooff}
  {label procpcheurekafix /} {input procpcheurekafix}
  {label procpcheureka /} {input procpcheureka}Kč
   | {label proheurekaoff/}: {input proheurekaoff}
</span>
{label protanganicaoff/}: {input protanganicaoff} |
{label proadultonly/}: {input proadultonly} |
{input discountremove} {label discountremove /} | {label discountset /}: {input discountset} {label discountunitset /}: {input discountunitset} <br>
{input orsremove} {label orsremove /} | {label orsset /}: {input orsset} | {label catidto /}:  {input catidto size=>3}
<a href="#" onclick="return clearAc('edit');"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a>
{input catnameto size=>40} | Změnit hodnotu pole: {input updatefield} hodnota: {input updatefieldvalue size=>10}<br>
<span style="padding: 5px; border: solid black 1px">
{input giftsremove} {label giftsremove /} | {label giftsset /}: {input giftsset 'title'=> 'Vyplňte kódy dárků oddělené čárkou. Původní dárky se u položek přepíšou novou hodnotou.'} |
{label giftreplacefrom /}: {input giftreplacefrom 'title'=> 'Vyplňte jen jeden kód dárku, který se má nahradit.'} {label giftreplaceto /}: {input giftreplaceto 'title'=> 'Nový kód dárku. Pokud chcete vymazat dárek, tak nechte prázdné.'} |
{label gifttype /} {input gifttype}
</span>
<br>
{label delfree /}:{input delfree} |
{label protypid /}:{input protypid}  |
{label protypid2 /}:{input protypid2}  |
{label protypid3 /}:{input protypid3}  |
{label protypid4 /}:{input protypid4} |
{label protypid5 /}:{input protypid5} |
{*{label prosaleadept /}:{input prosaleadept} |*}
{input overvalue} (vyplňte jen jednu možnost)
</div>
<a href="#" id="toggleLabels">Zobrazit/skrýt tisk štítků</a>
 <table class="grid">
  <tr>
    <th>&nbsp;</th>
    <th>&nbsp;</th>
    <th>&nbsp;</th>
    <th>Katalogové č.</th>
    <th class="labels">Štít.</th>
    <th>EAN</th>
    <th>Název</th>
    <th>Výrobce</th>
    <th>Nákup s DPH</th>
    <th colspan="2" style="text-align: center;">Cena {$dataRowsPriceCat|upper}</th>
    <th colspan="2" style="text-align: center;">Cena {$dataRowsPriceCatSecond|upper}</th>
    <th colspan="2">Bid</th>
    {if $serverId == 'web1'}
    <th style="text-align: center;" colspan="2">Skladem</th>
    {else}
    <th style="text-align: center;">SCH</th>
    <th style="text-align: center;">OP</th>
    <th style="text-align: center;">HA</th>
    <th style="text-align: center;">OVA</th>
    {/if}
    <th>Dost.</th>
    <th>Status</th>
    <th colspan="4"></th>
    <th colspan="10">FIX</th>
  </tr>
  {foreach $dataRows as $row}
    {php
      $serId = $serverId;
      if ($serId == 'web3') $serId = "web2";
      $picName = WWW_DIR."/pic/".$serId."/product/list/" . ($row|getProPicFileName);
      $fileExists = file_exists($picName);

      $propricefix["a"] = "";
      $propricefix["e"] = "";

      if ($row["propricefix"] == 1) {
        $propricefix["a"] = ' background-color: #FE9A2E; ';
      }

    }

    {var $container = $form[$row->proid]}
    {formContainer $row->proid}
    <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
      <td>{$iterator->getCounter()}</td>
      <td{if $row->protypid5 != 1 && $row->proaccess <= 3} title="zboží na Heureka" style="background-color: #1E90FF"{/if}>{if $row->proheko == 0}x{/if}</td>
      <td{if $row->propricecom > 0} style="background-color: #07cdde;"{/if}><input class="proid_chk" n:name="recalcprice"></td>
      <td{if $row["proodfix"] == 1} title="fixovaná cena" style="background-color: #dad55e"{/if}>{$row->procode}</td>
      <td class="labels">{php echo $container["labQty"]->getControl()} </td>
      <td>{$row->procode2}{if $row["prokaufland"] == 1} <span title="Kaufland" style="background-color: #e10915">K</span>{/if}</td>
      <td{if $row["pronamefix"] == 1} title="fixovaný název" style="background-color: violet"{/if}>{$row->proname}</td>
      <td{if $row["promanidfix"] == 1} title="fixovaný výrobce" style="background-color: #529bce"{/if}>{$row->manname}</td>
      <td style="text-align: right{if isset($isOverValued[$row->proid])};background-color: #885500{/if};">{if $admin->admrole == 'superadmin' && $row->deiprice > 0}{$row->deiprice|formatPrice} {if $row->proprice > 0}<small>({round(($row->proprice-$row->deiprice)/$row->proprice*100,2)}%)</small>{/if}{/if}</td>
      {if $serverId == 'web1'}
        <td style="text-align: right">{$row["proprice".$dataRowsPriceCat."_web1"]|formatPrice}</td>
        <td>{php echo $container["proprice" . $dataRowsPriceCat . "_web1"]->getControl()->addAttributes(array('size'=>5)) }</td>
        <td></td>
        <td></td>
      {elseif $serverId == 'web2'}
        <td style="{$propricefix[$dataRowsPriceCat]|noescape} text-align: right">{$row["proprice".$dataRowsPriceCat."_web2"]|formatPrice}</td>
        <td>{php echo $container["proprice" . $dataRowsPriceCat . "_web2"]->getControl()->addAttributes(array('size'=>5)) }</td>
        <td colspan="2" style="{$propricefix[$dataRowsPriceCatSecond]|noescape} text-align: right">{$row["proprice" . $dataRowsPriceCatSecond . "_web2"]|formatPrice}</td>
      {else}
        <td style="{$propricefix[$dataRowsPriceCat]|noescape} text-align: right">{$row["proprice".$dataRowsPriceCat."_web3"]|formatPrice}</td>
        <td>{php echo $container["proprice" . $dataRowsPriceCat . "_web3"]->getControl()->addAttributes(array('size'=>5)) }</td>
        <td colspan="2" style="{$propricefix[$dataRowsPriceCatSecond]|noescape} text-align: right">{$row["proprice" . $dataRowsPriceCatSecond . "_web3"]|formatPrice}</td>
      {/if}
      <td{if $row->procpcheureka > 0} style="background-color: yellow;"{/if}>{$row->procpcheureka|formatPriceAdmin:2:''}</td>
      <td{if $row->prodelfree == 1} style="background-color: #8811FF;"{elseif $row->prodelfree == 2} style="background-color: #CC2222;"{/if}>{php echo $container["procpcheureka"]->getControl()->addAttributes(array('size'=>2)) }</td>
      {*<td title="fixovat cenu">{php echo $container["propricefix"]->getControl() }</td>*}
      {if $serverId == 'web1'}
      <td style="text-align: right;">{$row->proqty_web1}</td>
      <td></td>
      {else}
        {if isset($row->stores)}
      <td style="text-align: right;">{isset($row->stores[2]) ? $row->stores[2] : 0}</td>
      <td style="text-align: right;">{isset($row->stores[3]) ? $row->stores[3] : 0}</td>
      <td style="text-align: right;">{isset($row->stores[4]) ? $row->stores[4] : 0}</td>
      <td style="text-align: right;">{isset($row->stores[5]) ? $row->stores[5] : 0}</td>
        {else}
      <td style="text-align: right;">0</td>
      <td style="text-align: right;">0</td>
      <td style="text-align: right;">0</td>
      <td style="text-align: right;">0</td>
        {/if}
      {/if}
      <td>{$row["proaccess_".$serverId]}</td>
      <td {if $row->prostatus!=0}bgcolor="#FF8080"{/if}>{$enum_prostatus[$row->prostatus]}</td>
      <td>
        {if $row->protypid === 1}A{/if}
        {if $row->protypid2 === 1}N{/if}
        {if $row->protypid3 === 1}T{/if}
        {if $row->protypid4 === 1}P{/if}
        {if $row->protypid5 === 1}V{/if}
      </td>
      <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a></td>
      <td><a href="{plink :Front:Product:detail, $row->proid, getProKey($row)}" target="front"><img src="{$baseUrl}/img/admin/front{if !$fileExists}2{/if}.png" width="16" height="16" alt="zobrazit zboží ve veřejné části" title="zobrazit zboží ve veřejné části" /></a></td>
      <td><a href="{plink Product:delete, $row->proid}" onclick="return DeleteConfirm('položku {$row->proname}');"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" /></a></td>

      {if !empty($propricefix[$dataRowsPriceCat])}
      <td style="{$propricefix[$dataRowsPriceCat]|noescape}" title="cena"></td>
      {else}
      <td></td>
      {/if}
      {if $row["pronamefix"] === 1}
      <td bgcolor="violet" title="název"></td>
      {else}
      <td></td>
      {/if}
      {if $row->promanidfix === 1}
      <td bgcolor="#529bce" title="výrobce"></td>
      {else}
      <td></td>
      {/if}
      {if $row->proodfix === 1}
      <td bgcolor="#dad55e" title="dodavatel"></td>
      {else}
      <td></td>
      {/if}
      {if $row->prodescfix === 1}
      <td bgcolor="#DD0000" title="popis"></td>
      {else}
      <td></td>
      {/if}
      {if $row->propicfix === 1}
      <td bgcolor="black" title="obrázek"></td>
      {else}
      <td></td>
      {/if}
      {if isset($row->prodatedisc) && $row->prodatedisc !== null}
      <td bgcolor="#b465dc" title="sleva do {$row->prodatedisc|date:'d.m.Y'}"></td>
      {else}
      <td></td>
      {/if}
    </tr>
    {/formContainer}
  {/foreach}
  </table>
  <p>Celkem {$itemCount} položek</p>
  {if $filteIsEmpy}
  <p style="color: red; font-size: large">Vyberte ve filtru co chcete hledat.</p>
  {/if}

  <span>{input save}
  {input print}</span>
  <span style="text-align: right; padding-left:100px ">{input delete}</span>

  {/form listEditForm}
  {control paginator}

  <p><strong style="color: red;"><a href="{plink NotInCatalog}">Položky které nejsou zařazeny v katalogu</a></strong> {if count($isOverValued) > 0}| <strong><a href="{plink mailOverValued}">Mailovat přeceněné položky</a></strong>{/if}</p>

  <script type="text/javascript">
    $("#checkAll").click(function(){
      $('.proid_chk').not(this).prop('checked', this.checked);
    });

    $("#toggleLabels").click(function(){
        $('.labels').toggle();
        return false;
    });

    $("#delete_products").click(function(){
        return confirm('Opravdu chcete vymazat vybrané produkty?');
    });

    $('.labels').hide();

</script>

<h3>Legenda barev</h3>
<table>
  <tr>
    <td style="background-color: #FE9A2E;">Fixní cena</td>
    <td style="background-color: violet;">Fixní název</td>
    <td style="background-color: #529bce;">Fixní výrobce</td>
    <td style="background-color: #dad55e ">Fixovaný dodavatel</td>
    <td style="background-color: black;color: white ">Fixovaný obrázek</td>
    <td style="background-color: #DD0000 ">Fixovaný popis</td>
    <td style="background-color: #07cdde;">Doporučená cena</td>
    <td style="background-color: #e10915 ">Kaufland</td>
    <td style="background-color: #1E90FF ">Heureka</td>
    <td style="background-color: #8811FF ">Doprava zdarma vynucená</td>
    <td style="background-color: #CC2222 ">Doprava zdarma blokovaná</td>
    <td style="background-color: #885500 ">Přeceněná položka</td>
    <td style="background-color: #b465dc ">Datum slevy</td>
  </tr>
</table>
{/block}
