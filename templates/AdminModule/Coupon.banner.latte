{varType App\Orm\Banner[] $banners}
{block title} Bannery{/block}
{block breadcrumb}
    <ol class="breadcrumb float-sm-right">
        <li class="breadcrumb-item"><a href="/administrace/">Domu</a></li>
        <li class="breadcrumb-item active"> bannery</li>
    </ol>
{/block}
{block content}
<div class="row">
    <div class="col-md-6">

        <div class="card card-info m-auto">
            <div class="card-header">
                <h3 class="card-title">Přidat banner</h3>
            </div>
            <div class="card-body">
                {control addBannerForm}


            </div>




        </div>



    </div>
</div>
<div class="row">
    <div class="col-12 mt-3">
        <div class="card">

            <div class="card-body table-responsive p-0">
                <table class="table table-hover text-nowrap">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Web</th>
                        <th>Nazev</th>
                        <th>Nahled</th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr n:foreach="$banners as $banner">
                        <td>{$banner->id}</td>
                        <td>{ifset $banner->web}{$banner->web->name}{/ifset}</td>
                        <td>{$banner->name}</td>
                        {if !empty($banner->url)}
                        <td><a href="{$banner->url}" target="_blank"><img n:if="$banner->getImagSrc()" style="max-width: 200px" src="{$banner->getImagSrc()|nocheck}" alt="banner"></a></td>
                        {else}
                        <td><img n:if="$banner->getImagSrc()" style="max-width: 200px" src="{$banner->getImagSrc()|nocheck}" alt=""></td>
                        {/if}
                        <td><a n:href="editBanner! $banner->id" class="btn btn-sm btn-outline-primary">Editovat</a></td>
                    </tr>

                    </tbody>
                </table>
            </div>

        </div>

    </div>
</div>