{varType App\Orm\Param $param}
{varType App\AdminModule\Presenters\ParamPresenter $presenter}
{varType App\Orm\Vendor[] $vendors}
{varType App\Orm\Product[] $products}
{block title}Parametr {$param->name}{/block}
{block breadcrumb}
    <ol class="breadcrumb float-sm-right">
        <li class="breadcrumb-item"><a href="/administrace/">Domu</a></li>
        <li class="breadcrumb-item "><a n:href="Param:edit">Parametry</a></li>
        <li class="breadcrumb-item active">{$param->name}</li>
    </ol>
{/block}
{block content}


<div class="row ">
    <div class="col-md-6 ">
        <div class="card cart-primary ">
            <div class="card-header">
                <h3 class="card-title">Základní nastavení</h3>
            </div>

            <div class="card-body">
                <div class="form-group row">
                    <div class="col-sm-3 col-form-label"><label for="frm-addParamForm-name">Název parametru:</label></div>

                    <div class="col-sm-9"><input type="text" name="name" class="form-control text" value="{$param->name}"></div>
                </div>
            </div>
            <div class="card-footer ">
                <button type="submit" class="btn btn-primary float-right">Uložit</button>
            </div>

        </div>
    </div>
    <div class="col-md-6">
        <div class="card cart-primary ">
            <div class="card-header">
                <h3 class="card-title">Kategorie</h3>
            </div>

            <div class="card-body">
                <ul>
                    {foreach $param->catalogs as $catalog}
                        <li>
                            {foreach $catalog->getTree() as $one}
                                <a n:href="Param:edit catalogId:$one->id" n:class="$iterator->last?font-weight-bold"> {$one->catname}</a> {sep} / {/sep}
                            {/foreach}
                        </li>
                    {/foreach}
                </ul>
            </div>

            <div class="card-footer">
                <div class="input-group input-group">
                    <input type="text" class="form-control">
                    <span class="input-group-append">
                    <button type="button" class="btn btn-primary btn-flat">Přidat</button>
                </span>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">


    <div class="col-md-6" n:if="$products->count()">
        <div class="card cart-primary ">
            <div class="card-header">
                <h3 class="card-title">Nevyplnené produkty <strong>{$products->count()}</strong></h3>
            </div>

            <div class="card-body">
                {foreach $products as $product}
                    {continueIf $product->getValueOnParam($param)!==null}
                <div class="form-group row">
                    <div class="col-sm-9 col-form-label"><label for="frm-addParamForm-name">{$product->vendor?->name} <a n:href="Product:edit $product->id">{$product->name}</a></label></div>
                    <div class="col-sm-3"><input type="text" name="name" class="form-control text" value=""></div>
                </div>
                {/foreach}
            </div>
            <div class="card-footer ">
                <button type="submit" class="btn btn-primary float-right">Uložit</button>
            </div>


        </div>
    </div>
    <div class="col-md-6">
        <div class="card cart-primary ">
            <div class="card-header">
                <h3 class="card-title">Nazev parametru ve feedu </h3>
            </div>

            <div class="card-body">
                {foreach $vendors as $vendor}
                    <div class="form-group row" >

                        <div class="col-sm-9 col-form-label"><label for="frm-addParamForm-name"> {$vendor->name}</label></div>
                        <div class="col-sm-3"><input type="text" name="name" class="form-control text" value="{$vendor->getParamVendorForParam($param)}"></div>
                    </div>
                {/foreach}
            </div>
            <div class="card-footer ">
                <button type="submit" class="btn btn-primary float-right">Uložit</button>
            </div>


        </div>
    </div>
</div>