<?xml version="1.0" encoding="utf-8"?>
<MoneyData ICAgendy="{$documents[0]->vendor->usric}" KodAgendy="" HospRokOd="{$year}-01-01" HospRokDo="{$year}-12-31" description="faktury př<PERSON> a vydané" ExpZkratka="_FP+FV" ExpDate="{date('Y-d-m', time())}" ExpTime="{date('h:i:s', time())}" VyberZaznamu="0">
  <SeznamFaktVyd>
    {foreach $documents as $doc}
    {php

      $kodDPH = "19Ř01,02";
      if (!empty($doc->ordic) && $doc->ordicouid == 2) {
        $kodDPH = "19Ř20";
      }

      if ($doc->ordcurcode === 'EURR') {
        $curCode = 'EUR';
        $decimals = 2;
        $decimalsItems = 2;
      } else {
        $curCode = 'Kč';
        $decimals = 2;
        $decimalsItems = 0;
      }

    }

    <FaktVyd>
      <Doklad>{$doc->ordinvcode}</Doklad>
      {*<Rada>162</Rada>*}
      {*<CisRada>49</CisRada>*}
      <Popis>prodej eshop</Popis>
      <Vystaveno>{$doc->ordinvdate|date:'Y-m-d'}</Vystaveno>
      <DatUcPr>{$doc->ordinvdate|date:'Y-m-d'}</DatUcPr>
      <PlnenoDPH>{$doc->ordinvdate|date:'Y-m-d'}</PlnenoDPH>
      <Splatno>{$doc->ordinvdate|addDaysToDate:$doc->orddaysdue}</Splatno>
      <DatSkPoh>{$doc->ordinvdate|date:'Y-m-d'}</DatSkPoh>
      {if $doc->ordpaystatus == 1}
      <Uhrazeno>{$doc->payDate|date:'Y-m-d'}</Uhrazeno>
      {/if}
      <KodDPH>{$kodDPH}</KodDPH>
      <ZjednD>0</ZjednD>
      <VarSymbol>{$doc->ordcode}</VarSymbol>
      <PuvDoklad></PuvDoklad>
      <Ucet>FIO1</Ucet>
      <Druh>N</Druh>
      <Dobropis>0</Dobropis>
      <Uhrada>
        {if $doc->payType->delcode == 'paybefore'}
        převodem
        {elseif $doc->payType->delcode == 'cofidis'}
        převodem
        {elseif $doc->payType->delcode == 'dobirka'}
        dobírkou
        {elseif $doc->payType->delcode == 'cash'}
        hotově
        {elseif $doc->payType->delcode == 'creditcard'}
        plat.kart.
        {/if}
      </Uhrada>
      <PredKontac>MALOOBCHOD</PredKontac>
      {ifset $doc->docVat[1]}<SazbaDPH1>{$doc->docVat[1]}</SazbaDPH1>{/ifset}
      <SazbaDPH2>{$doc->docVat[0]}</SazbaDPH2>
      <Proplatit>{if $doc->ordpaystatus == 1}0{else}{round($doc->ordpricevat)}{/if}</Proplatit>
      <Vyuctovano>0</Vyuctovano>
      <Celkem>{round($doc->ordpricevat)}</Celkem>
      <PriUhrZbyv>0</PriUhrZbyv>
      <ValutyProp>0</ValutyProp>
      <SumZaloha>0</SumZaloha>
      <SumZalohaC>0</SumZalohaC>
      <DodOdb>
        <KodPartn>{$doc->addiguid}</KodPartn>
        {if !empty($doc->ordifirname) || !empty($doc->ordilname)}<ObchNazev>{if !empty($doc->ordifirname)}{$doc->ordifirname}, {$doc->ordiname} {$doc->ordilname}{else}{$doc->ordiname} {$doc->ordilname}{/if}</ObchNazev>{/if}
        <ObchAdresa>
          {if !empty($doc->ordistreet)}<Ulice>{$doc->ordistreet} {$doc->ordistreetno}</Ulice>{/if}
          {if !empty($doc->ordicity)}<Misto>{$doc->ordicity}</Misto>{/if}
          {if !empty($doc->ordipostcode)}<PSC>{$doc->ordipostcode}</PSC>{/if}
          {if $doc->ordicouid == 2}
          <Stat>Slovenská republika</Stat>
          <KodStatu>SK</KodStatu>
          {else}
          <Stat>Česká republika</Stat>
          <KodStatu>CZ</KodStatu>
          {/if}
        </ObchAdresa>
        {if !empty($doc->ordifirname) || !empty($doc->ordilname)}<FaktNazev>{if !empty($doc->ordifirname)}{$doc->ordifirname}, {$doc->ordiname} {$doc->ordilname}{else}{$doc->ordiname} {$doc->ordilname}{/if}</FaktNazev>{/if}
        {if !empty($doc->ordic)}<ICO>{$doc->ordic}</ICO>{/if}
        {if !empty($doc->orddic)}<DIC>{$doc->orddic}</DIC>{/if}
        <FaktAdresa>
          {if !empty($doc->ordistreet)}<Ulice>{$doc->ordistreet} {$doc->ordistreetno}</Ulice>{/if}
          {if !empty($doc->ordicity)}<Misto>{$doc->ordicity}</Misto>{/if}
          {if !empty($doc->ordipostcode)}<PSC>{$doc->ordipostcode}</PSC>{/if}
          {if $doc->ordicouid == 2}
          <Stat>Slovenská republika</Stat>
          <KodStatu>SK</KodStatu>
          {else}
          <Stat>Česká republika</Stat>
          <KodStatu>CZ</KodStatu>
          {/if}
        </FaktAdresa>
        {if !empty($doc->ordifirname) || !empty($doc->ordilname)}<Nazev>{if !empty($doc->ordifirname)}{$doc->ordifirname}, {$doc->ordiname} {$doc->ordilname}{else}{$doc->ordiname} {$doc->ordilname}{/if}</Nazev>{/if}
        {if !empty($doc->ordmail)}<EMail>{$doc->ordmail}</EMail>{/if}
        {if !empty($doc->ordtel)}<tel>
          <cislo>{$doc->ordtel}</cislo>
        </tel>{/if}
      </DodOdb>
      {if !empty($doc->ordstname)}
      <KonecPrij>
        <KodPartn>{$doc->addstguid}</KodPartn>
        <Nazev>{if !empty($doc->ordstfirname)}{$doc->ordstfirname}, {$doc->ordstname} {$doc->ordstlname}{else}{$doc->ordstname} {$doc->ordstlname}{/if}</Nazev>
        <Adresa>
          <Ulice>{$doc->ordststreet} {$doc->ordststreetno}</Ulice>
          <Misto>{$doc->ordstcity}</Misto>
          <PSC>{$doc->ordstpostcode}</PSC>
          {if $doc->ordstcouid == 2}
          <Stat>Slovenská republika</Stat>
          <KodStatu>SK</KodStatu>
          {else}
          <Stat>Česká republika</Stat>
          <KodStatu>CZ</KodStatu>
          {/if}
        </Adresa>
        <ICO>{$doc->ordic}</ICO>
        <DIC>{$doc->orddic}</DIC>
      </KonecPrij>
      {/if}
      <DopravTuz>0</DopravTuz>
      <DopravZahr>0</DopravZahr>
      <Sleva>0</Sleva>
      <Pojisteno></Pojisteno>
      <PlnenDPH></PlnenDPH>
      <SeznamPolozek>
        {foreach $doc->items as $irow}
        <Polozka>
          <Popis>{$irow->oriname}</Popis>
          <PocetMJ>{$irow->oriqty}</PocetMJ>
          <SazbaDPH>{$irow->orivat}</SazbaDPH>
          <Cena>{$irow->oriprice}</Cena>
          {if $doc->ordcurcode === 'EURR' && $doc->ordcurrate > 0}
          <CenaTyp>3</CenaTyp>
          <Valuty>{$irow->oripriceeur}</Valuty>
          {else}
          <CenaTyp>1</CenaTyp>
          <Valuty>0</Valuty>
          {/if}
          <Sleva>0</Sleva>
          <Poradi>{$iterator->getCounter()}</Poradi>
          <KodDPH>{$irow->KodDph}</KodDPH>
          <Predkontac>{if isset($irow->predkontac)}{$irow->predkontac}{elseif $irow->oritypid==1}DOPRAVA{elseif $irow->oriprocode=='služba' || $irow->oriprocode=='oprava'}SLUŽBY{else}MALOOBCHOD{/if}</Predkontac>
          <NesklPolozka>
            <Zaloha>0</Zaloha>
            <TypZarDoby>N</TypZarDoby>
            <ZarDoba>0</ZarDoba>
            <Protizapis>0</Protizapis>
            <Hmotnost>0</Hmotnost>
          </NesklPolozka>
          <CenaPoSleve>1</CenaPoSleve>
        </Polozka>
        {/foreach}
      </SeznamPolozek>
      {if $doc->ordpaystatus == 1}
      <SeznamUhrad>
        <Uhrada>
          <Prijem>1</Prijem>
          <Poradi>4280</Poradi>
          <Datum>{$doc->payDate|date:'Y-m-d'}</Datum>
          <Castka>{$doc->ordpricevat}</Castka>
        </Uhrada>
      </SeznamUhrad>
      {/if}
      <SouhrnDPH>
        <Zaklad0>{round($irow->sumNoVat[3], 2)}</Zaklad0>
        <Zaklad5>{round($irow->sumNoVat[1], 2)}</Zaklad5>
        <Zaklad22>{round($irow->sumNoVat[0], 2)}</Zaklad22>
        <DPH5>{$irow->sum[1]-$irow->sumNoVat[1]}</DPH5>
        <DPH22>{$irow->sum[0]-$irow->sumNoVat[0]}</DPH22>
        {if $irow->sumNoVat[2] > 0}
        <SeznamDalsiSazby>
          <DalsiSazba>
            <Popis>druhá snížená</Popis>
            <HladinaDPH>1</HladinaDPH>
            <Sazba>10</Sazba>
            <Zaklad>{round($irow->sumNoVat[2], 2)}</Zaklad>
            <DPH>{$irow->sum[2]-$irow->sumNoVat[2]}</DPH>
          </DalsiSazba>
        </SeznamDalsiSazby>
        {/if}
      </SouhrnDPH>
      {if $doc->ordcurcode === 'EURR'}
      <Valuty>
        <Mena>
          <Kod>EUR</Kod>
          <Mnozstvi>1</Mnozstvi>
          <Kurs>{$doc->ordcurrate}</Kurs>
        </Mena>
        <SouhrnDPH>
          <Zaklad0>{round($irow->sumEurNoVat[3], 2)}</Zaklad0>
          <Zaklad5>{round($irow->sumEurNoVat[1], 2)}</Zaklad5>
          <Zaklad22>{round($irow->sumEurNoVat[0], 2)}</Zaklad22>
          <DPH5>{$irow->sumEur1-$irow->sumEurNoVat[1]}</DPH5>
          <DPH22>{$irow->sumEur0-$irow->sumEurNoVat[0]}</DPH22>
          {if $irow->sumEurNoVat[2] > 0}
          <SeznamDalsiSazby>
            <DalsiSazba>
              <Popis>druhá snížená</Popis>
              <HladinaDPH>1</HladinaDPH>
              <Sazba>10</Sazba>
              <Zaklad>{round($irow->sumEurNoVat[2], 2)}</Zaklad>
              <DPH>{$irow->sumEur[2]-$irow->sumEurNoVat[2]}</DPH>
            </DalsiSazba>
          </SeznamDalsiSazby>
          {/if}
        </SouhrnDPH>
      </Valuty>
      {/if}
      <MojeFirma>
        <Nazev>{$doc->vendor->usrifirname}</Nazev>
        <Adresa>
          <Ulice>{$doc->vendor->usristreet} {$doc->vendor->usristreetno}</Ulice>
          <Misto>{$doc->vendor->usricity}</Misto>
          <PSC>{$doc->vendor->usripostcode}</PSC>
          <Stat>Česká republika</Stat>
          <KodStatu>CZ</KodStatu>
        </Adresa>
        <ObchNazev>{$doc->vendor->usrifirname}</ObchNazev>
        <ObchAdresa>
          <Ulice>{$doc->vendor->usristreet} {$doc->vendor->usristreetno}</Ulice>
          <Misto>{$doc->vendor->usricity}</Misto>
          <PSC>{$doc->vendor->usripostcode}</PSC>
          <Stat>Česká republika</Stat>
          <KodStatu>CZ</KodStatu>
        </ObchAdresa>
        <FaktNazev>{$doc->vendor->usrifirname}</FaktNazev>
        <FaktAdresa>
          <Ulice>{$doc->vendor->usristreet} {$doc->vendor->usristreetno}</Ulice>
          <Misto>{$doc->vendor->usricity}</Misto>
          <PSC>{$doc->vendor->usripostcode}</PSC>
          <Stat>Česká republika</Stat>
          <KodStatu>CZ</KodStatu>
        </FaktAdresa>
        <Tel>
          <Pred></Pred>
          <Cislo></Cislo>
          <Klap></Klap>
        </Tel>
        <Fax>
          <Pred></Pred>
          <Cislo></Cislo>
          <Klap></Klap>
        </Fax>
        <Mobil>
          <Pred></Pred>
          <Cislo></Cislo>
        </Mobil>
        <EMail></EMail>
        <WWW></WWW>
        <ICO>********</ICO>
        <DIC>CZ********</DIC>
        <Banka>Raiffeisenbank a.s.</Banka>
        <Ucet>**********</Ucet>
        <KodBanky>5500</KodBanky>
        <KodPartn></KodPartn>
        <FyzOsoba>0</FyzOsoba>
        <MenaSymb>Kč</MenaSymb>
        <MenaKod>CZK</MenaKod>
      </MojeFirma>
    </FaktVyd>
    {/foreach} {*konec faktura*}
  </SeznamFaktVyd>
  <SeznamFaktVyd_DPP/>
</MoneyData>
