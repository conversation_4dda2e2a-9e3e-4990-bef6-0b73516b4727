{block #content}
  {if isset($dataRow["delstoid"])}
  <script>
  $(function() {
    $( ".autocomplete" ).keydown(function (event) {
      if (event.which == 13) {
        $.ajax({
            url: {plink 'autocompleteProducts'},
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            data: "proid=" + this.value,
            cache: false,
            complete: function (data) {
                if (data.readyState == 4) {
                    id = "#frmdelItemsEditForm-newitem-";
                    $(id + 'deiproid').val(data.responseJSON[0].id);
                    $(id + 'deiname').val(data.responseJSON[0].value);
                    $(id + 'deiprice').focus().select();
                } else {
                    alert('Informace se bohužel nepodařilo načíst.');
                }
            }
        });
        event.preventDefault();
        return false;
      }
    });

    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('deiname', 'deiproid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>
  {/if}
  <h3>{block #title} Editace dodávky {if $id >0} ID: {$id} {/if} {/block}</h3>
  <p>
  [ <a href="{plink edit}">přidat novou dodávku</a> ]
    {ifset $delItems}
      [ <a href="{plink edit, $id, 'm'=>'m'}">rozdělit dodávku /změnit datum nask. </a> ]
      [ <a target="invoicePrint" href="{plink Delivery:printProtocol, $id}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a> ]
    {/ifset}
  {ifset $delItems} {if $dataRow["delcurcode"] != 'CZK'}| [ <a href="{plink recalcPrices, $id}">přepočítat nákupní ceny</a> ]{/if}{/ifset}
  {if $dataRow && $dataRow->delstatus === 1 && ($admin->admrole === 'superadmin')}[ <a href="{plink delete $id}" onclick="return DeleteConfirm('dodávku');">Vymazat dodávku</a> ]{/if}
  </p>
  {control editForm}
  {if $proId0>O}
    <span style="color: red">Dávka obsahuje položky s proid=0 - kontaktujte podporu</span>
  {/if}
  {foreach $duplicitImeis as $row}
    {if $iterator->isFirst()}
    <div class="flash err">
    <h4>Duplicitní IMEI</h4>
    <table class="grid">
    <tr>
      <th>IMEI</th>
      <th>Produkt</th>
      <th>Duplicity</th>
    </tr>
    {/if}
    <tr>
      <td>{$row["item"]->diiimei}</td>
      <td><a href="{plink Product:edit, $row["item"]->proid}">{$row["item"]->proname}</a></td>
      <td>
        {foreach $row["dupls"] as $item}
          {if $dataRow->delid==$item->deidelid}tato dodávka{else} <a href="{plink 'edit', $item->deidelid}">dodávka</a>{/if}, produkt: {$item->proname}
          {if !$iterator->isLast()}
          <br />
          {/if}
        {/foreach}
      </td>
    </tr>
    {if $iterator->isLast()}
    </table>
    </div>
    {/if}
  {/foreach}
  {ifset $delItems}
  <h4 id="edititems">Položky dodávky</h4>

  {if count($proBlockStatus) > 0}
  {foreach $proBlockStatus as $text}
    {if $iterator->first}
    <strong style="color: red">Blokované položky:</strong>
    <ul>
    {/if}
      <li>{$text}</li>
    {if $iterator->last}
    </ul>
    {/if}
  {/foreach}
  <br>
  {/if}

  {form delItemsEditForm}
    <ul class="error" n:if="$form->hasErrors()">
      <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
    </ul>
    <table class="grid">
      <tr>
        <th></th>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>název</th>
        {if $dataRow->delcurcode == 'EUR'}<th>Cena v €</th>{/if}
        <th>cena bez DPH</th>
        <th>cena s DPH</th>
        <th>počet</th>
        <th>{if $dataRow->delstatus == 1}Přesunout kusů:{/if}</th>
        <th>IMEI</th>
        <th colspan="3"></th>
      </tr>

      {php
        $sumPrice=0;
        $sumPriceEur=0;
        $sumPriceNoVat=0;
        $sumQty=0;

        if ($dataRow->delcurcode == 'EUR') {
          $sumPriceEur=$dataRow->deldelprice;
          $sumPriceNoVat=round($dataRow->deldelprice * $dataRow->delcurrate, 2);
        } else {
          $sumPriceNoVat=$dataRow->deldelprice;
        }
        $sumPrice=round($sumPriceNoVat * 1.21, 2);
        
        if ($dataRow->delisonstock == 1) {
          $sumPrice=0;
          $sumPriceEur=0;
          $sumPriceNoVat=0;
        }
      }

      {ifset $form['newitem']}
      <tr>
        <th colspan="13">nová položka</th>
      </tr>
      <tr>
        <td></td>
        <td>{php echo $form['newitem']['deiproid']->control }</td>
        <td></td>
        <td>{php echo $form['newitem']['deiname']->control } </td>
        {if $dataRow->delcurcode == 'EUR'}<td>{php echo $form['newitem']['priceEur']->control }</td>{/if}
        <td>{php echo $form['newitem']['deipricenovat']->control }</td>
        <td>{php echo $form['newitem']['deiprice']->control }</td>
        <td>{php echo $form['newitem']['deiqty']->control }</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td colspan="13">{input saveitems}</td>
      </tr>
      {/ifset}

      {foreach  $form['items']->getComponents() as $cont}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
      {var $deiid=$form['items'][$cont->name]['deiid']->value}
        <td>{if $dataRow->delstatus == 1}{php echo $form['items'][$cont->name]['checked']->control }{/if}</td>
        <td>{$delItems[$deiid]->deiproid}</td>
        <td {if $delItems[$deiid]->promall} style="background-color: #80FF80"{/if}>{$delItems[$deiid]->deiprocode}</td>
        <td>{$delItems[$deiid]->deiproname} {if $delItems[$deiid]->prospecmode != $dataRow->delspecmode}<br><span style="color: red">nesouhlasí nastavení speciálního režimu u produktu a dodávky</span>{/if}</td>
        {if $dataRow->delcurcode == 'EUR'}<td>{php echo $form['items'][$cont->name]['priceEur']->control } {$delItems[$deiid]->deipriceeur}&nbsp;€</td>{/if}
        <td style="text-align: right;">{php echo $form['items'][$cont->name]['deipricenovat']->control }</td>
        <td>{php echo $form['items'][$cont->name]['deiprice']->control }</td>
        <td>{php echo $form['items'][$cont->name]['deiqty']->control }</td>
        <td>{if $dataRow->delstatus == 1}{php echo $form['items'][$cont->name]['deitraqty']->control }{/if}</td>
        <td>
          {if $delItems[$deiid]->deiimeiok}
            <img src="{$baseUrl}/img/admin/accept.png" width="16" height="16" title="IMEI vyplněno u všech položek" />
          {else}
            <img src="{$baseUrl}/img/admin/cancel.png" width="16" height="16"  title="Chybí IMEI" />
          {/if}
          <a href="{plink Delivery:editImeis, $deiid}" title="zadat IMEI"> <img src="{$baseUrl}/img/admin/prooffer.png" width="16" height="16" /></a></td>
        {ifset $form['newitem']}
        <td><a href="{plink Delivery:deleteItem, $deiid, $dataRow->delid}" onclick="return DeleteConfirm('položku dodávky');"> <img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" /></a></td>
        {else}
        <td><a href="{plink Delivery:repairItem, $deiid}" title="opravit záznam"> <img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a></td>
        {/ifset}
        <td><a href="{plink Product:edit, $delItems[$deiid]->deiproid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a></td>
        <td><a target="itemReport" href="{plink Delivery:itemReport, $delItems[$deiid]->deiid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a></td>
      </tr>
      {php
      $sumPrice += ($delItems[$deiid]->deiprice * $delItems[$deiid]->deiqty);
      $sumPriceEur += ($delItems[$deiid]->deipriceeur * $delItems[$deiid]->deiqty);
      $sumPriceNoVat += ($delItems[$deiid]->deipricenovat * $delItems[$deiid]->deiqty);
      $sumQty += $delItems[$deiid]->deiqty;
      }
      {/foreach}

      <tr>
        <td></td>
        <td></td>
        <td></td>

        <td style="text-align: right;">celkem {count($delItems)} položek</td>
        {if $dataRow->delcurcode == 'EUR'}
        <td style="text-align: right;">{$sumPriceEur}&nbsp;€</td>
        {/if}
        <td style="text-align: right;">{$sumPriceNoVat|formatPrice:2}</td>
        <td style="text-align: right;">{$sumPrice|formatPrice:2}</td>
        <td style="text-align: right;">{$sumQty}</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
      </tr>

      {if $dataRow->delstatus == 1}
      <tr>
        <td colspan="13">{label deitrastoid /} {input deitrastoid} {input createTransfer} | {input sendToMall} {ifset $form['saveprices']}| {input saveprices}{/ifset}</td>
      </tr>
      {/if}
    </table>
  {/form}
  {/ifset}

  <script type="text/javascript">
  $(document).ready(function() {
    setTimeout(function(){
      $('.autofocus').focus();
    });
  });
  </script>

  {ifset $delItems}
  {include @printZplLabels.latte items=>$delItemsPrint}
  {/ifset}

  {ifset $statusLog}
  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie</h3>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->deldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_delstatus[$row->delstatus]}</td>
    <td>{$row->deladmname}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}

  <a href="{plink Delivery:delete, $id}"  onclick="return DeleteConfirm('dodávku');">Vymazat dodávku</a>
  {/ifset}

{/block}