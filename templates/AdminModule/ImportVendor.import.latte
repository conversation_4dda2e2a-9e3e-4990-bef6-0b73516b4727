
{block #content}
  <h3>{block #title}Import od dodavatele {if !empty($type)}{$type|firstUpper}{/if}{/block}</h3>
  {ifset $importLog}
    {foreach $importLog as $message}
      {if $iterator->first}
        <h4>Průběh importu:</h4>
        <p>
      {/if}
      {$message}<br>
      {if $iterator->last}
        </p>
      {/if}
    {/foreach}
  {/ifset}

  {ifset $missingItems}
    <h3>Chybějící položky v ceníku ({count($missingItems)} položek)</h3>
    <table>
    {foreach $missingItems as $item}
      <tr>
        <td><a href="{plink Product:edit $item->proid}" target="product-edit"> {$item->procode} </a></td>
        <td>{$item->procodeven}</td>
        <td>{$item->proname}</td>
      </tr>
    {/foreach}
    </table>
  {/ifset}
  <p><a href="{plink products}">Přejít na seznam předimportovaného zboží</a></p>
{/block}