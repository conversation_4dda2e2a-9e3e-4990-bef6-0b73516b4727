<script type="text/javascript">
  $(function() {
    $( ".autocomplete" ).keydown(function (event) {
      if (event.which == 13) {
          var val = this.value;
          if (!isNaN(val)) {
              $.ajax({
                  url: {plink 'autocompleteProducts'},
                  contentType: "application/json; charset=utf-8",
                  dataType: "json",
                  data: "proid=" + this.value,
                  cache: false,
                  complete: function (data) {
                      if (data.readyState == 4) {
                          id = "#frmordItemsEditForm-newitem-";
                          $(id + 'oriproid').val(data.responseJSON[0].id);
                          $(id + 'oriname').val(data.responseJSON[0].value);
                          $(id + 'oriprice').val(data.responseJSON[0].price);
                          $(id + 'oriqty').get(0).focus();
                          $(id + 'oriqty').select();
                      } else {
                          alert('Informace se bohužel nepodařilo načíst.');
                      }
                  }
              });
              event.preventDefault();
              return false;
          }
      }
    });

    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('oriname', 'oriproid');
          $("#"+newId).val(ui.item.id);
          newId = id.replace('oriname', 'oriprice');
          $("#"+newId).val(ui.item.price);
        }
        return false;
      }
  });
});

$(function() {
    $("#ordpostcode_napostu").autocomplete({
        source: {plink Order:searchStPc},
        minLength: 3,
        select: function(event, ui) {
            $("#ordpostcode_napostu").val(ui.item.value);
            $("#cpname").text(ui.item.label.substr(6));
            return false;
        }
    });
});

//zasilkovna
$(function() {
    $("#orddelspeczasilkovna_label").autocomplete({
        source: {plink Order:searchZasPoints},
        minLength: 3,
        select: function(event, ui) {
            $("#orddelspeczasilkovna").val(ui.item.value);
            $("#orddelspeczasilkovna_label").val(ui.item.label);
            return false;
        }
    });
});

//balikovna
$(function() {
    $("#orddelspecbalikovna_label").autocomplete({
        source: {plink Order:searchBalikovna},
        minLength: 3,
        select: function(event, ui) {
            $("#orddelspecbalikovna").val(ui.item.value);
            $("#orddelspecbalikovna_label").val(ui.item.label);
            return false;
        }
    });
});

//ppl
$(function() {
    $("#orddelspecppl_label").autocomplete({
        source: {plink Order:searchPpl},
        minLength: 3,
        select: function(event, ui) {
            $("#orddelspecppl").val(ui.item.value);
            $("#orddelspecppl_label").val(ui.item.label);
            return false;
        }
    });
});

//zasilkovna
$(function() {
    $("#orddelspec_label").autocomplete({
        source: {plink Order:searchUloPoints},
        minLength: 3,
        select: function(event, ui) {
            $("#orddelspec").val(ui.item.value);
            $("#orddelspec_label").val(ui.item.label);
            return false;
        }
    });
});


</script>