{layout NULL}
{*<script type="text/javascript" src="{$baseUrl}/js/admin/jquery.js"></script>*}
<script type="text/javascript" src="{$baseUrl}/js/admin/BrowserPrint-1.0.4.min.js"></script>
<script type="text/javascript" src="{$baseUrl}/js/admin/zebraWebPrint.js"></script>
<script type="text/javascript">
$(document).ready(setup_web_print);
</script>
<script type="text/javascript">
var OSName="Unknown OS";
if (navigator.appVersion.indexOf("Win")!=-1) OSName="Windows";
if (navigator.appVersion.indexOf("Mac")!=-1) OSName="MacOS";
if (navigator.appVersion.indexOf("X11")!=-1) OSName="UNIX";
if (navigator.appVersion.indexOf("Linux")!=-1) OSName="Linux";
</script>


    <div id="main">
      <div id="printer_data_loading" style="display:none"><span id="loading_message">Zjišťuji informace o tiskárně ...</span><br/>
        <div class="progress" style="width:100%">
          <div class="progress-bar progress-bar-striped active"  role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
          </div>
        </div>
      </div> <!-- /printer_data_loading -->
      <div id="printer_details" style="display:none">
        <span id="selected_printer">No data</span>
        {*<button type="button" class="btn btn-success" onclick="changePrinter()">Změnit</button>*}
      </div><br /> <!-- /printer_details -->
      <div id="printer_select" style="display:none">
        Zebra Printer Options<br />
        Printer: <select id="printers"></select>
      </div> <!-- /printer_select -->
      <div id="print_form" style="display:none">

        <input id="printContent" type="hidden" value="
CT~~CD,~CC^~CT~
^XA~TA000~JSN^LT0^MNW^MTT^PON^PMN^LH0,0^JMA^PR5,5~SD25^JUS^LRN^CI0^XZ
{foreach $items as $row}
{php
  $name = Nette\Utils\Strings::toAscii($row->proname);
  $name1 = substr($name, 0, 61);
  $name2 = substr($name, 61, 130);
}
^XA
^MMT
^PW559
^LL0280
^LS0
^BY2,3,122^FT543,144^B2I,,N,N
^FD{$row->proid}^FS
^FT544,98^A0I,20,20^FH\^FD{$row->procode}^FS
^FT544,78^A0I,20,20^FH\^FD{$row->proprice|number:0, ',', ' '}Kc s DPH^FS
^FT545,56^A0I,20,19^FH\^FD{$name1}^FS
^FT545,28^A0I,20,19^FH\^FD{$name2}^FS
^PQ{$row->qty},0,1,Y^XZ
{/foreach}
">
      <button type="button" class="btn btn-lg btn-primary" onclick="sendData();" value="Print">Tisknout štítky ...</button>
      </div> <!-- /print_form -->
    </div> <!-- /main -->
    <div id="error_div" style="width:500px; display:none"><div id="error_message"></div>
      <button type="button" class="btn btn-lg btn-success" onclick="trySetupAgain();">Try Again</button>
    </div><!-- /error_div -->


