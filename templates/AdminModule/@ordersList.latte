

  <script>
  function noteSH(id) {
     if ($('#'+id).is(':visible')) {
      $('#'+id).hide();
     } else {
       $('#'+id).show();
     }
     return false;
  }
  </script>
<form method="get" action="{plink Order:batchAction}">
  {foreach $presenter->getParameters() as $key=>$value}
    {if !empty($value)}
    <input type="hidden" name="{$key}" value="{$value}">
    {/if}
  {/foreach}

 <input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše
<table class="grid">
  <tr>
    <th colspan="2"> </th>
    <th>Č. obj.</th>
    <th>Č. fa.</th>
    <th>Zákazník</th>
    <th>Datum</th>
    <th>Cena s DPH</th>
    <th>Doprava, platba</th>
    <th><span style="background: #FFE7A1; color: black">Doručit</span><br>Exped<PERSON>no</th>
    <th>Pozn.</th>
    <th>Status</th>
    <th colspan="3"></th>
    <th>Dobropis</th>
    <th>Fa</th>
    <th></th>
    <th></th>
    <th></th>
  </tr>
  {php


    $sum = 0;
    $cnt = 0;
  }

  {foreach $dataRows as $row}
  {php
    $sum += $row->ordpricevat;
    $cnt ++;
    $tdStyle = 'bgcolor="#FFFFFF"';
    if ($row->ordtype == 1) {
      $tdStyle = 'bgcolor="#55C468"';
    } else if ($row->ordtype == 3) {
      $tdStyle = 'bgcolor="#43BDF8"';
    }

    $style='';
    switch ($row->ordfirid) {
      case 1:
        $style='';
        break;
      case 2:
        $style='bgcolor="#C0C0C0"';
        break;
      case 3:
        $style='bgcolor="#FFFFC0"';
        break;
    }

    $styleCar='';

    $styleDeli='';

    if (($row->delcodemas === 'ULOZENKA' && empty($row->orddelspec)) || ($row->delcodemas === 'ZASILKOVNA' && empty($row->orddelspeczasilkovna))) {
      $styleCar = 'style="background-color: red" title="Není vyplněno odběrné místo!"';
    }
    if ($row->delnamemas === 'Nadrozměrná doprava') {
    $styleDeli = 'style="background-color: yellow" ';
    }

  }

  <tr {$style|noescape} >
    <td><input class="ordid_chk" type="checkbox" name="ordid[{$row->ordid}]" value="{$row->ordid}" ></td>
    <td {$styleCar|noescape}>
    {if !empty($row->ordparcode)}
      {if $row->delcodemas == 'ULOZENKA'}
      <a href="https://tracking.ulozenka.cz/{$row->ordparcode}" target="_blank">
      {elseif str_contains($row->delcodemas, 'ZASILKOVNA')}
      <a href="https://www.zasilkovna.cz/vyhledavani/{$row->ordparcode}/" target="_blank">
      {elseif $row->delcodemas == 'INTIME'}
      <a href="https://trace.intime.cz/?orderNumber={$row->ordparcode}" target="_blank">
      {elseif str_contains($row->delcodemas, 'CESKA_POSTA')}
      <a href="https://www.postaonline.cz/trackandtrace/-/zasilka/cislo?parcelNumbers={$row->ordparcode}" target="_blank">
      {elseif str_contains($row->delcodemas, 'PPL')}
      <a href="https://www.ppl.cz/vyhledat-zasilku?shipmentId={$row->ordparcode}" target="_blank">
      {elseif $row->delcodemas == 'DPD'}
      {if !empty($row->ordparcode2)}{$row->ordparcode2} | {/if}<a href="https://tracking.dpd.de/status/cs_CZ/parcel/{$row->ordparcode}" target="_blank">
      {/if}
      <img src="{$baseUrl}/img/admin/car.png" width="16" height="16" title="Už existuje balík - sledovat balík" />
      </a>
    {/if}
    {if str_contains($row->delcodemas, 'CESKA_POSTA') && !empty($row->ordparcode2) && empty($row->ordparcode)}
      <a href="{plink Order:checkPostTransactions, $row->ordparcode2}">
        <img src="{$baseUrl}/img/admin/bigsize.png" width="16" height="16" title="Probíhá zpracování balíku - zjistit stavu" />
      </a>
    {/if}
    {if str_contains($row->delcodemas, 'PPL') && !empty($row->ordparcode2) && empty($row->ordparcode)}
      <a href="{plink Order:checkPplTransactions, $row->ordparcode2}">
        <img src="{$baseUrl}/img/admin/bigsize.png" width="16" height="16" title="Probíhá zpracování balíku - zjistit stavu" />
      </a>
    {/if}
    </td>
    <td {$tdStyle|noescape}>{if $row->ordtype == 3}
      <a href="https://sluzby.heureka.cz/obchody/kosik-objednavky/?filters[email]={$row->ordmail}" target="heureka">{$row->ordcode}</a>{else}{$row->ordcode}{/if}
      {if !empty($row->ordmalid)}<br><span  style="background: #eab102">M:</span> <a href="https://new-partners.mallgroup.com/orders/{$row->ordmalid}" target="_blank">{$row->ordmalid}</a>{/if}
      {if !empty($row->ordsource) && $row->ordsource === App\Orm\Order::SOURCE_KAUFLAND}<br><span style="background: orangered" target="_blank">K:</span> {$row->ordsourceid}{/if}
    </td>
    <td>{$row->ordinvcode}</td>
    <td>{$row->ordiname} {$row->ordilname} {$row->ordifirname}</td>
    <td>{$row->orddatec|date:'d.m.Y H:i:s'}</td>
    <td style="text-align: right;white-space:nowrap">
      {$row->ordpricevat|formatPrice}

      {if $row->ordfirstpay !== NULL}
      <br><span style="color: red; font-size: small">PP: {$row->ordfirstpay|formatPrice}</span>
      {/if}

      {if isset($row->orditemssum) && $row->ordpricevat !== $row->orditemssum}
      <span style="font-size: xxx-large;color: red" title="nesedí cena objednávky s položkami"><strong>!</strong></span>
      {/if}
    </td>
    <td {$styleDeli|noescape}>{$row->delnamemas}, {$row->delname}</td>
    <td>{if !empty($row->ordmalshipdate)}<span style="background: #FFE7A1">{$row->ordmalshipdate|date:'d.m.Y'}</span><br>{/if}{$row->orddatesend|date:'d.m.Y'}</td>
    <td>{if !empty($row->ordnote)}<a href="#" onclick="return noteSH('note_{$row->ordid}');"><img src="{$baseUrl}/img/admin/magnifier.png" width="16" height="16" /></a><div style="display:none" id="note_{$row->ordid}">{$row->ordnote|breakLines}</div>{/if}</td>
    <td style="color: black; background-color: {$colors[$row->ordstatus]|noescape};">
    {foreach $enum_ordstatus as $key => $text}
      {if $iterator->isFirst()}
      <select name="ordstatus[{$row->ordid}]">
      {/if}
      <option value="{$key}" {if $key==$row->ordstatus} selected="selected"{/if}>{$text}</option>
      {if $iterator->isLast()}
      </select>
      {/if}
    {/foreach}
    </td>
    <td><a href="{plink Order:edit, $row->ordid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" /></a></td>
    <td><!--<a href="{plink Order:edit, $row->ordid, 1}" class="ajax-popup-link" alt="info" style="background-color: #E0E0E0; padding: 5px;">NÁHLED</a>--></td>
    <td>{if $row->ordusrid > 0}<a href="{plink User:edit, $row->ordusrid}"><img src="{$baseUrl}/img/admin/user.png" width="16" height="16" /></a>{/if}</td>
    <td>{if !empty($row->crnid)}{if $identity->isAllowed('Admin:CreditNote', 'edit')}<a href="{plink CreditNote:edit, $row->crnid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" /></a>{/if} <a target="invoicePrint" href="{plink CreditNote:print, $row->crnid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>{/if}</td>
    <td>{if !empty($row->ordinvcode)}<a target="invoicePrint" href="{plink Order:printInvoice, $row->ordid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a> <a href="{plink Order:printInvoice, $row->ordid, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a>{/if}</td>
    <td><a href="{plink //:Front:Order:status, $row->ordid.substr(md5($row->ordid.$row->orddatec->getTimestamp()), 0, 8)}" target="_blank"  title="status objednávky"title><img src="{$baseUrl}/img/admin/magnifier.png" width="16" height="16" /></a></td>
    <td><a href="{plink Order:delete, $row->ordid}"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" onclick="return DeleteConfirm('objednávku č. {$row->ordcode}');" /></a></td>
    <td><a href="{plink Order:getOrderStore, $row->ordid}"><img src="{$baseUrl}/img/admin/front.png" width="16" height="16" alt="Vyskladňovací objednávka" title="Vyskladňovací objednávka" /></a></td>
  </tr>
  {/foreach}


  <tr>
    <td colspan="6" ><strong>Celkem: {$cnt} objednávek </strong>    </td>
    <td style="text-align: right;"><strong>{$sum|formatPrice}</strong>    </td>
    <td colspan="9" ></td>
  </tr>
  </table>
  <fieldset>
  {foreach $enum_ordstatus as $key => $text}
      {if $iterator->isFirst()}
      <select name="ordstatus_list">
        <option value="from_list">Dle nastavení v seznamu</option>
        <optgroup label="Jen u vybraných nastavit status:">
      {/if}

      <option value="{$key}">{$text}</option>
      {if $iterator->isLast()}
        </optgroup>
      </select>
      {/if}
    {/foreach}
  <input type="submit" name="change_status" value="Aktualizovat status">
  <input type="submit" name="export_ppl" value="Export PPL">
  <input type="submit" name="export_post_csv" value="Export Česká pošta - CSV">
  </fieldset>
  <fieldset>
  <legend>Česká pošta API</legend>
    <input type="submit" name="export_post" value="Odeslat balíky">
    <input type="submit" name="check_post" value="Kontrola stavu odeslání">
  <input type="submit" name="print_post" value="Tisk štítků"><br>
  </fieldset>
  <fieldset>
  <legend>PPL API</legend>
    <input type="submit" name="export_ppl" value="Odeslat balíky">
    <input type="submit" name="check_ppl" value="Kontrola stavu odeslání">
    <input type="submit" name="print_ppl" value="Tisk štítků"><br>
  </fieldset>
  <fieldset>
  <legend>DPD</legend>
    <input type="submit" name="dpd_export" value="Odeslat balíky">&nbsp;&nbsp;&nbsp;
    <input type="submit" name="dpd_delete" value="Vymazat balíky">&nbsp;&nbsp;&nbsp;
    <input type="submit" name="dpd_print" value="Tisk soupisky">&nbsp;&nbsp;&nbsp;
    <input type="submit" name="dpd_reprintlabel" value="Opakovaný tisk štítků">&nbsp;&nbsp;&nbsp;
    <input type="text" name="dpd_search_date" size="5">[dd.mm.rrrr] <input type="submit" name="dpd_search" value="Dohledat nedokončené zásilky pro dané datum">&nbsp;&nbsp;&nbsp;
  </fieldset>
  <fieldset>
    <legend>Zásilkovna</legend>
    <select name="formatZasilkovna">
      <option value="A6 on A4">štítek o velikosti 105x148 mm (A6) na stránce velikosti 210x297 mm (A4)</option>
      <option value="A7 on A4">štítek o velikosti 105x74 mm (A7) na stránce velikosti 210x297 mm (A4)</option>
      <option value="A7 on A7">štítek o velikosti 105x74 mm (A7) na stránce o totožné velikosti</option>
      <option value="A9 on A4">štítek o velikosti 52x37 mm (A9) na stránce velikosti 210x297 mm (A4)</option>
    </select>
    Vynechat: <select name="fpZasilkovna">
      <option value="0">0</option>
      <option value="1">1</option>
      <option value="2">2</option>
      <option value="3">3</option>
      <option value="4">4</option>
      <option value="5">6</option>
    </select>
    <input type="submit" name="export_zasilkovna" value="Export zásilkovna"><br>
  </fieldset>
  <fieldset>
    <legend>Hromadný tisk vybraných</legend>
    <input type="submit" name="print_orderstore" value="Vyskladňovací objednávky"> <input type="submit" name="print_invoices" value="Faktury"> <br>
  </fieldset>
  </form>
  <script type="">

  $("#checkAll").click(function(){
    $('.ordid_chk').not(this).prop('checked', this.checked);
});

$('.ajax-popup-link').magnificPopup({
    type: 'ajax',
    alignTop: true,
    closeOnContentClick: true,
    showCloseBtn: true,
    overflowY: 'scroll'
});
  </script>