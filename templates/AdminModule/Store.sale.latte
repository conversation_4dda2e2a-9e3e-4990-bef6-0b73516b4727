{block #content}
 <h3>{block #title}Prodeje {$date|date:'d.m.Y'} pro sklad {$store->stoname} {/block}</h3>
<form action="" method="get">
  <input type="hidden" value="{$store->stoid}" name="stoid">
  Datum: <input type="text" name="date" size="8" value="{$dateFormated}"><small>Zadejte ve tvaru dd.mm.rrrr</small><input type="submit" value="Vyhledat">

</form>
<form action="{plink genStoreTransferFromSale}" method="post">
 <input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše
 <input name="stoidto" value="{$store->stoid}" type="hidden">
 <table class="grid">
   {foreach $stoOpers as $row}
     {if $iterator->isFirst()}
     <tr>
     <th></th>
     <th>Kód</th>
     <th>Ná<PERSON>v</th>
     <th>Prodáno</th>
     <th>Skladem</th>
     <th>Skladem MH</th>
     </tr>
     {/if}
     <tr>
     <td><input class="ordid_chk" type="checkbox" name="items[{$row->proid}]" value="{abs($row->qty)}"></td>
     <td>{$row->procode}</td>
       <td><a href="{plink Product:edit, $row->proid}">{$row->proname}</a></td>
     <td>{$row->qty}</td>
     <td>{$row->qtyfree}</td>
     <td>{$row->qtyfree_2}</td>
     </tr>
     {if $iterator->isLast()}
     {/if}
   {/foreach}
 </table>

 ze skladu:
  {php
  unset($enumStoId[$store->stoid], $enumStoId[1], $enumStoId[3]);
  }
  {foreach $enumStoId as $stoId => $stoName}
   {if $iterator->first}
   <select name="stoidfrom">
   {/if}
     <option value="{$stoId}">{$stoName}</option>
   {if $iterator->last}
   </select>
   {/if}
 {/foreach}
 na sklad {$store->stoname} <input type="submit" name="submit" value="vygenerovat přesun">
 </form>
{control paginator}

<script type="">

  $("#checkAll").click(function(){
    $('.ordid_chk').not(this).prop('checked', this.checked);
  });

</script>

{/block}