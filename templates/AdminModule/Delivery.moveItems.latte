{block #content}

  <h3>{block #title} P<PERSON>es<PERSON> polo<PERSON> dodávky {/block}</h3>

  <p><strong><PERSON>dr<PERSON><PERSON><PERSON><PERSON> dodávka:</strong></p>
  {*<!-- detail dodávky start -->*}
  <p>
    <strong>ID:</strong> {$dataRow->delid}<br>
    <strong>Č<PERSON>lo dokladu:</strong> {$dataRow->delvencode}<br>
    <strong>Číslo nákupní faktury:</strong> {$dataRow->delinvcode}<br>
    <strong>Sklad:</strong> {$enum_stoid[$dataRow->delstoid]}<br>
    <strong>Datum naskladnění:</strong> {$dataRow->deldate|date:'d.m.Y'} {form changeDateForm}{input deldate}{input submit}{/form}<br>
    <strong>Dodavatel:</strong> {$enum_venid[$dataRow->delvenid]} |
    <strong>Měna:</strong> {$dataRow->delcurcode} |
    <strong>Kurz:</strong> {$dataRow->delcurrate} |
    <strong>Stav:</strong> {$enum_delstatus[$dataRow->delstatus]}
  </p>
  {*<!-- detail dodávky end -->*}

  {form moveToForm}
     <ul class="error" n:if="$form->hasErrors()">
      <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
    </ul>
    <table>
      <tr>
        <th>{label targetdelid /}:</th>
        <td>{input targetdelid} <small>ponechte prázdné pokud se má vytvořit nová</small></td>
      </tr>
      <tr>
        <th>{label delinvcode /}:</th>
        <td>{input delinvcode}</td>
      </tr>
      <tr>
        <th>{label deldate class=>'required' /}:</th>
        <td>{input deldate} [dd.mm.rrrr]</td>
      </tr>
    </table>
  <h4 id="edititems">Položky dodávky</h4>
    <table class="grid">
      <tr>
        <th>ID</th>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>název</th>
        {if $dataRow->delcurcode == 'EUR'}<th>Cena v €</th>{/if}
        <th>cena bez DPH</th>
        <th>cena s DPH</th>
        <th>počet</th>
        <th>IMEI</th>
        <th colspan="3"></th>
      </tr>
      {foreach  $delItems as $row}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        <td>{php echo $form['items'][$row->deiid]['deiid']->control }</td>
        <td>{$row->deiproid}</td>
        <td>{$row->deiprocode}</td>
        <td>{$row->deiproname}</td>
        {if $dataRow->delcurcode == 'EUR'}<td> {$row->deipriceeur}&nbsp;€</td>{/if}
        <td style="text-align: right;">{$row->deipricenovat}</td>
        <td style="text-align: right;">{$row->deiprice}</td>
        <td style="text-align: right;">{$row->deiqty}</td>
      </tr>
      {/foreach}
    </table>
    {input submit}
  {/form}
{/block}
