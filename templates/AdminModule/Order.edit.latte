{varType App\Orm\Order $order}

{block content}
  {if $popUp}
  {else}

  {include @orderEditAutocomplete.latte}

  {/if}
  {php
    $tdStyle = '#FFFFFF';
    if ($dataRow->ordtype == 1) {
      $tdStyle = '#55C468';
    } else if ($dataRow->ordtype == 3) {
      $tdStyle = '#43BDF8';
    }
  }

<h3 style="background-color: {$tdStyle};color: black">{block #title}Objednávka č. {$dataRow->ordcode} {if !empty($dataRow->ordmalid)} | MALL: <span style="background: #ffaa82">{$dataRow->ordmalid}</span> | {/if} ({$dataRow->ordiname} {$dataRow->ordilname} | {$dataRow->ordstname} {$dataRow->ordstlname}) {if !empty($dataRow->ordip)}, IP: {$dataRow->ordip}{/if}{/block}</h3>
{if !empty($orderValidateError)}
<p style="background-color: #f1a899; padding: 7px; font-weight: bold">{$orderValidateError}</p>
{/if}
  {if !$popUp}
    {if empty($dataRow->ordmalid)}
      {if !empty($dataRow->ordinvcode)}
      <p>
      <b>Faktura č.: </b><a target="invoicePrint" href="{plink Order:printInvoice, $dataRow->ordid, 'P'}"><span style="font-size: 18px;"> {$dataRow->ordinvcode} </span><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>  | <a href="{plink Order:printInvoice, $dataRow->ordid, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a> |
      <a href="{plink CreditNote:addFromOrder, $dataRow->ordid}"  onclick="return DeleteConfirmFront('Opravdu chcete vytvořit dobropis na základě objednávky č. {$dataRow->ordcode}');">Vytvořit dobropis</a>
      </p>
      {else}
      <p>Není přiřazen kód faktury. <a href="{plink setInvoice, $dataRow->ordid}">Vytvořit fakturu</a></p>
      {/if}

      {if !empty($dataRow->ordprocode)}
        <p><b>Proforma č.: </b>{$dataRow->ordprocode} <a target="invoicePrint" href="{plink Order:printProforma, $dataRow->ordid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>  | <a href="{plink Order:printProforma, $dataRow->ordid, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a></p>
      {else}
        <p>Není přiřazen kód proformy.</p>
      {/if}
    {/if}
    {if !empty($dataRow->ordmalid)}
    <p>

    {if count($crns) > 0}
    <p>Dobropisy:
      {foreach $crns as $key => $row}
        {if $identity->isAllowed('Admin:CreditNote', 'edit')}<a href="{plink CreditNote:edit, $row->crnid}">{$row->crncode}</a>{/if}
        <a target="invoicePrint" href="{plink CreditNote:print, $row->crnid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>{if !$iterator->isLast()} | {/if}
      {/foreach}
    </p>
    {/if}

    <form action="{plink CreditNote:addToExist}">
    <input type="hidden" name="ordid" value="{$dataRow->ordid}">
    <a href="{plink CreditNote:addFromOrder, $dataRow->ordid}"  onclick="return DeleteConfirmFront('Opravdu chcete vytvořit dobropis na základě objednávky č. {$dataRow->ordcode}');">Vytvořit dobropis</a> |
    Kód dobropisu: <input type="text" name="crncode"><input type="submit" name="submit" value="Přidat do dobropisu"></form>

    Výdejka: <a target="invoicePrint" href="{plink Order:printDeliveryNote, $dataRow->ordid, 'P'}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>  | <a href="{plink Order:printDeliveryNote, $dataRow->ordid, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a>
    </p>

    {/if}
    <p>

  {if !empty($dataRow->ordmail)}
    <a href="mailto:{$dataRow->ordmail}?subject=Objednávka č. {$dataRow->ordcode} na {$presenter->config['SERVER_NAMESHORT']}&body=potvrzení objednávky">Mailovat zákazníkovi</a> | {/if}
  {if !empty($dataRow->ordreferer)}
  Referer: <a href="{$dataRow->ordreferer}">{$dataRow->ordreferer} | </a>
  {/if}
  {php
  $fieldPrefix = !empty($dataRow->ordstname) ? "ordst" : "ordi";
  }

  <a href="https://vdp.cuzk.cz/vdp/ruian/overeniadresy/vyhledej?as.nazevUl={$dataRow[$fieldPrefix . 'street']}&as.cisDom={$dataRow[$fieldPrefix . 'streetno']}&as.cisOr.cisloOrientacniText=&as.nazevCo=&as.nazevOb={$dataRow[$fieldPrefix . 'city']}&as.psc={$dataRow[$fieldPrefix . 'postcode']}&asg.sort=UZEMI&search=Vyhledat" target="_validate"> Validace adresy </a>

  </p>
  {if !empty($dataRow->ordparcode) && $delmode->delcode == 'ULOZENKA'}
  <form method="get" action="{plink Order:printUlozenkaLabel}">
  Balík: <a href="https://tracking.ulozenka.cz/{$dataRow->ordparcode}" target="baliky">{$dataRow->ordparcode}</a>
  <input type="hidden" name="id" value="{$dataRow->ordparcode}">
  <select name="format">
    <option value="pdf">pdf</option>
    <option value="zpl">zpl</option>
  </select>
  <select name="firstPosition">
    <option value="1">1</option>
    <option value="2">2</option>
    <option value="3">3</option>
    <option value="4">4</option>
  </select>
  <input type="submit" name="export_ulozenka" value="Tisk štítku Uloženka">
  </form>
  {elseif !empty($dataRow->ordparcode) && $delmode->delcode == 'INTIME'}
  <form method="get" action="{plink Order:printIntime}">
  Balík: <a href="https://trace.intime.cz/?orderNumber={$dataRow->ordparcode}" target="baliky">{$dataRow->ordparcode}</a>
  <input type="hidden" name="id" value="{$dataRow->ordparcode}">
  <input type="hidden" name="batchid" value="{$dataRow->orddelbatidintime}">
  <select name="firstPosition">
    <option value="0">0</option>
    <option value="1">1</option>
    <option value="2">2</option>
    <option value="3">3</option>
    <option value="4">4</option>
    <option value="5">5</option>
    <option value="6">6</option>
  </select>
  <input type="submit" name="intime_label" value="Tisk štítku Intime">
  <input type="submit" name="intime_protocol" value="Tisk protokolu">
  </form>
  {/if}
  {if count($orderRemItems) > 0} <h4 style="color: red;">Objednávka není kompletně vykrytá!</h4>{/if}

  {if $isAc}<h4 style="color: red;">Objednávka obsahuje klimatizaci!</h4>{/if}
  {control orderChangeStateForm}
  {/if}
  <h4 id="edititems">Položky objednávky</h4>

  {foreach $orderRemItems as $row}
    {if $iterator->isFirst()}
    <h4 style="color: red;">Nevykryté položky</h4>
    <table class="grid">
    <tr>
    <th>kód</th>
    <th>název</th>
    <th>zbývá</th>
    <th>skladem</th>
    </tr>
    {/if}
    <tr>
    <td>{$row->procode}</td>
    <td>{$row->proname}</td>
    <td>{$row->oriqtyrem}</td>
    <td>
    {ifset $productsFree[$row->proid]}
      {if count($productsFree[$row->proid]) > 0}
    <form style="border: none; margin: 0;" action="{plink ordItemBlock}" method="get">
      {foreach $productsFree[$row->proid] as $key => $text}
        {if $iterator->isFirst()}
      <input type="hidden" name="oriid" value="{$row->oriid}">
      <input type="hidden" name="ordid" value="{$dataRow->ordid}">
      <select name="stiid">
        {/if}
      <option value="{$key}">{$text}</option>
        {if $iterator->isLast()}
      </select>
        {/if}
      {/foreach}

      <input type="text" name="qty" value="{$row->oriqtyrem}" size="3">
      <input type="submit" name="block_items" value="Blokovat">
    </form>
      {else}
      není
      {/if}
    {else}
    není
    {/ifset}
    </td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  {form ordItemsEditForm}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
  </ul>
    <table class="grid">
      <tr>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>název</th>
        <th>cena</th>
        <th>počet</th>
        <th colspan="3"></th>
      </tr>
      <tr>
        <th colspan="10">nová položka</th>
      </tr>
      <tr>
        <td>{php echo $form['newitem']['oriproid']->control }</td>
        <td></td>
        <td>{php echo $form['newitem']['oriname']->control } </td>
        <td>{php echo $form['newitem']['oriprice']->control }</td>
        <td>{php echo $form['newitem']['oriqty']->control }</td>
        <td colspan="3"></td>
      </tr>

      {foreach  $form['items']->getComponents() as $cont}
        {php
        $bgcolor = (!$iterator->isOdd() ? 'bgcolor="#D0D0D0"' : '');
        }
      <tr {$bgcolor}>
      {var $oriid=$form['items'][$cont->name]['oriid']->value}
      {if $cont->name != 'delivery'}
        <td>{php echo $form['items'][$cont->name]['oriproid']->control }</td>
        <td>{$ordItems[$oriid]->oriprocode}
          {if $ordItems[$oriid]->prodelfree === 1}<br>DOPRAVA ZDARMA{/if}
          {if $ordItems[$oriid]->origifttype === App\Orm\BasketItem::GIFT_REJECTED}<br>DÁREK ODMÍTNUT{/if}
          {if $ordItems[$oriid]->protypid5 == 1}<br><span style="background: yellow"><a href="{plink productFixOff $ordItems[$oriid]->oriproid, $dataRow->ordid}" title="Odfixovat výprodej">VÝPRODEJ</a></span>{/if}
        </td>
        <td>
          {if $ordItems[$oriid]->prospecmode === 1}<span style="font-size: larger; background: orangered; padding: 3px;">Z</span>{/if}
        {php echo $form['items'][$cont->name]['oriname']->control->size(90) }
        {ifset $form['items'][$cont->name]['sn']}
        <br>IMEI: {php echo $form['items'][$cont->name]['sn']->control }
        {/ifset}
        {php
        $arr = explode('|', trim($ordItems[$oriid]->orisn, '|'));
        }
        {foreach $arr as $i}
          {if $iterator->isFirst()}
          <br>IMEI/SN:
          {/if}
          <a href="{plink 'DelItemsImeis:default' 'sImei'=>$i}" title="Vyhledat naskladnění">{$i}</a>
          {if !$iterator->isLast()}
          ,
          {/if}
        {/foreach}

        {* vypis blokaci polozky objednavky *}

        {ifset $orderItemsBlock[$oriid]}
          {foreach $orderItemsBlock[$oriid] as $brow}
            {if $iterator->isFirst()}
          <table class="grid" style="padding: 0; margin: 0; background-color: #80FF80;">
            {/if}
          <tr>
          <td>Sklad: {$brow->stoname}</td>
          <td>{$brow->deldate|date:'d.m.Y'}</td>
          {if !empty($brow->stistoidfrom)}<td>přesunuto z {$enum_stoid[$brow->stistoidfrom]}</td>{/if}
            {if $admin->admrole !== 'prodavac'}{if !empty($brow->delid)}<td><a href="{plink Delivery:edit $brow->delid}" target="_blank">dodávka</a></td>{/if}<td>Dodavatel: {$brow->venname}</td>{/if}
          <td>{$brow->stiqty} ks</td>
          <td>{$brow->deiprice|formatPrice|showFieldByPermission:'deiprice':$admin->admid:$admin->admrole}</td>
          {if $brow->delstatus == 2}<td style="background: #f1a899">Na cestě {if !empty($brow->deldateexpected)}({$brow->deldateexpected|date:'d.m.Y'}){/if}</td>{/if}
          {if !empty($orderItemsTransInfo[$brow->stiid])}
            <td>
              {foreach $orderItemsTransInfo[$brow->stiid] as $iBlock}
                {if $iBlock->sttid > 0}
                  Přesun: <a href="{plink StoreTransfer:edit $iBlock->sttid}" target="_blank">{$iBlock->sttid}</a>|{$iBlock->sttdate|date:'d.m.Y'}|{$enum_stoid[$iBlock->sttstoidfrom]} -> {$enum_stoid[$iBlock->sttstoidto]}
                  {if !$iterator->isLast()}
                    <br>
                  {/if}
                {/if}
              {/foreach}
            </td>
          {/if}


          </tr>
            {if $iterator->isLast()}
          </table>
            {/if}
          {/foreach}
        {/ifset}
        {if isset($heurekaPaths[$oriid]) && empty($dataRow->ordmalid) && $dataRow->ordtype==3}
          Heureka marže: <a href="https://sluzby.heureka.cz/napoveda/kosik-provize/" target="heureka_provize">{$heurekaPaths[$oriid]}</a>
        {/if}
        {if $ordItems[$oriid]->oristtids != ''}
          {php
            $arr = explode(',', trim($ordItems[$oriid]->oristtids, ','));
          }
          {foreach $arr as $oristtid}
          <a href="{plink StoreTransfer:edit $oristtid}" style="background-color: #FFBD69"> AUTO.PŘES. </a>&nbsp;
          {/foreach}

        {/if}
        {*
        {ifset $form['items'][$cont->name]['sopstoidto']}
        {if empty($brow->stistoidfrom)}Přesun na: {php echo $form['items'][$cont->name]['sopstoidto']->control }{else} <small>Nelze už znovu přesunout položku, která už byla jednou přesunuta. Pokud chcete přesun smazat nebo změnit vymažte rezervaci.</small>{/if}
        {/ifset}
        *}
        </td>
        <td>
          {php echo $form['items'][$cont->name]['oriprice']->control }{if $ordItems[$oriid]->oriprice != $ordItems[$oriid]->oripriceoriginal}<br>Původní cena: {$ordItems[$oriid]->oripriceoriginal}{/if}
          {if !empty($ordItems[$oriid]->orimallmargin)}M:{$ordItems[$oriid]->orimallmargin}%{/if}
          {if !empty($ordItems[$oriid]->oriaffmargin)}A:{$ordItems[$oriid]->oriaffmargin}%{/if}
          {if !empty($ordItems[$oriid]->origiftdisc)}<br></br>Sleva:{$ordItems[$oriid]->origiftdisc|formatPrice}{/if}
        </td>
        <td style="background-color: {if $ordItems[$oriid]->oriqty - $ordItems[$oriid]->oriqtyfil > 0}red{else}#00CC00{/if}; ">{php echo $form['items'][$cont->name]['oriqty']->control } {if $ordItems[$oriid]->oriqtyfil < $ordItems[$oriid]->oriqty}<br><strong>zbývá: {$ordItems[$oriid]->oriqty - $ordItems[$oriid]->oriqtyfil}ks</strong>{/if}<strong><br>Skladem: {$ordItems[$oriid]->proqty}</strong></td>
        <td><a href="{plink Order:deleteItem, $form['items'][$cont->name]['oriid']->value, $dataRow->ordid}" onclick="return DeleteConfirm('položku objednávky {$form['items'][$cont->name]['oriname']->value}');" title="vymazat položku"> <img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" /></a></td>
        <td><a href="{plink Order:itemBlockDelete, $form['items'][$cont->name]['oriid']->value, $dataRow->ordid}" onclick="return DeleteConfirm('načtené IMEI a skladovou blokaci položky {$form['items'][$cont->name]['oriname']->value}');" title="vymazat blokaci zboží"> <img src="{$baseUrl}/img/admin/lock_delete.png" width="16" height="16" /></a></td>
        <td>
          <a href="{plink Product:edit, $ordItems[$oriid]->oriproid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a>
          {if ($ordItems[$oriid]->provenid == 'bakr')}<br><a href="https://www.bakr.cz/default.asp?cls=stoitem&stiid={$ordItems[$oriid]->procodeven}" target="bakr"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16"  alt="vyhledat na stránkách Bakru" title="vyhledat na stránkách Bakru" /></a>{/if}
          {if ($ordItems[$oriid]->oriproid > 0)}<br><a href="{plink Complaint:edit 0, $oriid}" target="complaint" title="Reklamace">&#129691;</a>{/if}
        </td>
      {else}
        <td>Doprava/platba</td>
        <td></td>
      <td  n:class="$orderDelMode->delid==167 ? 'yellowDelivery'">
        {php echo $form['items'][$cont->name]['oriname']->control->size(90) }
        </td>
        <td>
          Cena: {$ordItems[$oriid]->oriprice}<br />
          {php echo $form['items'][$cont->name]['oripricemaster']->control->size(3) }
        </td>
        <td>1</td>
        <td colspan="3"></td>
      {/if}
      </tr>
        {if isset($insurances[$oriid])}

          {foreach $insurances[$oriid] as $isRow}

            <tr {$bgcolor}>
              <td></td>
              <td></td>
              <td>{$isRow->oriname}</td>
              <td>{$isRow->oriprice}</td>
              <td>{$isRow->oriqty}</td>
              <td></td>

            </tr>

          {/foreach}
        {/if}
      {/foreach}
      {if $ordItemPayCard}
        <tr>
          <th colspan="10">{$ordItemPayCard->oriname}</th>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td>{$ordItemPayCard->oriname}</td>
          <td>{$ordItemPayCard->oriprice}</td>
          <td>1</td>
          <td></td>
        </tr>
      {/if}
      {if $ordItemDisc}
      <tr>
        <th colspan="10">Sleva</th>
      </tr>
      <tr>
        <td></td>
        <td></td>
        <td>{$ordItemDisc->oriname}</td>
        <td>{$ordItemDisc->oriprice}</td>
        <td>1</td>
        <td></td>
      </tr>
      {/if}
      {if $order->coupon}
      <tr>
      <tr>
        <th colspan="10">Kupon</th>
      </tr>
        <td></td>
        <td></td>
        <td>{$order->coupon->campaign->name}: {$order->coupon->code}</td>
        <td>{$order->coupon->campaign->val} {=App\Orm\Campaign::VALTEXT[$order->coupon->campaign->valType]}</td>
        <td>1</td>
        <td></td>
      {/if}
      <tr>
        <td colspan="10">
        {if $currentDelPrice != $validDelPrice}
        <div class="flash err">Cena dopravy nesouhlasí, měla by být {$validDelPrice|formatPrice}!</div>
        {/if}
        {php  echo $form['recalcDelivery']->control } přepočítat cenu dopravy<br>
        {php  echo $form['setFreeDelivery']->control } nastavit dopravu zdarma<br>
        {if !$popUp}{input saveitems} <a href="{plink editInsurance $dataRow->ordid}">Upravit pojištění</a>{/if}</td>
      </tr>
    </table>
  {/form}
  {if !$popUp}
  {control orderEditForm}
  {control orderAddNoteForm}

  <h3>Přílohy</h3>
  {control addAttachmentForm}

  {foreach $attachments as $row}
    {if $iterator->first}
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->oraname}</td>
    <td>{$row->oraextension}</td>
    <td>[ <a href="{plink Order:getAttachment, $row->oraid}" target="_blank">otevřít</a> ]</td>
    <td><a href="{plink Order:deleteAttachment, $row->oraid}"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" onclick="return DeleteConfirm('přílohu {$row->oraname}');" /></a></td>
    </tr>
    {if $iterator->last}
    </table>
    {/if}
  {/foreach}

  {/if}
  <script type="">
  function fillInvCode($code) {
    $('#ordinvcode').val($code);
    return (false);
  }
  </script>

  {control addZasilkovnaClaimForm}

  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie objednávky</h3>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->orldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_ordstatus[$row->orlstatus]}</td>
    <td>{$row->orladmname}</td>
    <td>{$row->orlnote}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}

  {if !$popUp}
  {foreach $statusSysLog as $row}
    {if $iterator->isFirst()}
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->osldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_ordsysstatus[$row->oslstatus]}</td>
    <td>{$row->osladmname}</td>
    <td>{$row->oslnote}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}

  {foreach $order->fioTransactions as $row}
  {if $iterator->isFirst()}
    <h3>Historie online platby</h3>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->dateCreated|date:'d.m.Y H:i:s'}</td>
    <td>{$row->prcode}</td>
    <td>{$row->srcode}</td>
    <td>{$row->resulttext}</td>
    <td>{$row->dateUpdated|date:'d.m.Y H:i:s'}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  {/if}

  <script type="text/javascript">
  $(document).ready(function () {
    $(".biginput").focus();
    $("#orddelspeczasilkovna_label").on('input', function(){
      if ($(this).val().length == 0) {
        $("#orddelspeczasilkovna").val('');
      }
    });
    $("#orddelspecbalikovna_label").on('input', function(){
      if ($(this).val().length == 0) {
        $("#orddelspecbalikovna").val('');
      }
    });
    $("#orddelspec_label").on('input', function(){
      if ($(this).val().length == 0) {
        $("#orddelspec").val('');
      }
    });

  });
  </script>

  {if !empty($dataRow->ordmalid)}
  <a href="https://mpapi.mallgroup.com/v1/orders/{$dataRow->ordmalid}?client_id=dfc35543af9b9b3dd1203aeb8e4ef1270961f2a57ee62ff02d9fcf9150e32f64" target="_blank">Mall data</a>
  {/if}

  {if !empty($dataRow->ordstatstatus)}
    <p>Status pro statistiku: {$dataRow->ordstatdate|date:'d.m.Y H:i:s'} | {$enum_ordstatus[$dataRow->ordstatstatus]} {ifset $enum_admid[$dataRow->ordstatadmid]}| {$enum_admid[$dataRow->ordstatadmid]}{/ifset}</p>
  {/if}

  {if $dataRow->ordheurekaoff == 1}
  <p>Heureka vypnuta</p>
  {/if}


  {if !empty($ulozenkaData)}
  Stav Uloženka: {print_r($ulozenkaData, TRUE)}
  {/if}
  {/block}
