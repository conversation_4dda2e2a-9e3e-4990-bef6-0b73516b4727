{varType App\Orm\Catalog[] $catalogs}
{varType ?App\Orm\Product[] $products}
{varType ?App\Orm\Catalog $selectCatalog}

{block title}Katalog Obrázky{/block}
{block breadcrumb}
    <ol class="breadcrumb float-sm-right">
        <li class="breadcrumb-item"><a href="/administrace/">Domu</a></li>
        <li class="breadcrumb-item active">Katalog Obrázky</li>
    </ol>
{/block}
{block content}

<div class="row">
    <div class="col-lg-2">
        <div class="card">
            <div class="card-header">
                <h5 class="m-0">Root kategorie</h5>
            </div>
            <div class="card-body">
              <a n:foreach="$catalogs as $catalog" n:href="this catalogId:$catalog->id" class="btn  btn-block {if isset($rootCatalog) && $rootCatalog->id==$catalog->id}btn-primary{else}btn-default{/if} "> {$catalog->catname}</a>
            </div>
            <div class="card-footer">
            <a n:href="autoUpdateImage!" class="btn btn-danger">Auto update</a>
</div>
        </div>
    </div>

    <div class="col-lg-3" n:ifset="$rootCatalog">
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h5 class="m-0">{$rootCatalog->catname}</h5>
            </div>
            <div class="card-body">

                    {var App\Orm\Catalog $loopCatalog=$rootCatalog}
                    {block cataloglist}
                    <ul >
                        <li n:foreach="$loopCatalog->catalogs as $catalog" class="{$catalog->manualSetImage?'text-success':'text-danger'}">
                            <a n:href="this catalogId:$catalog->id" n:class="$selectCatalog->id!=$catalog->id?text-dark">{$catalog->catname} <img n:if="$catalog->getImagSrc()" src="{$catalog->getImagSrc()}" style="max-height: 25px"></a>
                             {if count($catalog->catalogsAll)} {include this, loopCatalog => $catalog}{/if}
                        </li>
                    </ul>
                    {/block}

            </div>
        </div>
    </div>

    <div class="col-lg-6" n:if="$selectCatalog"  >
        <div class="card card-primary card-outline">
            <div class="card-header">
                <h5 class="m-0">{foreach $selectCatalog->getTree() as $one} <a n:href="this catalogId:$one->id" n:class="$iterator->last?font-weight-bold"> {$one->catname }</a> {sep} / {/sep} {/foreach}</h5>
            </div>
            <div class="card-body"  >
<div class="text-center mb-3">
            <img n:if="$selectCatalog->getImagSrc()"  src="{$selectCatalog->getImagSrc()}" class="m-auto" style="max-height: 68px;max-width: 68px">
            </div>
              {control uploadImageForm}
            <table class="table">
                <tr>
                    <th>Produkt</th>
                    <th>Obrázek</th>
                    <th></th>
                </tr>
                {*nextras bug n:foreach="$selectCatalog->products as $product" *}

                <tr n:foreach="$products as $product">
                    <td>{$product->name}</td>
                    <td><img src="{$product->getImage()->getPathList()}" style="max-height: 45px"> </td>
                    <td> <a n:href="setImageFromProduct! $product->id" class="btn btn-sm btn-outline-primary">Nastavit</a> </td>
                </tr>
            </table>

            </div>
             <div class="card-footer">

                </div>
        </div>
    </div>



</div>


    <div class="modal fade" id="editNameModal" tabindex="-1" aria-labelledby="editNameModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Editace Názvu</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input class="form-control" type="text" id="inputEditName" placeholder="Default input">
                    <input type="hidden" id="what">
                    <input type="hidden" id="id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Zavřit</button>
                    <button type="button" id="saveEditName" class="btn btn-primary" data-dismiss="modal">Uložit</button>
                </div>
            </div>
        </div>
    </div>


{/block}
{block scripts}
{include parent}
 <script>





      $(function () {





 });
     </script>
{/block}