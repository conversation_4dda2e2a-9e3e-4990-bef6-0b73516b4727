{layout NULL}

{block #content}
<div id="custom-content" class="white-popup-block" style="max-width:600px; margin: 20px auto; background-color: white; padding: 50px;">
  <h3>Objednávka {$order->ordcode} ({$order->ordiname} {$order->ordilname} | {$order->ordstname} {$order->ordstlname})</h3>
  {if !empty($order->ordnote)}<strong>Poznámka:</strong><br><div style="color: red; font-weight: bold;">{$order->ordnote|breakLines}</div><br>{/if}
  {if !empty($order->ordnotedel)}<strong>Poznámka pro řidiče:</strong><br><div style="color: red; font-weight: bold;">{$order->ordnotedel|breakLines}</div><br>{/if}
  <div style="font-weight: bold;"><PERSON><PERSON><PERSON> objednávky:</div>
  {foreach $ordItems as $row}
    {if $iterator->isFirst()}
  <table class="grid">
  <tr>
    <th>Kód</th>
    <th>Název</th>
    <th>Cena</th>
    <th>Objednáno</th>
    <th>Zbývá</th>
    <th>Skladem</th>
  </tr>
    {/if}
  <tr>
    <td>{$row->procode}</td>
    <td>{$row->proname}</td>
    <td style="text-align: right;">{$row->oriqty}</td>
    <td style="text-align: right;">{$row->oriprice|formatPrice}</td>
    <td style="text-align: right;">{if $row->oriqtyfil < $row->oriqty}{$row->oriqty - $row->oriqtyfil}{else}0{/if}</td>
    <td style="text-align: right;">{$row->proqty}</td>

  </tr>
    {if $iterator->isLast()}
  <tr>
    <td></td>
    <td>{$delRow->oriname}</td>
    <td>&nbsp;</td>
    <td style="text-align: right;">{$delRow->oriprice|formatPrice}</td>
    <td>&nbsp;</td>
    <td>&nbsp;</td>
  </tr>
  </table>
    {/if}
  {/foreach}

  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <br><div><strong>Historie objednávky</strong></div>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->orldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_ordstatus[$row->orlstatus]}</td>
    <td>{$row->orladmname}</td>
    <td>{$row->orlnote}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  </div>
{/block}
