{block #content}
<script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'procode');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

 <h3>{block #title}Odpis ze skladu {if $stoid > 0} {$enum_stores[$stoid]} {/if}{/block}</h3>
 {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label code /}: {input code 'id'=>'procode'}
  {label name /}: {input name class=>'autocomplete', 'id'=>'proname'}
  {label stoid /}: {input stoid}<br />
  {input search}
  {input clear}
  </fieldset>
  {/form}
 
 {if $stoid > 0}
 {foreach $stoitems as $row}
 {if $iterator->isFirst()} 
 <form method="post" action="{plink Store:batchWriteOff}">
 <input type="hidden" name="stoid" value="{$stoid}">
 <table class="grid">
  <tr>
    <th>Id</th>
    <th>Kód</th>
    <th>Název</th>
    <th>Skladem</th>
    <th>Odepsat</th>
    <th></th>
  </tr>
  {/if} 
  <tr>
    <td>{$row->proid}</td>
    <td>{$row->procode}</td>
    <td>{$row->proname}</td>
    <td style="text-align: right;">{$row->stiqtyfree}</td>
    <td style="text-align: right;"><input type="text" name="qty_off[{$row->proid}]" size="2">ks</td>
    <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
  </tr>
{if $iterator->isLast()}
</table>
Důvod odpisu: <input type="text" name="note" size="100"><br>
<input type="submit" name="submit" value="Odepsat ze skladu {$enum_stores[$stoid]}">
</form>
{/if}
{/foreach}  
{/if}
{if count($stoitems) == 0}<p>Nic nenalezeno, nebo není vybrán sklad</p>{/if} 
{/block}