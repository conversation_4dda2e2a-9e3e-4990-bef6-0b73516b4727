{block #content}
  <h3>{block #title}Zákazníci{/block}</h3>
  {$control['searchForm']->render('begin')}
  <fieldset>
  <legend>Vyhledávání</legend>
  {$control['searchForm']['ic']->label} {$control['searchForm']['ic']->control}
  {$control['searchForm']['stname']->label} {$control['searchForm']['stname']->control}
  {$control['searchForm']['firname']->label} {$control['searchForm']['firname']->control}
  {$control['searchForm']['email']->label} {$control['searchForm']['email']->control}
  {$control['searchForm']['prccat']->label} {$control['searchForm']['prccat']->control}
  {$control['searchForm']['search']->control}
  {$control['searchForm']['clear']->control}
  </fieldset>
  {$control['searchForm']->render('end')}

  {* strankovani *}
  {control paginator}
  <table class="grid">
  <tr>
    <th>J<PERSON>no</th>
    <th>Firma</th>
    <th>IČ</th>
    <th>Sleva</th>
    <th>Cena</th>
    <th>Email</th>
    <th>Mobil</th>
    <th>Status</th>
    <th></th>
    <th></th>
  </tr>
{foreach $dataRows as $row}
  <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
    <td>{$row->usriname} {$row->usrilname}</td>
    <td>{$row->usrifirname}</td>
    <td>{$row->usric}</td>
    <td>{$row->usrdiscount}%</td>
    <td>{$row->usrprccat|upper}</td>
    <td>{$row->usrmail}</td>
    <td>{$row->usrtel}</td>
    <td>{$enum_usrstatus[$row->usrstatus]}</td>
    <td><a href="{plink User:edit, $row->usrid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" /></a></td>
    <td><a href="{plink User:delete, $row->usrid}" onclick="return DeleteConfirm('účet {$row->usrmail}');"><img src="{$baseUrl}/ico/delete.png" width="16" height="16" /></a></td>
  </tr>
{/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}
