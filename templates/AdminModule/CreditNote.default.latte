{block #content}
  <h3>{block #title}Sez<PERSON>ů{/block}</h3>
  <p><a href="{plink CreditNote:edit}">Založit dobropis</a></p>

  {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label code /} {input code}
  {label name /} {input name}
  {label mail /} {input mail}
  {label admin /} {input admin}<br />
  {label status /} {input status}
  {label orderby /} {input orderby} {input orderbytype}
  {label datefrom /} {input datefrom}
  {label dateto /} {input dateto}
  {label firid /} {input firid}
  {input search}
  {input clear}
  </fieldset>
  {/form}



<table class="grid">
  <tr>
    <th>Č. dobr.</th>
    <th>O<PERSON><PERSON><PERSON><PERSON></th>
    <th>Zákazník</th>
    <th>Datum</th>
    <th>Cena s DPH</th>
    <th>Status</th>
    <th colspan="2"></th>
    <th></th>
    <th></th>
  </tr>
  {var $sum = 0}
  {foreach $dataRows as $row}
  {php
    $sum += $row->crnpricevat;
    $tdStyle = 'bgcolor="#FFFFFF"';

    $style='';
    switch ($row->crnfirid) {
      case 1:
        $style='';
        break;
      case 2:
        $style='bgcolor="#C0C0C0"';
        break;
      case 3:
        $style='bgcolor="#FFFFC0"';
        break;
    }
  }

  <tr {$style}>
    <td {$tdStyle}>{$row->crncode}</td>
    <td>{$row->admname}</td>
    <td>{$row->crnfirname} {$row->crnname} {$row->crnlname}</td>
    <td>{(!empty($row->crndate) ? $row->crndate : $row->crndatec)|date:'d.m.Y'}</td>
    <td style="text-align: right;white-space:nowrap">{$row->crnpricevat|formatPrice}</td>
    <td>{$enum_crnstatus[$row->crnstatus]}</td>
    <td><a href="{plink CreditNote:edit, $row->crnid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" /></a></td>
    <td>{if $row->crnusrid > 0}<a href="{plink User:edit, $row->crnusrid}"><img src="{$baseUrl}/img/admin/user.png" width="16" height="16" /></a>{/if}</td>
    <td><a target="invoicePrint" href="{plink CreditNote:print, $row->crnid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a> <a href="{plink CreditNote:print, $row->crnid, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a></td>
    <td><a href="{plink CreditNote:delete, $row->crnid}"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" onclick="return DeleteConfirm('dobropis č. {$row->crncode}');" /></a></td>
  </tr>
  {/foreach}


  <tr>
    <td colspan="4" ><strong>Celkem:</strong>    </td>
    <td style="text-align: right;"><strong>{$sum|formatPrice}</strong>    </td>
    <td colspan="9" ></td>
  </tr>
  </table>

  {control paginator}
{/block}
