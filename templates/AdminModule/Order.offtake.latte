{block #content}
  <h3>{block #title}Seznam objednávek s osobním odběrem{/block}</h3>


<form method="get" action="{plink Order:batchActionOfftake}">
 <input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše
<table class="grid">
  <tr>
    <th> </th>
    <th>Č. obj.</th>
    <th>Č. fa.</th>
    <th>Zákazník</th>
    <th>Datum</th>
    <th>Cena s DPH</th>
    <th>Doprava, platba</th>
    <th>Pozn.</th>
    <th>Status</th>
  </tr>
  {php
    $colors[0] = "#FF8080";
    $colors[1] = "#D801E9";
    $colors[2] = "#FFFF80";
    $colors[3] = "#80FF80";
    $colors[4] = "#8080FF";
    $colors[5] = "#969696";
    $colors[6] = "#FF80FF";
    $colors[7] = "#000000";
    $colors[8] = "#00FFFF";
    $colors[9] = "#80FF80";
    $colors[10] = "#FFC140";
    $sum = "";
  }

  {foreach $dataRows as $row}
  {php
    $sum += $row->ordpricevat;

    $alowChangeStatus = ($row->ordstatus == 10 || $row->ordstatus == 9 || $row->ordstatus == 6);
  }
  {var $style= ($iterator->isOdd() ? 'bgcolor="#D0D0D0"' : '')}

  <tr {$style}>
    <td>{if $alowChangeStatus}<input class="ordid_chk" type="checkbox" name="ordid[{$row->ordid}]" value="{$row->ordid}" >{/if}</td>
    <td>{$row->ordcode}</td>
    <td>{$row->ordinvcode}</td>
    <td>{$row->ordiname} {$row->ordilname}, {$row->ordtel}</td>
    <td>{$row->orddatec|date:'d.m.Y H:i:s'}</td>
    <td style="text-align: right;white-space:nowrap">{$row->ordpricevat|formatPrice}</td>
    <td>{$row->delnamemas}, {$row->delname}</td>
    <td>{$row->ordnote|breakLines}</td>
    <td style="color: black; background-color: {$colors[$row->ordstatus]};">{$enum_ordstatus[$row->ordstatus]}</td>
  </tr>
  <tr {$style} style="border-bottom: solid 2px black;">
  <td></td>
  <td colspan="8">
    {foreach $row->items as $irow}
      {$irow->oriname} ({$irow->oriprice|number:2:',', ' '}Kč)
      {if !$iterator->isLast()}
      <br />
      {/if}
    {/foreach}
  </td>
  </tr>
  {/foreach}
  </table>
  <input type="submit" name="change_status_9" value="Připraveno k odběru">
  <input type="submit" name="change_status_6" value="Zaplaceno">
  <input type="submit" name="print_getmobiles" value="Protokol o převzetí zboží">
  <input type="submit" name="print_getmoney" value="Protokol o převzetí peněz">

  </form>
  <script type="">
  $("#checkAll").click(function(){
    $('.ordid_chk').not(this).prop('checked', this.checked);
  });
  </script>


  {control paginator}
{/block}
