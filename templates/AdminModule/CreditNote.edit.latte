{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).keydown(function (event) {
      if (event.which == 13) {
        $.ajax({
          url: {plink 'autocompleteProducts'},
          contentType: "application/json; charset=utf-8",
          dataType: "json",
          data: "proid=" + this.value,
          cache: false,
          complete: function (data) {
            if (data.readyState == 4) {
              id = "#frmitemsEditForm-newitem-";
              $(id + 'criproid').val(data.responseJSON[0].id);
              $(id + 'criname').val(data.responseJSON[0].value);
              $(id + 'criprice').focus().select();
            } else {
              alert('Informace se bohužel nepodařilo načíst.');
            }
          }
        });
        event.preventDefault();
        return false;
      }
    });


    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('criname', 'criproid');
          $("#"+newId).val(ui.item.id);
          newId = id.replace('criname', 'criprice');
          $("#"+newId).val(ui.item.price*(-1));
        }
        return false;
      }
    });
  });
  </script>

  <h3>{block #title}{ifset $dataRow}Dobropis č. {$dataRow->crncode} {if !empty($dataRow->crnname)}({$dataRow->crnname} {$dataRow->crnlname}){/if}{else}Založení dobropisu{/ifset}{/block}</h3>
  {if isset($orders) && count($orders) > 0}
  <p>Příslušné objednávky: {foreach $orders as $ord}<a href="{plink Order:edit $ord->ordid}">{$ord->ordcode}</a>{if !$iterator->last}|{/if}{/foreach}</p>
  {elseif isset($dataRow) && $dataRow->crnordid > 0}
  <p><a href="{plink Order:edit $dataRow->crnordid}">Objednávka</a></p>
  {/if}

  {ifset $dataRow}
  <p>
  Tisk:
    <a target="invoicePrint" href="{plink CreditNote:print, $dataRow->crnid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>  |
    <a href="{plink CreditNote:print, $dataRow->crnid, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a>
    <br>
    Status: {$enum_crnstatus[$dataRow->crnstatus]} | <a href="{plink CreditNote:sendCreditNote, $dataRow->crnid}">Odeslat dobropis zákazníkovi</a><br>

  </p>
  <h4 id="edititems">Položky dobropisu</h4>
  {form itemsEditForm}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
  </ul>
    <table class="grid">
      <tr>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>název</th>
        <th>cena</th>
        <th>počet</th>
        <th colspan="3"></th>
      </tr>
      {foreach  $form['items']->getComponents() as $cont}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
      {var $criid=$form['items'][$cont->name]['criid']->value}
      <td>{php echo $form['items'][$cont->name]['criproid']->control }</td>
      <td>{$items[$criid]->criprocode}</td>
      <td>
        {php echo $form['items'][$cont->name]['criname']->control->size(100) }
        {if !empty($form['items'][$cont->name]['criordmalid']->value)}
        <br>
        <small>
          MALL: {php echo $form['items'][$cont->name]['criordmalid']->control->size(10) }
          Id vratky: {php echo $form['items'][$cont->name]['crimalretid']->control->size(60) }
        </small>
        {/if}
      </td>
      <td>{php echo $form['items'][$cont->name]['criprice']->control }</td>
      <td>{php echo $form['items'][$cont->name]['criqty']->control }</td>
      <td><a href="{plink CreditNote:deleteItem, $form['items'][$cont->name]['criid']->value, $dataRow->crnid}" onclick="return DeleteConfirm('položku objednávky {$form['items'][$cont->name]['criname']->value}');" title="vymazat položku"> <img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" /></a></td>
      <td><a href="{plink Product:edit, $items[$criid]->criproid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a></td>
      </tr>
      {/foreach}
      <tr>
        <th colspan="10">nová položka</th>
      </tr>
      <tr>
        <td>{php echo $form['newitem']['criproid']->control }</td>
        <td></td>
        <td>{php echo $form['newitem']['criname']->control->size(100) }</td>
        <td>{php echo $form['newitem']['criprice']->control }</td>
        <td>{php echo $form['newitem']['criqty']->control }</td>
        <td colspan="3"></td>
      </tr>
      <tr>
        <td colspan="10">{input saveitems}</td>
      </tr>
    </table>
  {/form}
  {/ifset}
  {control editForm}

  <script type="">
  function fillInvCode($code) {
    $('#ordinvcode').val($code);
    return (false);
  }
  </script>
  {ifset $statusLog}
  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie dobropisu</h3>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->orldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_crnstatus[$row->orlstatus]}</td>
    <td>{$row->orladmname}</td>
    <td>{$row->orlnote}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  {/ifset}
  {/block}
