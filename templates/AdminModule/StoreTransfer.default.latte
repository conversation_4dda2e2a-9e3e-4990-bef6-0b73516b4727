{block #content}
<script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'proid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

 <h3>{block #title}Skladové přesuny{/block}</h3>
 <p>[ <a href="{plink edit}">přidat nový přesun</a> ]</p>

 {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label proid /} {input proid} Název zboží:<input type="text" class="autocomplete" id="proname" />
  {label status /} {input status}<br />
  {input search}
  {input clear}
  </fieldset>
  {/form}

 <table class="grid">
  <tr>
    <th>Id</th>
    <th>Sklad z</th>
    <th>Sklad na</th>
    <th>Datum</th>
    <th>Status</th>
    <th></th>
    <th></th>
  </tr>
  {foreach $dataRows as $row}
    {php
    $style = "";
    if ($row->sttstatus == 0) {
      $style = "#FFFF80";
    } else if ($row->sttstatus == 1) {
      $style = "#80FF80";
    }
    }
    <tr {if !empty($style)} style="background-color: {$style|noescape}"{/if}>
      <td>{$row->sttid}</td>
      <td>{$enum_sttstoid[$row->sttstoidfrom]}</td>
      <td>{$enum_sttstoid[$row->sttstoidto]}</td>
      <td>{$row->sttdatec|date:'d.m.Y H:i'}</td>
      <td>{$enum_sttstatus[$row->sttstatus]}</td>
      <td><a href="{plink StoreTransfer:edit, $row->sttid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
      <td>
        <a target="invoicePrint" href="{plink StoreTransfer:printProtocol, $row->sttid}"><img src="{$baseUrl}/img/admin/pdf.png" width="16" height="16" /></a>
        <a href="{plink StoreTransfer:printProtocol, $row->sttid, 'D'}"><img src="{$baseUrl}/img/admin/export.png" width="16" height="16" /></a>
      </td>
    </tr>
  {/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}