{block #content}
<script type="">$(document).ready(function() {
    $('form:first *:input[type!=hidden]:first').focus();
});
</script>
  <h3>{block #title} Editace IMEI položky dodávky {/block}</h3>
  <p>[ <a href="{plink edit $dataRow->deidelid}">zpět na položky dodávky</a> ]</p>
  <h4 id="edititems">{$dataRow->proname}, cena: {$dataRow->deiprice|formatPrice} s DPH</h4>
  {form delItemsImeisEditForm}
    <ul class="error" n:if="$form->hasErrors()">
      <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
    </ul>
    <table class="grid">
      {foreach  $form['items']->getComponents() as $cont}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        {if $iterator->isLast()}
          <td>{php echo $form['items'][$cont->name]['diiimei']->control->class("last")->size(40) }</td>
        {else}
          <td>{php echo $form['items'][$cont->name]['diiimei']->control->class("tab")->size(40) }</td>
        {/if}
      </tr>
      {/foreach}
      <tr>
        <td colspan="8">{input saveitems}</td>
      </tr>
    </table>
  {/form}
  <script type="text/javascript">

  $('input.tab').keydown( function(e) {
    var key = e.charCode ? e.charCode : e.keyCode ? e.keyCode : 0;
    if(key == 13) {
        e.preventDefault();
        var inputs = $(this).closest('form').find(':input:visible');
        inputs.eq( inputs.index(this)+ 1 ).focus();
    }
  });

  $(document).ready(function() {
    $("input:text").focus(function() { $(this).select(); } );
  });

  </script>
{/block}
