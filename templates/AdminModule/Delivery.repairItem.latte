{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('deiname', 'deiproid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

  <h3>{block #title} Oprava položky dodávky {$delItem->proname} {/block}</h3>
  <p>
  Původni hodnoty na položce:<br>
  <strong>Id: </strong>{$delItem->proid} <br>
  <strong>Název: </strong>{$delItem->proname} <br>
  <strong>Cena s DPH: </strong>{$delItem->deiprice|formatPrice}<br>
  <strong>Kusy: </strong>{$delItem->deiqty} <br>
  
  {if count($ordItemsBlocked) === 0}
  Nové hodnoty na položce:<br>
  {form repairItemForm}
  <strong>Id: </strong>{input deiproid} <br>
  <strong>Název: </strong>{input deiname} <br>
  <strong>Cena s DPH: </strong>{input deiprice}<br>
  <strong>Kusy: </strong>{input deiqty} <br>
  {input submit}
  {input delete}
  {/form}
  {else}
  <h3>Nelze provést opravu položky, existují na tuto položku blokace</h3>
  <p>Až odstraníte níže popsané problémy <a href="{plink this}">klikněte sem</a>.</p>
  
  {foreach $ordItemsBlocked as $row}
    {if $iterator->isFirst()}
    <p>
    {/if}
    {if !empty($row->ordid_web1)}
      <a href="https://www.{$serverNames["web1"]}/{plink Order:edit $row->ordid_web1}" target="order_detail">detail objednávky {$serverNames["web1"]}</a><br>
    {elseif !empty($row->ordid_web2)}
      <a href="https://www.{$serverNames["web2"]}/{plink Order:edit $row->ordid_web2}" target="order_detail">detail objednávky na {$serverNames["web2"]}</a><br>
    {/if}
    {if $iterator->isLast()}
    </p>
    {/if}
  {/foreach}
  
  
  {/if}
  </p>
{/block}
