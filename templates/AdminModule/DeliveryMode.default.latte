{varType App\Orm\Delivery[] $deliveries}
{varType int $status}
{*
  DeliveryMode.default.latte

  array $dataRows       - seznam položek
  array $enum_delstatus - ciselnik delstatus
*}

{block content}
 <h3>{block title}Způsoby dodání{/block}</h3>
 <p>[ <a n:href="DeliveryMode:edit, 0, 0">přidat nový způsob dodání</a> ] [ <a href="{plink DeliveryMode:default, 1}">vypsat i blokované položky</a> ]</p>
 <table class="grid delivery">
  <tr>
    <th>Název</th>
    <th>Typ</th>
    <th>Cena</th>
    <th>Limit od</th>
    <th>Limit do</th>
    <th>Pořadí</th>
    <th>Status</th>
    <th></th>

  </tr>
  {foreach $deliveries as $delivery}
    <tr><td colspan="8"></td></tr>
    <tr>
      <th><img src="{$baseUrl}/{$delivery->getDelLogo()}" alt="{$delivery->delname}" height="30"> {$delivery->delname}</th>
      <th colspan="1"><a n:href="DeliveryMode:edit, 0, $delivery->id"><img src="{$baseUrl}/img/admin/add.png" width="16" height="16" alt="přidat další variantu"  title="přidat další variantu" /></a></th>
      <th>{$delivery->delprice}{if $delivery->delpriceinper == 1}%{else}Kč{/if}</th>
      <th>{$delivery->dellimitfrom}Kč</th>
      <th>{$delivery->dellimitto}Kč</th>
      <th>{$delivery->delorder}</th>
      <th>{$delivery->getStatusText()}</th>
      <th><a n:href="DeliveryMode:edit, $delivery->id"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></th>
    </tr>
    {var App\Orm\Delivery[] $payments=$status==0?$delivery->payments:$delivery->paymentsAll}
    {foreach $payments as $payment}
    <tr>
      <td>{$payment->delname}</td>
      <td>{$payment->delcode}</td>
      <td>{$payment->delprice}{if $payment->delpriceinper == 1}%{else}Kč{/if}</td>
      <td>{$payment->dellimitfrom}Kč</td>
      <td>{$payment->dellimitto}Kč</td>
      <td>{$payment->delorder}</td>
      <td>{$payment->getStatusText()}</td>
      <td><a n:href="DeliveryMode:edit, $payment->id"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
    </tr>
    {/foreach}
  {/foreach}
  </table>
{/block}