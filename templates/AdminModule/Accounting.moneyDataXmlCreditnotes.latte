{*
CO NEVIM JSEM ZAPOZNAMKOVAL

DatUcPr, PlnenoDPH, DatSkPoh - stejné jako datum vytvoření fa
Splatno - muzou si nastavit ale mají všude 0
*}
{*contentType application/xml; charset=utf-8*}
<?xml version="1.0" encoding="utf-8"?>
<MoneyData ICAgendy="{$documents[0]->vendor->usric}" KodAgendy="" HospRokOd="{$year}-01-01" HospRokDo="{$year}-12-31" description="faktury přijaté a vydané" ExpZkratka="_FP+FV" ExpDate="{date('Y-d-m', time())}" ExpTime="{date('h:i:s', time())}" VyberZaznamu="0">
  <SeznamFaktVyd>
    {foreach $documents as $doc}
      <FaktVyd>
        <Doklad>{$doc->crncode}</Doklad>
        {*<Rada>162</Rada>*}
        {*<CisRada>49</CisRada>*}
        <Popis>Opravný doklad faktury č. {$doc->ordinvcode}</Popis>
        <Vystaveno>{$doc->crndatec|date:'Y-m-d'}</Vystaveno>
        <DatUcPr>{$doc->crndatec|date:'Y-m-d'}</DatUcPr>
        <PlnenoDPH>{$doc->crndatec|date:'Y-m-d'}</PlnenoDPH>
        <Splatno>{$doc->crndatec|addDaysToDate:14}</Splatno>
        <DatSkPoh>{$doc->crndatec|date:'Y-m-d'}</DatSkPoh>
        <KodDPH>19Ř01,02</KodDPH>
        <ZjednD>0</ZjednD>
        <VarSymbol>{if !empty($doc->crnvarsym)}{$doc->crnvarsym}{else}{$doc->crncode}{/if}</VarSymbol>
        <ParSymbol>{if !empty($doc->crnvarsym)}{$doc->crncode}{else}{$doc->ordcode}{/if}</ParSymbol>
        <PuvDoklad></PuvDoklad>
        <Ucet>RB1</Ucet>
        <Druh>N</Druh>
        <Dobropis>1</Dobropis>
        <Uhrada>{if $doc->crnpayid == 1}hotově{else}převodem{/if}</Uhrada>
        <PredKontac>MALOOBCHOD</PredKontac>
        {ifset $doc->docVat[1]}<SazbaDPH1>{$doc->docVat[1]}</SazbaDPH1>{/ifset}
        <SazbaDPH2>{$doc->docVat[0]}</SazbaDPH2>
        <Proplatit>{round($doc->crnpricevat)}</Proplatit>
        <Vyuctovano>0</Vyuctovano>
        <Celkem>{round($doc->crnpricevat)}</Celkem>
        <PriUhrZbyv>0</PriUhrZbyv>
        <ValutyProp>0</ValutyProp>
        <SumZaloha>0</SumZaloha>
        <SumZalohaC>0</SumZalohaC>
        <DodOdb>
          <KodPartn>{$doc->addguid}</KodPartn>
          <ObchNazev>{if !empty($doc->crnfirname)}{$doc->crnfirname}, {$doc->crnname} {$doc->crnlname}{else}{$doc->crnname} {$doc->crnlname}{/if}</ObchNazev>
          <ObchAdresa>
            <Ulice>{$doc->crnstreet} {$doc->crnstreetno}</Ulice>
            <Misto>{$doc->crncity}</Misto>
            <PSC>{$doc->crnpostcode}</PSC>
            <Stat>Česká republika</Stat>
            <KodStatu>CZ</KodStatu>
          </ObchAdresa>
          <FaktNazev>{if !empty($doc->crnfirname)}{$doc->crnfirname}, {$doc->crnname} {$doc->crnlname}{else}{$doc->crnname} {$doc->crnlname}{/if}</FaktNazev>
          <ICO>{$doc->crnic}</ICO>
          <DIC>{$doc->crndic}</DIC>
          <FaktAdresa>
            <Ulice>{$doc->crnstreet} {$doc->crnstreetno}</Ulice>
            <Misto>{$doc->crncity}</Misto>
            <PSC>{$doc->crnpostcode}</PSC>
            <Stat>Česká republika</Stat>
            <KodStatu>CZ</KodStatu>
          </FaktAdresa>
          <Nazev>{if !empty($doc->crnfirname)}{$doc->crnfirname}, {$doc->crnname} {$doc->crnlname}{else}{$doc->crnname} {$doc->crnlname}{/if}</Nazev>
          <EMail>{$doc->crnmail}</EMail>
          <tel>
            <cislo>{$doc->crntel}</cislo>
          </tel>
        </DodOdb>
        <DopravTuz>0</DopravTuz>
        <DopravZahr>0</DopravZahr>
        <Sleva>0</Sleva>
        <Pojisteno></Pojisteno>
        <PlnenDPH></PlnenDPH>
        <SeznamPolozek>
          {php
          $sum = 0;
          $sumNoVat = 0;
          }
          {foreach $doc->items as $irow}
            {php
              $irow->cripricenovat = ($irow->criprice|getPriceNoVatByVat:$irow->crivat:2);
              $sumNoVat += ($irow->cripricenovat*$irow->criqty);
              $sum += ($irow->criprice*$irow->criqty);
            }
            <Polozka>
              <Popis>{$irow->criname}</Popis>
              <PocetMJ>{$irow->criqty}</PocetMJ>
              <SazbaDPH>{$irow->crivat}</SazbaDPH>
              <Cena>{$irow->criprice}</Cena>
              <CenaTyp>1</CenaTyp>
              <Sleva>0</Sleva>
              <Poradi>{$iterator->getCounter()}</Poradi>
              <KodDPH>19Ř01,02</KodDPH>
              <Predkontac>{if $irow->critypid==1}DOPRAVA{elseif $irow->criprocode=='služba' || $irow->criprocode=='oprava'}SLUŽBY{else}MALOOBCHOD{/if}</Predkontac>
              <Valuty>0</Valuty>
              <NesklPolozka>
                <Zaloha>0</Zaloha>
                <TypZarDoby>N</TypZarDoby>
                <ZarDoba>0</ZarDoba>
                <Protizapis>0</Protizapis>
                <Hmotnost>0</Hmotnost>
              </NesklPolozka>
              <CenaPoSleve>1</CenaPoSleve>
            </Polozka>
          {/foreach}
        </SeznamPolozek>
        <SouhrnDPH>
          <Zaklad0>0</Zaklad0>
          <Zaklad5>0</Zaklad5>
          <Zaklad22>{round($sumNoVat, 2)}</Zaklad22>
          <DPH5>0</DPH5>
          <DPH22>{$doc->crnpricevat-$sumNoVat}</DPH22>
          {if isset($irow->sumNoVat[2]) && $irow->sumNoVat[2] > 0}
          <SeznamDalsiSazby>
            <DalsiSazba>
              <Popis>druhá snížená</Popis>
              <HladinaDPH>1</HladinaDPH>
              <Sazba>10</Sazba>
              <Zaklad>0</Zaklad>
              <DPH>0</DPH>
            </DalsiSazba>
          </SeznamDalsiSazby>
          {/if}
        </SouhrnDPH>
        <MojeFirma>
          <Nazev>{$doc->vendor->usrifirname}</Nazev>
          <Adresa>
            <Ulice>{$doc->vendor->usristreet} {$doc->vendor->usristreetno}</Ulice>
            <Misto>{$doc->vendor->usricity}</Misto>
            <PSC>{$doc->vendor->usripostcode}</PSC>
            <Stat>Česká republika</Stat>
            <KodStatu>CZ</KodStatu>
          </Adresa>
          <ObchNazev>{$doc->vendor->usrifirname}</ObchNazev>
          <ObchAdresa>
            <Ulice>{$doc->vendor->usristreet} {$doc->vendor->usristreetno}</Ulice>
            <Misto>{$doc->vendor->usricity}</Misto>
            <PSC>{$doc->vendor->usripostcode}</PSC>
            <Stat>Česká republika</Stat>
            <KodStatu>CZ</KodStatu>
          </ObchAdresa>
          <FaktNazev>{$doc->vendor->usrifirname}</FaktNazev>
          <FaktAdresa>
            <Ulice>{$doc->vendor->usristreet} {$doc->vendor->usristreetno}</Ulice>
            <Misto>{$doc->vendor->usricity}</Misto>
            <PSC>{$doc->vendor->usripostcode}</PSC>
            <Stat>Česká republika</Stat>
            <KodStatu>CZ</KodStatu>
          </FaktAdresa>
          <Tel>
            <Pred></Pred>
            <Cislo></Cislo>
            <Klap></Klap>
          </Tel>
          <Fax>
            <Pred></Pred>
            <Cislo></Cislo>
            <Klap></Klap>
          </Fax>
          <Mobil>
            <Pred></Pred>
            <Cislo></Cislo>
          </Mobil>
          <EMail></EMail>
          <WWW></WWW>
          <ICO>{$doc->vendor->usric}</ICO>
          <DIC>{$doc->vendor->usrdic}</DIC>
          <KodPartn></KodPartn>
          <FyzOsoba>0</FyzOsoba>
          <MenaSymb>Kč</MenaSymb>
          <MenaKod>CZK</MenaKod>
        </MojeFirma>
      </FaktVyd>
    {/foreach} {*konec faktura*}
  </SeznamFaktVyd>
  <SeznamFaktVyd_DPP/>
</MoneyData>
