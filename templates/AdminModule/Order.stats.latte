{block #content}
  <h3>{block #title} Statistika {/block}</h3>
  {php
     $sts = array(
      'open' => 'Otevřené',
      'close' => 'Uzavřené',
      'cancel' => 'Stornované',
     );
   }

  <form method="get">
  Status objednávky: {foreach $sts as $key => $text}
      {if $iterator->isFirst()}
      <select name="s">
      {/if}
      <option value="{$key}" {if $key==$sStatus} selected="selected"{/if}>{$text}</option>
      {if $iterator->isLast()}
      </select>
      {/if}
    {/foreach}
    Datum od:  <input type="text" name="df" value="{$sDateFrom}" size="6">
    Datum do:  <input type="text" name="dt" value="{$sDateTo}" size="6">
    <input type="checkbox" name="mall" value="1" {if $sIsMall==1}checked{/if}> Mall
    <input type="checkbox" name="heureka" value="1" {if $sIsHeureka==1}checked{/if}> Heureka
    <input type="checkbox" name="kaufland" value="1" {if $sIsKaufland==1}checked{/if}> Kaufland
    <br>

    Dodavatelé:  {foreach $vendors as $key => $text}
    {if $iterator->isFirst()}
      <select name="v">
      <option value=""></option>
      {/if}
      <option value="{$key}" {if $key==$sProVenId} selected="selected"{/if}>{$text}</option>
      {if $iterator->isLast()}
      </select>
      {/if}
    {/foreach}
    <input type="submit" name="new" value="Filtrovat">
     <br>
     Mall, Heureka, Kaufland jsou zahrnuty ve statistikách, když nejsou zaškrtnuty.
     Pokud filtrujete podle dodavatele, filtr Mall, Heureka, Kaufland je ignorován.
  </form> 
  <h3>{$sts[$sStatus]} objednávky</h3>
  <table class="grid">
  <tr>
    <th></th>
    <th>Obrat s dopravou</th>
    <th>Nákup</th>
    <th>Prodej</th>
    <th>Mall</th>
    <th>Zisk</th>
    <th>Obrat ostatní<br>(pojištění, doprava, služby, opravy)</th>
  </tr>
  <tr>
    <th>dnes</th>  
    <td style="text-align: right;">{ifset $ordersDay}{$ordersDay|FormatPriceAdmin}{/ifset}</td>
    <td style="text-align: right;">{$ordersProfitDayRow->buyPrice|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitDayRow->selPrice|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitDayRow->mallMargin|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitDayRow->profit|FormatPriceAdmin}</td>
    <td style="text-align: right;">{ifset $ordersOthersDayRow}{$ordersOthersDayRow->selPrice|FormatPriceAdmin}{/ifset}</td>
  </tr>
  <tr>
    <th>7 dní</th>  
    <td style="text-align: right;">{ifset $ordersWeek}{$ordersWeek|FormatPriceAdmin}{/ifset}</td>
    <td style="text-align: right;">{$ordersProfitWeekRow->buyPrice|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitWeekRow->selPrice|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitWeekRow->mallMargin|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitWeekRow->profit|FormatPriceAdmin}</td>
    <td style="text-align: right;">{ifset $ordersOthersWeekRow}{$ordersOthersWeekRow->selPrice|FormatPriceAdmin}{/ifset}</td>
  </tr>
  <tr>
    <th>30 dní</th>  
    <td style="text-align: right;">{ifset $ordersMonth}{$ordersMonth|FormatPriceAdmin}{/ifset}</td>
    <td style="text-align: right;">{$ordersProfitMonthRow->buyPrice|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitMonthRow->selPrice|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitMonthRow->mallMargin|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitMonthRow->profit|FormatPriceAdmin}</td>
    <td style="text-align: right;">{ifset $ordersOthersMonthRow}{$ordersOthersMonthRow->selPrice|FormatPriceAdmin}{/ifset}</td>
  </tr>
  <tr>
    <th>{$sDateFrom} do {$sDateTo}</th>  
    <td style="text-align: right;">{ifset $ordersFilter}{$ordersFilter|FormatPriceAdmin}{/ifset}</td>
    <td style="text-align: right;">{$ordersProfitFilterRow->buyPrice|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitFilterRow->selPrice|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitFilterRow->mallMargin|FormatPriceAdmin}</td>
    <td style="text-align: right;">{$ordersProfitFilterRow->profit|FormatPriceAdmin}</td>
    <td style="text-align: right;">{ifset $ordersOthersFilterRow}{$ordersOthersFilterRow->selPrice|FormatPriceAdmin}{/ifset}</td>
  </tr>
  </table>


{if isset($ordersProfitFilterRowsAdmin)}
<h3>Zisk po prodejcích {$sDateFrom} do {$sDateTo}</h3>
{php
$sum = 0;
$obrat = 0;
}
{foreach $ordersProfitFilterRowsAdmin as $row}
  {if $iterator->first}
  <table class="grid">
    <tr>
      <th></th>
      <th>Zisk</th>
      <th>Obrat</th>
    </tr>
  {/if}
    <tr>
      <th>{$admins[$row->ordadmid]}</th>
      <td style="text-align: right;">{$row->profit|FormatPriceAdmin}</td>
      <td style="text-align: right;">{$row->selPrice|FormatPriceAdmin}</td>
    </tr>
    {php
    $sum += $row->profit;
    $obrat += $row->selPrice;
    }
  {if $iterator->last}
    <tr>
      <th>e-shop</th>
      <td style="text-align: right;">{$ordersProfitFilterRowEshop->profit|FormatPriceAdmin}</td>
      <td style="text-align: right;">{$ordersProfitFilterRowEshop->selPrice|FormatPriceAdmin}</td>
    </tr>
    {php
      $sum += $ordersProfitFilterRowEshop->profit;
      $obrat += $ordersProfitFilterRowEshop->selPrice;
    }
    <tr>
      <th>Suma</th>
      <th style="text-align: right;">{$sum|FormatPriceAdmin}</th>
      <th style="text-align: right;">{$obrat|FormatPriceAdmin}</th>
    </tr>
  </table>
  {/if}
{/foreach}
{/if}
  {/block}
