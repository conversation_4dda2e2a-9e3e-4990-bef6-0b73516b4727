{varType App\Orm\EcomailCampaign[] $ecomailCampaignsOrm}

{varType App\Orm\Product[] $ecomailCampaignProductsOrm}

{block title} {if isset($ecomailCampaignOrm)}Kampaň <strong>{$ecomailCampaignOrm->name}</strong>{else}Nová kampaň{/if}  {/block}
{block #content}

<div class="row">
    <div class="col-md-6">
        <div class="card card-info m-auto">
            <div class="card-header">
                <h3 class="card-title">{if isset($ecomailCampaignOrm)}Upravit kampaň{else}Vytvořit novou kampaň{/if}</h3>
            </div>
            <div class="card-body">
            {control editCampaignForm}
            </div>
            {if isset($ecomailCampaignOrm)}
              <div class="card-header">
                <h3 class="card-title">Přidat položku</h3>
              </div>
              <div class="card-body">
              {control addProductForm}
              </div>
            {/if}
        </div>
    </div>
</div>

{if isset($ecomailCampaignProductsOrm) && $ecomailCampaignProductsOrm}
<div class="row">
    <div class="col-md-12 mt-3">
      <div class="card ">
        <div class="card-body table-responsive p-0">
          <h3 class="m-2">Položky kamapaně</h3>
          <table class="table">
            <tr>
              <th></th>
              <th>Kód</th>
              <th>Název</th>
              <th>Cena</th>
              <th>Dostupnost</th>
              <th></th>
            </tr>
          {foreach $ecomailCampaignProductsOrm as $productOrm}

            <tr>
              <td><img src="{$baseUrl}/{$productOrm->getImage()->getPathList()}" alt="{$productOrm->name}"></td>
              <td><a href="{plink Product:edit $productOrm->id}" target="_blank">{$productOrm->code}</a></td>
              <td>{$productOrm->name}</td>
              <td>{$productOrm->price|formatPrice}</td>
              <td>{$productOrm->getAccess()} ({$productOrm->getQuantity()} ks)</td>
              <td><a href="{plink deleteFeedItem $ecomailCampaignOrm->id, $productOrm->id}" class="btn btn-sm btn-outline-primary">vymazat</a></td>
            </tr>

          {/foreach}
          </table>
        </div>
      </div>
    </div>
  </div>
{elseif isset($ecomailCampaignsOrm) && $ecomailCampaignsOrm}
  <div class="row">
    <div class="col-md-12 mt-3">
      <div class="card ">
        <div class="card-body table-responsive p-0">
          <h3 class="m-2">Stávající kampaně</h3>
          <table class="table">
            <tr>
              <th>Id</th>
              <th>Název</th>
              <th>Počet adresátů</th>
              <th></th>
              <th></th>
            </tr>
            {foreach $ecomailCampaignsOrm as $campaignOrm}

              <tr>
                <td>{$campaignOrm->id}</td>
                <td>{$campaignOrm->name}</td>
                <td>{$campaignOrm->count}</td>
                <td><a href="{plink campaignList $campaignOrm->id}" target="_blank" class="btn btn-sm btn-outline-primary">upravit</a></td>
                <td><a href="{plink campaignDelete $campaignOrm->id}" class="btn btn-sm btn-outline-primary">vymazat</a></td>
              </tr>

            {/foreach}
          </table>
        </div>
      </div>
    </div>
  </div>
{/if}

