{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).keydown(function (event) {
      if (event.which == 13) {
        $.ajax({
          url: {plink 'autocompleteProducts'},
          contentType: "application/json; charset=utf-8",
          dataType: "json",
          data: "proid=" + this.value,
          cache: false,
          complete: function (data) {
            if (data.readyState == 4) {
              id = "#frmitemsEditForm-newitem-";
              $(id + 'ttiproid').val(data.responseJSON[0].id);
              $(id + 'ttiname').val(data.responseJSON[0].value);
              $(id + 'ttiqty').focus().select();
            } else {
              alert('Informace se bohužel nepodařilo načíst.');
            }
          }
        });
        event.preventDefault();
        return false;
      }
    });

    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('ttiname', 'ttiproid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

  <h3>{block #title} Editace přesunu {/block}</h3>
  <p>[ <a href="{plink edit}">přidat nový přesun</a> ]</p>
  {control editForm}

  {ifset $items}
  <h4 id="edititems">Položky</h4>
  {form itemsEditForm}
    <ul class="error" n:if="$form->hasErrors()">
      <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
    </ul>
    <table class="grid">
      <tr>
        <th>ID zboží</th>
        <th>Katalogové č.</th>
        <th>název</th>
        <th>počet</th>
        <th colspan="2"></th>
      </tr>
      {var $sumQty=0}
      {foreach  $form['items']->getComponents() as $cont}
      {var $ttiid=$form['items'][$cont->name]['ttiid']->value}
      <tr {if $dataRow->sttstatus==0 && (empty($items[$ttiid]->proqty) || ($items[$ttiid]->ttiqty > $items[$ttiid]->proqty))}bgcolor="#FD4501"{else}{if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}{/if}>
        <td>{$items[$ttiid]->ttiproid}</td>
        <td>{$items[$ttiid]->ttiprocode}</td>
        <td>{$items[$ttiid]->ttiproname}</td>
        <td>{php echo $form['items'][$cont->name]['ttiqty']->control }<br>skladem: {if empty($items[$ttiid]->proqty)}0{else}{$items[$ttiid]->proqty}{/if}</td>
        {ifset $form['newitem']}
        <td><a href="{plink StoreTransfer:deleteItem, $ttiid, $dataRow->sttid}" onclick="return DeleteConfirm('položku skladového přesunu');"> <img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" /></a></td>
        {/ifset}
        <td><a href="{plink Product:edit, $items[$ttiid]->ttiproid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16"  alt="upravit položku" title="upravit položku" /></a></td>

      </tr>
      {php
      $sumQty += $items[$ttiid]->ttiqty;
      }
      {/foreach}
      {ifset $form['newitem']}
      <tr>
        <th colspan="10">nová položka</th>
      </tr>
      <tr>
        <td>{php echo $form['newitem']['ttiproid']->control }</td>
        <td></td>
        <td>{php echo $form['newitem']['ttiname']->control } </td>
        <td>{php echo $form['newitem']['ttiqty']->control }</td>
        <td></td>
        <td></td>
      </tr>
      <tr>
        <td colspan="8">{input saveitems}</td>
      </tr>
      {/ifset}
      <tr>
        <td></td>
        <td></td>
        <td style="text-align: right;">celkem {count($items)} položek</td>
        <td style="text-align: right;">{$sumQty}</td>
        <td></td>
        <td></td>
      </tr>
    </table>
  {/form}
  {/ifset}

{include @printZplLabels.latte items=>$traItemsPrint}

  {ifset $statusLog}
  {foreach $statusLog as $row}
    {if $iterator->isFirst()}
    <h3>Historie</h3>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->stldatec|date:'d.m.Y H:i'}</td>
    <td>{$enum_sttstatus[$row->stlstatus]}</td>
    <td>{$row->stladmname}</td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  {if $dataRow->sttstatus==0}
  <a href="{plink StoreTransfer:delete, $id}"  onclick="return DeleteConfirm('přesun');">Vymazat přesun</a>
  {/if}
  {/ifset}
{/block}
