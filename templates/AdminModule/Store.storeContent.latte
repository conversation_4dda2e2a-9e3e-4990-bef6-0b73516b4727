{block #content}
 <h3>{block #title}Obsah skladu {if $stoid > 0} {$enum_stores[$stoid]} {/if}{/block}</h3>

 {foreach $enum_stores as $key=>$name}
   {if $iterator->isFirst()}
 <p>Výpis po produktech:
   {/if}
 <a href="{plink storeContent $key}">{$name}</a>
   {if $iterator->isLast()}
 (nákupní ceny jsou průměrné)</p> 
   {else}
    |  
   {/if}
 {/foreach}
 {foreach $enum_stores as $key=>$name}
   {if $iterator->isFirst()}
 <p>Výpis po nákupních cenách:
   {/if}
 <a href="{plink storeContent $key, 'del'}">{$name}</a>
   {if $iterator->isLast()}
 </p> 
   {else}
    |  
   {/if}
 {/foreach}
 {foreach $enum_stores as $key=>$name}
   {if $iterator->isFirst()}
 <p>Výpis po dodavatelích:
   {/if}
 <a href="{plink storeContent $key, 'ven'}">{$name}</a>
   {if $iterator->isLast()}
 </p> 
   {else}
    |  
   {/if}
 {/foreach}
 {if $stoid > 0}

  {form searchStoreContentForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label catid /} {input catid}
  {input search}
  {input clear}
  </fieldset>
  {/form}

  <a href="{plink this print=>TRUE}">Vytisknout</a>

 <table class="grid">
  <tr>
    <th style="font-size: 10px;">Kód</th>
    <th style="font-size: 10px;" width="300">Název</th>
    {if $type == 'ven'}<th style="font-size: 10px;">Dodavatel</th>{/if}
    <th style="font-size: 10px;">ks</th>
    <th style="font-size: 10px;">Cena<br>bez DPH</th>
    <th style="font-size: 10px;">Cena<br>s DPH</th>
    <th style="font-size: 10px;"></th>
  </tr>
  {php
    $sumQtyFree = 0;
    $sumPrice = 0;
    $sumPriceNoVat = 0;
  }
  {foreach $stoitems as $row}
    <tr>
      {php
        $sumPrice += ($row->stipriceavg * $row->stiqtyfree);   
        $sumPriceNoVat += ($row->stipriceavgnovat * $row->stiqtyfree);   
        $sumQtyFree += $row->stiqtyfree;  
      }
      <td style="font-size: 10px;">{$row->procode}</td>
      <td style="font-size: 10px;">{$row->proname}</td>
      {if $type == 'ven'}<td>{$row->venname}</td>{/if}
      <td style="text-align: right;font-size: 10px;">{$row->stiqtyfree}</td>
      <td style="text-align: right;font-size: 10px;">{$row->stipriceavgnovat|formatPrice}</td>
      <td style="text-align: right;font-size: 10px;">{$row->stipriceavg|formatPrice}</td>
      <td><a href="{plink Product:edit, $row->proid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
    </tr>
    
  {/foreach}
  <tr>
      <th></th>
      <th></th>
      {if $type == 'ven'}<th></th>{/if}
      <th style="text-align: right;font-size: 10px;">{$sumQtyFree}</th>
      <th style="text-align: right;font-size: 10px;">{$sumPriceNoVat|formatPrice}</th>
      <th style="text-align: right;font-size: 10px;">{$sumPrice|formatPrice}</th>
      <th></th>
    </tr>
  </table>
  {/if}
{/block}