
{block #content}
  <h3>{block #title}Produkty{/block}</h3>
<p><a href="{plink //:Front:Batch:importReadyProducts?m=1}">Spustit přenos do ostrého prodeje</a></p>
  {form searchForm}
      <ul class="error" n:if="$form->hasErrors()">
        <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
      </ul>
      {label code /}: {input code}
      {label codeven /}: {input codeven}
      {label name /}: {input name size=>20}
      {label ready /}: {input ready}
      {*
      {label status /}: {input status} |
      {label manid /} {input manid}
      *}
      {input typid} {label typid /}
      {label access /}: {input access}
      {*
      {label orderby /}: {input orderby} {input orderbytype}
      {label catid /}: {input catid}
      *}
      <br />
      {label pagerows /}: {input pagerows} {input search}
      {input clear}

  {/form}

  {control paginator}
  {form listEditForm}
    <ul class="error" n:if="$form->hasErrors()">
      <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
    </ul>
    <input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše | <input type="checkbox" id="checkHeko"> Zaškrtnout/odškrtnou Heureka košík
  {foreach $products as $row}
    {var $container = $form[$row->proid]}
    {if $iterator->first}
    <table class="grid">
      <tr>
        <th></th>
        <th>Heureka<br>košík</th>
        <th>Kód</th>
        <th>Kód dodavatele</th>
        <th>Název produktu</th>
        <th>Výrobce</th>
        <th>Výchozí cena</th>
        <th>Nákupní cena</th>
        <th>Cena | Fixovat</th>
      </tr>
    {/if}
      {formContainer $row->proid}
      <tr data-proid="{$row->proid}" {if $row->proready == 1}style="background-color: greenyellow"{/if}>
        <td>{input selected: class=>"proid_chk"}</td>
        <td>{input proheko: class=>"ischanged heko_chk"}</td>
        <td>
          {$row->procode}
          {if !empty($warranties[$row->proid])}
          {foreach $warranties[$row->proid] as $war}
            <br>
            {$war["prpname"]}:{$war["prpvalue"]}
          {/foreach}
          {/if}
        </td>
        <td>{$row->procodeven}</td>
        <td>
          {input proname: class=>"ischanged ischangednamefix"}
          {input pronamefix class=>"ischanged"}
        </td>
        <td>
          {$row->manname}<br>
          {input promanidfix class=>"ischanged"}
        </td>
        <td>
          SH:{$row["propricec_web2"]|formatPrice}<br>
          SC:{$row["propricec_web3"]|formatPrice}
        </td>
        <td>
          SH:{$row["propriced_web2"]|formatPrice}<br>
          SC:{$row["propriced_web3"]|formatPrice}
        </td>
        <td style="text-align: right">

          SH:{input propricea_web2: class=>"ischanged ischangedprice_web2"} {input propricefix_web2: class=>"ischanged"}<br>
          SC:{input propricea_web3: class=>"ischanged ischangedprice_web3"} {input propricefix_web3: class=>"ischanged"}
        </td>
      </tr>
      {/formContainer}
    {if $iterator->last}
    </table>
    {/if}
  {/foreach}
    Cílový katalog: {input targetCatId}
    <a href="#" onclick="return clearAc();"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a>
    <input type="text" id="catname" size="100"> <br>
    Výrobce: {input targetManId}
    <a href="#" onclick="return clearManAc();"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat výrobce"></a>
    <input type="text" id="manname" size="100"> <br>
    {input save} {input save_setready} {input setready}
  {/form}
  <p>Celkem {count($products)} položek na stránce</p>
  {control paginator}

  <script type="text/javascript">
  var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatId.js"></script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteManId.js"></script>
  <script type="text/javascript">
      $("#checkAll").click(function(){
          $('.proid_chk').not(this).prop('checked', this.checked);
      });

      $("#checkHeko").click(function(){
          $('.heko_chk').not(this).prop('checked', this.checked);
      });

      $('.ischanged').change(function() {
          var proid = 0;
          proid = $(this).closest('tr').data('proid');
          $('#selected_'+proid).prop('checked', true);
      });

      $('.ischangedprice_web2').change(function() {
          var proid = 0;
          proid = $(this).closest('tr').data('proid');
          $('#propricefix_web2_'+proid).prop('checked', true);
      });

      $('.ischangedprice_web3').change(function() {
          var proid = 0;
          proid = $(this).closest('tr').data('proid');
          $('#propricefix_web3_'+proid).prop('checked', true);
      });

      $('.ischangednamefix').change(function() {
          var proid = 0;
          proid = $(this).closest('tr').data('proid');
          $('#pronamefix_'+proid).prop('checked', true);
      });

      $('.ischangedmanidfix').change(function() {
          var proid = 0;
          proid = $(this).closest('tr').data('proid');
          $('#promanidfix_' + proid).prop('checked', true);
      });
  </script>

{/block}