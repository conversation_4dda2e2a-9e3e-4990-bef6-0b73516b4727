{block #content}
  <h3>
   {block #title}Obsah skladu {if $stoid > 0} {$enum_stores[$stoid]} {/if}
   {if !empty($date)}k datu {$date|date:'m.d.Y'} {/if}{/block}
  </h3>

  <p>{$presenter->config["INVOICE_VENDOR_R1"]}, {$presenter->config["INVOICE_VENDOR_R5"]}</p>

  {form searchStoreContentToDateForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label stoid /} {input stoid}
  {label date /} {input date}
  {input search}
  {input export}
  {input clear}
  </fieldset>
  {/form}

  <a href="{plink this print=>TRUE}" target="_blank">Vytisknout</a>

 <table class="grid">

  <tr>
    <th style="font-size: 10px;">Kód</th>
    <th style="font-size: 10px;" width="300">Název</th>
    <th style="font-size: 10px;">ks</th>
    <th style="font-size: 10px;">Cena za kus<br>s DPH</th>
    <th style="font-size: 10px;"></th>
  </tr>

  {php
    $sumQtyFree = 0;
    $sumPrice = 0;
  }

  {foreach $rows as $row}
    {php
      $qtyFree = $row["stiqty_in"] - $row["stiqty_out"];
      if ($qtyFree == 0) {
        continue;
      }
      $stiprice_in = $row["stiprices_in_sum"] / $row["rows_cnt"];
      $sumPrice += $stiprice_in * $qtyFree;
      $sumQtyFree += $qtyFree;

    }
  <tr>
    <td style="font-size: 10px;">{$row["procode"]}</td>
    <td style="font-size: 10px;">{$row["proname"]}</td>
    <td style="text-align: right;font-size: 10px;">{$qtyFree}</td>
    <td style="text-align: right;font-size: 10px;">{$stiprice_in|formatPrice}</td>
    <td><a href="{plink Product:edit, $row["proid"]}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
  </tr>
    
  {/foreach}
  <tr>
      <th></th>
      <th></th>
      <th style="text-align: right;font-size: 10px;">{$sumQtyFree}</th>
      <th style="text-align: right;font-size: 10px;">{$sumPrice|formatPrice}</th>
      <th></th>
    </tr>
  </table>
{/block}