
{block #content}
  <script type="text/javascript">
  var basePath = {$baseUrl};
  </script>
  <script type="text/javascript" src="{$baseUrl}/js/autocompleteCatId.js"></script>

 <h3>{block #title}Nastavení ceny dle katalogu{/block}</h3>
 <p>[ <a href="{plink edit, 0}">nové nastavení</a> ]</p>

{form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label catid /} {input catid size=>3} <input type="text" id="catname" size="80" value="{$catPath}">
      <a href="#" onclick="return clearAc();"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" title="vymazat katalog"></a><br>
  {label serid /} {input serid}
  {label venid /} {input venid}
  {label pricecat /} {input pricecat}
  {label margintype /} {input margintype}
  {label status /} {input status}
  {input search}
  {input clear}
  </fieldset>
{/form}

 <table class="grid">
  <tr>
    <th>Server</th>
    <th>Katalog</th>
    <th>Dodavatel</th>
    <th>Cena</th>
    <th>Marze</th>
    <th>Výpočet</th>
    <th>Status</th>
    <th></th>
    <th></th>

  </tr>
  {foreach $dataRows as $row}
    <tr>
      <td>{$row->cprserid}</td>
      <td>{str_replace("|", " > ", $row->catpath)}</td>
      <td>{$row->cprvenid|capitalize}</td>
      <td>{$row->cprpricecat}</td>
      <td style="text-align: right">{$row->cprmargin} %</td>
      <td>{$enum_cprmargintype[$row->cprmargintype]}</td>
      <td>{$enum_cprstatus[$row->cprstatus]}</td>
      <td><a href="{plink edit, $row->cprid}"><img src="{$baseUrl}/img/admin/edit.png" width="16" height="16" alt="editovat"/></a></td>
      <td><a href="{plink delete, $row->cprid}"><img src="{$baseUrl}/img/admin/delete.png" width="16" height="16" alt="vymazat"/></a></td>
    </tr>
  {/foreach}
  </table>
{/block}