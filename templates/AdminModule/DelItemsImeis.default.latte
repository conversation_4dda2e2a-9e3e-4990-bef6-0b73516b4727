{block #content}
 <h3>{block #title}Vyhledávání IMEI{/block}</h3>
 {form searchForm}
  <fieldset>
  <legend>Vyhledávání</legend>
  {label venid /} {input venid}
  {label imei /} {input imei} 
  {input search}
  {input clear}
  </fieldset>
  {/form} 
 <table class="grid">
  <tr>
    <th>Server</th>
    <th>IMEI</th>
    <th>Kód</th>
    <th>Id</th>
    <th>Sklad</th>
    <th>Dodavatel</th>
    <th>Datum</th>
    <th>Status</th>
    <th colspan="5"></th>
  </tr>
  {foreach $dataRows as $row}
    <tr>
      <td>{$row->diioriserid}</td>
      <td>{$row->diiimei}</td>
      <td>{$row->delvencode}</td>
      <td>{$row->delid}</td>
      <td>{$enum_delstoid[$row->delstoid]}</td>
      <td>{$row->venname}</td>
      <td>{if !empty($row->deldate)}{$row->deldate|date:'d.m.Y'}{/if}</td>
      <td>{$enum_delstatus[$row->delstatus]}</td>
      <td><a href="{plink Delivery:edit, $row->delid}">Detail dodávky</a></td>
      <td>{if !empty($row->ordid_web1)}<a href="{plink Order:edit, $row->ordid_web1}">Detail objednávky {$serverNames["web1"]}</a>{/if}</td>
      <td>{if !empty($row->ordid_web2)}<a href="{plink Order:edit, $row->ordid_web2}">Detail objednávky {$serverNames["web2"]}</a>{/if}</td>
      <td><a href="{plink Product:edit, $row->deiproid}">Detail zboží</a></td>
      <td><a href="{plink Complaint:edit, 0, $row->diioriserid,0, $row->diiimei}">Založit reklamaci</a></td>
    </tr>
  {/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}