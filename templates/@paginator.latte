{if $paginator->pageCount > 1}
<div id="paginator">
  <div class="pager">
	{if $paginator->isFirst()}
	<!--<span class="button">« Předchozí</span>-->
	{else}
	<a href="{plink this, 'page' => $paginator->page - 1}">« Předchozí</a>
	{/if}

	{foreach $steps as $step}
	{if $step == $paginator->page}
		<a href="" class="current pager__current">{$step}</a>
	{else}
		<a href="{plink this, 'page' => $step}">{$step}</a>
	{/if}
	{if $iterator->nextValue > $step + 1}<span class="pager__dots">…</span>{/if}
	{/foreach}

	{if $paginator->isLast()}
	<!--<span class="button">Dal<PERSON><PERSON> »</span>-->
	{else}
	<a href="{plink this, 'page' => $paginator->page + 1}"><PERSON><PERSON><PERSON> »</a>
	{/if}
  </div>
</div>
{/if}
