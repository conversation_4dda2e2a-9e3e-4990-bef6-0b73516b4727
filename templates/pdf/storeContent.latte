
 <h3>Obsah skladu: {$enum_stores[$stoid]}</h3>
 <p>{$presenter->config["INVOICE_VENDOR_R1"]}, {$presenter->config["INVOICE_VENDOR_R5"]}</p>

  <table width="100%" style="border-collapse:collapse;1px solid black">
  <tr>
    <th style="font-size: 10px;border: 1px solid black">Kód</th>
    <th style="font-size: 10px;border: 1px solid black;">Název</th>
    <th style="font-size: 10px;border: 1px solid black;">ks</th>
    <th style="font-size: 10px;border: 1px solid black;">Cena<br>bez DPH</th>
    <th style="font-size: 10px;border: 1px solid black;">Cena<br>s DPH</th>
  </tr>
  {php
    $sumQtyFree = 0;
    $sumPrice = 0;
    $sumPriceNoVat = 0;
  }
  {foreach $stoitems as $row}
    <tr>
      {php
        $sumPrice += ($row->stipriceavg * $row->stiqtyfree);
        $sumPriceNoVat += ($row->stipriceavgnovat * $row->stiqtyfree);
        $sumQtyFree += $row->stiqtyfree;
      }
      <td style="font-size: 10px;border: 1px solid black;">{$row->procode}</td>
      <td style="font-size: 10px;border: 1px solid black;">{$row->proname}</td>
      <td style="text-align: right;font-size: 10px;border: 1px solid black;">{$row->stiqtyfree}</td>
      <td style="text-align: right;font-size: 10px;border: 1px solid black;">{$row->stipriceavgnovat|formatPrice}</td>
      <td style="text-align: right;font-size: 10px;border: 1px solid black;">{$row->stipriceavg|formatPrice}</td>
    </tr>

  {/foreach}
  <tr>
      <th></th>
      <th></th>
      <th style="text-align: right;font-size: 10px;border: 1px solid black;">{$sumQtyFree}</th>
      <th style="text-align: right;font-size: 10px;border: 1px solid black;">{$sumPriceNoVat|formatPrice}</th>
      <th style="text-align: right;font-size: 10px;border: 1px solid black;">{$sumPrice|formatPrice}</th>
      <th></th>
    </tr>
  </table>