
  <p>
    <strong>{$delItem->proname}</strong><br>
    <strong><PERSON><PERSON><PERSON> n<PERSON>up<PERSON>í fa: </strong>{$delItem->delinvcode}<br>
    <strong><PERSON><PERSON><PERSON>: </strong>{$delItem->procode}<br>
    <strong><PERSON><PERSON><PERSON> k<PERSON>: </strong>{$delItem->deiqty}<br>
    <strong><PERSON><PERSON>lo nákupní fa: </strong>{$delItem->delinvcode}
  </p>
  {foreach $ordItemsBlocked as $row}
    {php
    if (isset($orders[$row["ordid_".$row->stioriserid]])) {
      $order = $orders[$row["ordid_".$row->stioriserid]];
    } else {
      continue;
    }
    }
    {if $iterator->first}
    <table class="">
      <tr>
        <th>Eshop</th>
        <th>Kód obj.</th>
        <th>Kód faktury</th>
        <th>Datum vystavení</th>
        <th>Jméno/firma</th>
        <th><PERSON><PERSON></th>
        <th>Nákupn<PERSON> cena</th>
        <th>Prodejn<PERSON> cena</th>
      </tr>
    {/if}
      <tr>
        <td>{$enum_serverId[$row->stioriserid]}</td>
        <td><a href="https://www.{$enum_serverId[$row->stioriserid]}/{plink Order:edit $order->ordid}" target="order">{$order->ordcode}</a></td>
        <td>{$order->ordinvcode}</td>
        <td>{$order->ordinvdate|date:'d.m.Y'}</td>
        <td>{$order->ordiname} {$order->ordilname}, {$order->ordifirname}</td>
        <td>{$row["oriqty_".$row->stioriserid]}</td>
        <td>{$delItem->deiprice|formatPriceAdmin}</td>
        <td>{$row["oriprice_".$row->stioriserid]|formatPriceAdmin}</td>
      </tr>
    {if $iterator->last}
    </table>
    {/if}
  {/foreach}
