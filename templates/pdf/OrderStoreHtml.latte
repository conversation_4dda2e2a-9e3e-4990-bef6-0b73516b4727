{php
  if ($order->ordcurcode === 'EURR') {
    $curCode = 'EUR';
    $decimals = 2;
    $decimalsItems = 2;
  } else {
    $curCode = 'Kč';
    $decimals = 2;
    $decimalsItems = 0;
  }
}
<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/css/bootstrap.min.css" integrity="sha384-zCbKRCUGaJDkqS1kPbPd7TveP5iyJE0EjAuZQTgFLD2ylzuqKfdKlfG/eSrtxUkn" crossorigin="anonymous">

    <title>Objednávka: {$order->ordcode}</title>
</head>
<body>
<div class="m-5">
  <div><a href="{plink Order:default}" class="badge badge-warning">&lt; Zpět na seznam objednávek</a></div>
  <form action="{plink fillOrderStore}" method="get">
    Další objednávka: <input type="text" name="ordcode">
  </form>
<h2>Objednávka: {$order->ordcode}</h2>
  {if empty($order->ordmalid) && $order->ordtype == 3} &nbsp;&nbsp;&nbsp;<span style="font-weight: bold; font-size: 16px; background: #0b0b0b; color: white">Heureka Košík</span>{/if}
    <div class="border">
    {_'Odběratel'}: <br>
    {if !empty($order->ordifirname)}{$order->ordifirname}<br />{/if}
    {$order->ordiname} {$order->ordilname}<br />
    {$order->ordistreet} {$order->ordistreetno}<br />
    {$order->ordipostcode}, {$order->ordicity}<br />
    {if !empty($order->ordic)}<br />{_'IČ'}: {$order->ordic}{/if} {if !empty($order->orddic)}{_'DIČ'}: {$order->orddic}{/if}<br />
    E-mail: {$order->ordmail}<br />
    Tel.: {$order->ordtel}
    <br>
    {if !empty($order->ordstname)}
    <br>Dodací adresa:<br>
    {if !empty($order->ordstfirname)}{$order->ordstfirname}, {/if}
    {$order->ordstname} {$order->ordstlname}<br />
    {$order->ordststreet} {$order->ordststreetno}, {$order->ordstpostcode}, {$order->ordstcity}
    <br>
    {/if}
    </div>

    <table class="table">
      <tr>
        <th>{_'Faktura číslo'}:</th>
        <td>{$order->ordinvcode}</td>
        <th>{_'Datum vytvoření'}:</th>
        <td>{$order->ordinvdate|date:'d.m.Y'}</td>
      </tr><tr>
        <th>{_'Variabilní symbol'}:</th>
        <td>{$order->ordcode}</td>
        <th>{_'Datum splatnosti'}:</th>
        <td>{$dateDue|date:'d.m.Y'}</td>
      </tr><tr>
        <th>{_'Způsob platby'}:</th>
        <td>{$payMode->delname}</td>
        <th>{_'Datum zdanitelného plnění'}:</th>
        <td>{$order->ordinvdate|date:'d.m.Y'}</td>
      </tr><tr>
        <th>{_'Způsob Dodání'}:</th>
        <td>{$delMode->delname}</td>
        <th>Vaše číslo objednávky:</th>
        <td>{$order->ordcodecus}</td>
      </tr>
    </table>
    <br>
    <br>
    <table class="table">
      <thead class="thead-dark">
      <tr>
        <th></th>
        <th>{_'Kód'}</th>
        <th>{_'Název'}</th>
        <th>{_'Kusů'}</th>
        <th>{_'Cena'}</th>
      </tr>
      </thead>
      {foreach $ordItems as $row}
        <tr>
          <td>{if isset($row->proid)}<a href="{plink :Front:Product:detail, $row->proid, getProKey($row)}" target="front"><img src="{$baseUrl}/{getPicName($row, 'product/list')}" /></a>{/if}</td>
          <td>{$row->oriprocode}{if !empty($row->procode2)}<br>EAN: {$row->procode2}{/if}</td>
          <td>{$row->oriname}
              {if !empty($row->orisn)}<br />IMEI/SN: {php echo str_replace("|", ", ", $row->orisn) } {/if}
              {if $row->origiftdisc > 0}<br><small>(původní cena {($row->oriprice + $row->origiftdisc)|formatPrice})</small>{/if}
          </td>
          <td>{if $row->oriqty > 1}<span>&nbsp;{$row->oriqty}&nbsp;</span>{else}{$row->oriqty}{/if}</td>
          <td>{$row->oriprice|formatPrice}</td>
        </tr>
        {ifset $orderItemsBlock[$row->oriid]}
        <tr>
          <td colspan="10">
          {foreach $orderItemsBlock[$row->oriid] as $brow}
            {if $iterator->isFirst()}
            <p style="font-style: italic; font-size: 13px">
            {/if}
              Sklad: {$brow->stoname} |
            {if !empty($brow->stistoidfrom)}
              přesunuto z {$enum_stoid[$brow->stistoidfrom]} |
            {/if}
            {$brow->deldate|date:'d.m.Y'} |
            {if $admin->admrole !== 'prodavac'}
              Dodavatel: {$brow->venname} |
            {/if}
            {$brow->stiqty} ks {if $brow->delstatus == 2} | <strong>Na cestě {if !empty($brow->deldateexpected)}({$brow->deldateexpected|date:'d.m.Y'}){/if}</strong>{/if}
            {if !empty($orderItemsTransInfo[$brow->stiid])}
                {foreach $orderItemsTransInfo[$brow->stiid] as $iBlock}
                  {if $iBlock->sttid > 0}
                    <br>Přesun: {$iBlock->sttdate|date:'d.m.Y'}|{$enum_stores[$iBlock->sttstoidfrom]} -> {$enum_stores[$iBlock->sttstoidto]}
                    {if !$iterator->isLast()}
                      <br>
                    {/if}
                  {/if}
                {/foreach}
            {/if}
            {if $iterator->isLast()}
            </p>
            {/if}
          {/foreach}
          {if $row->oristtids != ''}
            <strong>AUTOMATICKÝ PŘESUN</strong>
          {/if}
          </td>
        </tr>
        {/ifset}
      {/foreach}
        <thead class="thead-dark">
        <tr class="table-active">
          <td>Celková cena {if $order->ordnodph == 0}s DPH{else}bez DPH{/if}</td>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
          <td>&nbsp;</td>
          <td>{$order->ordpricevat|formatPriceAdmin:$decimalsItems:$curCode}</td>
        </tr>
        </thead>
      </table>



</div>
<!-- Optional JavaScript; choose one of the two! -->

<script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js" integrity="sha384-fQybjgWLrvvRgtW6bFlB7jaZrFsaBXjsOMm/tB9LTS58ONXgqbR9W8oWht/amnpF" crossorigin="anonymous"></script>

</body>
</html>