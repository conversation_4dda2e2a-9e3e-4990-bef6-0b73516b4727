{php
	$curCode = 'Kč';
	$decimals = 2;
	$decimalsItems = 0;
}
      <table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
	      <tr>
		      <td style="padding:0.1em 0.5em;"><img src="/img/{$serverId}/faktura-logo.png"></td>
          <td style="padding:0.1em 0.5em;"><p align="right">&nbsp;<font size="5"><font size="4">Dobropis číslo: {$crn->crncode} {if !empty($ord->ordinvcode)}k faktuře č: {$ord->ordinvcode}{/if} ,Opravný daňový doklad</font></p></td>
	      </tr>
      </table>
		  <table width="19cm" border="0" style="border-collapse:collapse;margin-top:10px;"  align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;" valign="top" width="50%"><p><font size="2">{_'Dodavatel'}:<br>
							{if !empty($vendor->usrifirname)}{$vendor->usrifirname}<br />{/if}
              {$vendor->usriname} {$vendor->usrilname}<br />
              {$vendor->usristreet} {$vendor->usristreetno}<br />
              {$vendor->usripostcode}, {$vendor->usricity}<br />
              <br />  
              {if !empty($vendor->usric)}{_'IČ'}:{$vendor->usric}{/if} {if !empty($vendor->usrdic)}{_'DIČ'}:{$vendor->usrdic}{/if}<br />
              Bankovní spojení: {$vendor->usrbankaccount}/{$vendor->usrbankcode}<br />
              E-mail: {$presenter->config["SERVER_MAIL"]}<br />
              Tel.: {$vendor->usrtel}</font></p></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;" colspan="2" valign="top"><p><font size="2">{_'Odběratel'}: <br>
							{if !empty($crn->crnfirname)}{$crn->crnfirname}<br />{/if}
              {$crn->crnname} {$crn->crnlname}<br />
							{$crn->crnstreet} {$crn->crnstreetno}<br />
							{$crn->crnpostcode}, {$crn->crncity}<br />
							<br />	
							{if !empty($crn->crnc)}{_'IČ'}:{$crn->crnic}{/if} {if !empty($crn->crndic)}{_'DIČ'}:{$crn->crndic}{/if}<br />
							E-mail: {$crn->crnmail}<br />
							Tel.: {$crn->crntel}</font></p></td>
				</tr>
			</table>
			<table width="19cm"  style=" margin-top: 10px;border-collapse:collapse;" border="0" align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Dobropis číslo'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$crn->crncode}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum vytvoření'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$crn->crndatec|date:'d.m.Y'}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Variabilní symbol'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{if !empty($crn->crnvarsym)}{$crn->crnvarsym}{else}{$crn->crncode}{/if}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">Datum splatnosti</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$payDate|date:'d.m.Y'}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">Specifický symbol:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{if !empty($crn->crnvarsym)}{$crn->crncode}{else}{ifset $ord}{$ord->ordcode}{/ifset}{/if}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum zdanitelného plnění'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$crn->crndatec|date:'d.m.Y'}</font></td>
				</tr>
        {ifset $enum_crnpayid[$crn->crnpayid]}
        <tr>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">Způsob platby:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$enum_crnpayid[$crn->crnpayid]}</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2"></font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2"></font></td>
        </tr>
        {/ifset}
			</table>
			<table style="background:#fff;border-collapse:collapse;margin-top: 10px;" width="19cm" border="0" cellspacing="0" cellpadding="2" align="center">
				<tr>
          <td colspan="5"><b>Položky dobropisu:</b></td>
        </tr>
        <tr>
          <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;" align="center"><font size="2">{_'Kód'}</font></td>
					<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;" width="40%" align="left"><font size="2">{_'Název'}</font></td>
					<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;" align="center"><font size="2">{_'Kusů'}</font></td>
					<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;" align="center"><font size="2">Bez DPH/ks</font></td>
					<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0" align="center"><font size="2">DPH/ks</font></td>
					<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0" align="center"><font size="2">S DPH/ks</font></td>
					<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0" align="right"><font size="2">Celkem s DPH</font></td>
				</tr>
				{php $sum = 0;}
				{foreach $items as $row}
        {php
          $cripricevat = $row->criprice;
          $cripricenovat = round($cripricevat  / (1 + ($vatSumary[$row->crivatid]['vatLevel'] / 100)), 2);
          $crivat = $cripricevat - $cripricenovat;
        }

				{php $sum += ($row->criprice*$row->criqty);}
				<tr>
          <td style="border-bottom: 1px solid #e0e0e0;" align="center"><font size="2">{$row->procode}</font></td>
					<td style="border-bottom: 1px solid #e0e0e0;"  width="40%" align="left"><font size="2">{$row->criname}</font>
            {if !empty($row->criordmalid) || !empty($row->crimalretid)}
            <br><font size="1"><i>Mall id: {$row->criordmalid}, číslo vrácené dodávky: {$row->crimalretid}</i></font>
            {/if}
          </td>
					<td style="border-bottom: 1px solid #e0e0e0;" align="center"><font size="2">{$row->criqty}</font></td>
					<td style="border-bottom: 1px solid #e0e0e0;" align="center"><font size="2">{$cripricenovat|formatPriceAdmin:$decimals:$curCode}</font></td>
					<td style="border-bottom: 1px solid #e0e0e0;" align="center"><font size="2">{$crivat|formatPriceAdmin:$decimals:$curCode}</font></td>
					<td style="border-bottom: 1px solid #e0e0e0;" align="center"><font size="2">{$row->criprice|formatPriceAdmin:$decimals:$curCode}</font></td>
					<td style="border-bottom: 1px solid #e0e0e0;" align="right"><font size="2">{$row->criprice*$row->criqty|formatPriceAdmin:$decimals:$curCode}</font></td>
				</tr>
				{/foreach}
			</table>
							
			<table width="19cm" style="border-collapse:collapse" border="0" cellspacing="0" cellpadding="2" align="center">
				<tr>
					<td style="font-weight: bold;padding:0.1em 0.5em 0.1em 0;"  width="40%" align="left"><font size="2">{_'Celková cena s DPH'}</font></td>
					<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
					<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
					<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
					<td style="font-weight: bold;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$crn->crnpricevat|formatPrice:2}</font></td>
				</tr>
			</table>
      {if $vendor->usrvat == 1}
      <br />
      <table style="width: 200mm; text-align: center; margin-left: 100mm;" class="border">
        <tr>
          <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">Sazba</font></td>
          <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">bez daně</font></td>
          <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">DPH</font></td>
          <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">s daní</font></td>
        </tr>
        {foreach $vatSumary as $key=>$rowi}
        {if !empty($vatSumary[$key]['pricevat'])}
        <tr>
          <td align="center"><font size="2">{$vatSumary[$key]['vatLevel']}%</font></td>
          <td align="right"><font size="2">{$vatSumary[$key]['price']|formatPriceAdmin:2:$curCode}</font></td>
          <td align="right"><font size="2">{$vatSumary[$key]['vat']|formatPriceAdmin:2:$curCode}</font></td>
          <td align="right"><font size="2">{$vatSumary[$key]['pricevat']|formatPriceAdmin:2:$curCode}</font></td>
        </tr>
        {/if}
        {/foreach}
       </table>
      {/if}
      
      <table width="19cm" style="border-collapse:collapse;margin-bottom: 10px;" cellspacing="0" cellpadding="0">
				
					<tr bgcolor="#e6e6e6">
						<td style="padding:0.5em" align="left" width="50%" bgcolor="#e6e6e6"><font face="Arial" size="2"><strong><big>&nbsp; <b>Dodavatel je plátce DPH</b></big></strong></font></td>
						<td style="padding: 0.5em "  align="right" width="50%"><font face="Arial" size="2"><strong><big>{_'Celkem'}:</big></strong></font> <font face="Arial" size="2"><strong><big>{$crn->crnpricevat|formatPrice:2}</big></strong></font></td>
            
					</tr>
				
			</table>
      <table style="border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm" border="0">
      <tr valign="top">
        <td style="padding:0.1em 0.5em 0.1em 0" align="left"><br>
          Opravný doklad<br>
Důvod opravy: {if !empty($crn->crnreason)}{$crn->crnreason}{else}vrácení peněz (odstoupení od kupní smlouvy){/if}
        </td>
      </tr>
      </table>
      
      
      
      
  <table style="border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm" border="0">
    {if !empty($eet)}
			<tr valign="top">
				<td style="padding:0.1em 0.5em 0.1em 0" align="left"><br>
					<font face="Arial" size="1">
						<strong>Tržba evidovaná v běžném režimu</strong><br>
						<strong>Datum: </strong>{$eet->logdatec|date:'d.m.Y H:i:s'}<br>
						<strong>FIK: </strong> {$eet->logfik}<br>
						<strong>BKP: </strong> {$eet->logbkp}<br>
						<strong>Id provozovny: </strong> {$eet->logprovozid}<br>
						<strong>Id pokladny: </strong> {$eet->logpoklid}<br>
						<strong>Poř. číslo: </strong> {$eet->logid}<br>
					</font>
				</td>
			</tr>
    {/if}
	<tr valign="top">
		<td style="padding:0.1em 0.5em 0.1em 0" align="right"><br>
			<img src="/img/{$serverId}/razitko_{$vendor->usrid}.jpg" width="280" />
    </td>
	</tr>
  </table>