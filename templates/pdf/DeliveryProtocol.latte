<h3><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> č. {$dataRow->delid}</h3>
<p>
  <strong><PERSON><PERSON><PERSON> dokladu:</strong> {$dataRow->delvencode}<br>
  <strong><PERSON><PERSON><PERSON> nákupní fa:</strong> {$dataRow->delinvcode}<br>
  <strong>Datum:</strong> {$dataRow->deldate|date:'d.m.Y'}<br>
  <strong>Sklad:</strong> {$enum_stoid[$dataRow->delstoid]}<br>
  <strong>Dodavatel:</strong> {$vendor->venname}<br>
  <strong>Měna:</strong> {$dataRow->delcurcode}<br>
</p>
<br>
<table>
<tr  style="background-color: #E0E0E0;" >
  <th>Kód</th>
  <th>Název</th>
  <th>Cena<br>bez DPH</th>
  <th>Množství</th>
</tr>
{foreach $items as $key => $row}
{if $iterator->isFirst()}
{/if}
<tr  style="background-color: {if $iterator->isOdd()}#C0C0C0{else}#E0E0E0{/if};" >
  <td>{$row->deiprocode}</td>
  <td>{$row->deiproname}</td>
  <td>{$row->deipricenovat}</td>
  <td style="text-align: right;">{$row->deiqty}</td>
</tr>
{if $iterator->isLast()}
{/if}
{/foreach}
</table>
<br>
<br>
<br>
<br>
<table width="100%">
<tr>
<td width="50%" style="text-align: center;">
....................................... <br>
Předal/a
</td>
<td width="50%" style="text-align: center;">
.......................................<br>
Přijal/a
</td>

</tr>
</table>