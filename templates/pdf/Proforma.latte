{* loader helperů *}

      <table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
	      <tr>
		      <td style="padding:0.1em 0.5em;"><img src="{php echo constant("WWW_DIR")}/img/{$serverId}/faktura-logo.png"></td>
          <td style="padding:0.1em 0.5em;"><p align="right">&nbsp;<font size="5"><b><font size="4">Zálohová faktura číslo: {$order->ordprocode}<br>(ne<PERSON><PERSON> doklad)</font></b></font></p></td>
	      </tr>
      </table>
		  <table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;" valign="top" width="50%"><p><font size="2">{_'Dodavatel'}:<br>
							{if !empty($vendor->usrifirname)}{$vendor->usrifirname|upper}<br />{/if}
              {$vendor->usriname} {$vendor->usrilname}<br />
              {$vendor->usristreet} {$vendor->usristreetno}<br />
              {$vendor->usripostcode}, {$vendor->usricity}<br />
              <br />  
              {if !empty($vendor->usric)}{_'IČ'}:{$vendor->usric}{/if} {if !empty($vendor->usrdic)}{_'DIČ'}:{$vendor->usrdic}{/if}<br />
              Bankovní spojení: {$vendor->usrbankaccount}/{$vendor->usrbankcode}<br />
              E-mail: {$presenter->config["SERVER_MAIL"]}<br />
              Tel.: {$vendor->usrtel}<br />
              <i><small>Dodavatel je registrován pod spisovou značkou oddíl C, vložka číslo
                  51125 ze dne 13.02.2006 veden u Krajského soudu v Ostravě</small></i>
              </font></p></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;" colspan="2" valign="top"><p><font size="2">{_'Odběratel'}: <br>
							{if !empty($order->ordifirname)}{$order->ordifirname}<br />{/if}
              {$order->ordiname} {$order->ordilname}<br />
							{$order->ordistreet} {$order->ordistreetno}<br />
							{$order->ordipostcode}, {$order->ordicity}<br />
							<br />	
							{if !empty($order->ordic)}{_'IČ'}:{$order->ordic}{/if} {if !empty($order->orddic)}{_'DIČ'}:{$order->orddic}{/if}<br />
							E-mail: {$order->ordmail}<br />
							Tel.: {$order->ordtel}</font></p></td>
				</tr>
			</table>
			<table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Faktura číslo'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordinvcode}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum vytvoření'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordprodate|date:'d.m.Y'}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Variabilní symbol'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordcode}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum splatnosti'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$dateDue|date:'d.m.Y'}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob platby'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$payMode->delname}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob Dodání'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$delMode->delname}</font></td>
				</tr>
			</table>
							<table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
								<tr>
                  <td colspan="5"><b>{_'Objednané zboží'}:</b></td>
                </tr>
                <tr>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{_'Kód'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="40%" align="left"><font size="2">{_'Název'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{_'Kusů'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{$presenter->config["CURR_CODE"]} / {_'kus'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$presenter->config["CURR_CODE"]} s DPH</font></td>
								</tr>
								{php $sum = 0;}
								{php $sumNoVat = 0;}
								{foreach $ordItems as $row}
								{php
                $priceVat = $row->oriprice;
                if ($order->ordnodph == 1) {
                  $row->oriprice = ($priceVat|getPriceNoVatByVat:$row->orivat:0);
                  $row->oripricenovat = ($priceVat|getPriceNoVatByVat:$row->orivat:0);
                } else {
                  $row->oripricenovat = ($priceVat|getPriceNoVatByVat:$row->orivat:2);
                }

                $sumNoVat += ($row->oripricenovat*$row->oriqty);
                $sum += ($row->oriprice*$row->oriqty);

                }
								<tr>
                  <td style="border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{$row->oriprocode}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="40%" align="left"><font size="2">{$row->oriname} {if !empty($row->orisn)}<br />IMEI: {php echo str_replace("|", ", ", $row->orisn) } {/if}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{$row->oriqty}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{$row->oriprice|formatPriceAdmin}</font></td>
									<td style="border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$row->oriprice*$row->oriqty|formatPriceAdmin}</font></td>
								</tr>
								{/foreach}
                {php
                if ($order->ordnodph == 1) $order->ordpricevat = $sum;
                }
							</table>
							
							<table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
								<tr>
									<td style="font-weight: bold;padding:0.1em 0.5em 0.1em 0;"  width="40%" align="left"><font size="2">{_'Celková cena s DPH'}</font></td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="font-weight: bold;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$order->ordpricevat|formatPrice:2}</font></td>
								</tr>
							</table>
              {if $vendor->usrvat == 1 && $order->ordnodph == 0}
              <br />
              <table style="width: 200mm; text-align: center; margin-left: 100mm;" class="border">
                <tr>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">Sazba</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">bez daně</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">DPH</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">s daní</font></td>
                </tr>
                <tr>
                  <td><font size="2">21%</font></td>
                  <td><font size="2">{$order->ordprice|formatPrice:2} Kč</font></td>
                  <td><font size="2">{$order->ordpricevat-$order->ordprice|number:2:',':' '} Kč</font></td>
                  <td><font size="2">{$order->ordpricevat|number:2:',':' '} Kč</font></td>
                </tr>
               </table>
              {elseif $order->ordnodph == 1}
               <p>Jedná se o dodání zboží do jiného členského státu dle par.64 odst.1) ZDPH. Daň odvede zákazník.</p>
              {/if}
              
              <table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
								
									<tr bgcolor="#e6e6e6">
										<td style="padding:0.5em" align="left" width="50%" bgcolor="#e6e6e6"><font face="Arial" size="2"><strong><big>&nbsp; <b>Dodavatel je plátce DPH</b></big></strong></font></td>
									  {if $order->ordpaystatus == 1}
                    <td style="padding: 0.5em "  align="right" width="50%"><font face="Arial" size="2"><strong><big>Celkem: {$order->ordpricevat|formatPrice:2}<br>
                    Neplaťte! Již uhrazeno {if !empty($payDate)}{$payDate|date:'d.m.Y'}{/if}</big></strong></font></td>
                    {else}
										<td style="padding: 0.5em "  align="right" width="50%"><font face="Arial" size="2"><strong><big>{_'Celkem k úhradě'}:</big></strong></font> <font face="Arial" size="2"><strong><big>{$order->ordpricevat|formatPrice:2}</big></strong></font></td>
                    {/if}
									</tr>
								
							</table>
  <table style="border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm" border="0">
	<tr valign="top">
		<td style="padding:0.1em 0.5em 0.1em 0" align="left"><br>
      {if !empty($qrCodeImage)}
        <strong>QR platební kód:</strong><br />
        <img src="{$qrCodeImage|dataStream}" title="QR Platba FIO"  width="120px" height="120px" /><br>
      {/if}
    </td>
		<td style="padding:0.1em 0.5em 0.1em 0" align="right"><br>
			<img src="/img/{$serverId}/razitko_{$vendor->usrid}.jpg" width="280" />
    </td>
	</tr>
  </table>