{* loader helperů *}

{php
  if ($order->ordcurcode === 'EURR') {
    $curCode = 'EUR';
    $decimals = 2;
    $decimalsItems = 2;
  } else {
    $curCode = 'Kč';
    $decimals = 2;
    $decimalsItems = 0;
  }
  $vatMode = 'new';
  $dateFrom = strtotime(VAT_CHANGED_FROM);
  $curDate = !empty($order->ordinvdate) ? $order->ordinvdate : $order->orddatec;
  if ($curDate->getTimestamp() < $dateFrom) {
    $vatMode = 'old';
  }
}
      <table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
	      <tr>
		      <td style="padding:0.1em 0.5em;"><img src="{php echo constant("WWW_DIR")}/img/{$serverId}/faktura-logo.png"></td>
          <td style="padding:0.1em 0.5em;"><p align="right">&nbsp;<font size="5"><b><font size="4">Výdejka: {$order->ordcode}</font></b></font></p> <barcode code="{$order->ordcode}" size="1.5" height="0.5" type="I25" /></td>
	      </tr>
      </table>
		  <table width="19cm" border="0" style="border-collapse:collapse;margin-top:10px;"  align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;" valign="top" width="50%"><p><font size="2">{_'Dodavatel'}:<br>
							{if !empty($vendor->usrifirname)}{$vendor->usrifirname}<br />{/if}
              {$vendor->usriname} {$vendor->usrilname}<br />
              {$vendor->usristreet} {$vendor->usristreetno}<br />
              {$vendor->usripostcode}, {$vendor->usricity}<br />
              <br />  
              {if !empty($vendor->usric)}{_'IČ'}:{$vendor->usric}{/if} {if !empty($vendor->usrdic)}{_'DIČ'}:{$vendor->usrdic}{/if}<br />
              Bankovní spojení:
                {if $curCode === 'EUR'}
                  <br>
                  IBAN: {$presenter->config["SERVER_ACCIBAN"]}<br>
                  SWIFT: {$presenter->config["SERVER_ACCSWIFT"]}<br>
                {else}
								{if !empty($order->ordbankacc)}
									{$order->ordbankacc}<br />
								{else}
                  {$vendor->usrbankaccount}/{$vendor->usrbankcode}<br />
								{/if}
                {/if}
              E-mail: {$presenter->config["SERVER_MAIL"]}<br />
              Tel.: {$vendor->usrtel}</font></p></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;" colspan="2" valign="top"><p><font size="2">{_'Odběratel'}: <br>
							{if !empty($order->ordifirname)}{$order->ordifirname}<br />{/if}
              {$order->ordiname} {$order->ordilname}<br />
							{$order->ordistreet} {$order->ordistreetno}<br />
							{$order->ordipostcode}, {$order->ordicity}<br />	
							{if !empty($order->ordic)}<br />{_'IČ'}: {$order->ordic}{/if} {if !empty($order->orddic)}{_'DIČ'}: {$order->orddic}{/if}<br />
							E-mail: {$order->ordmail}<br />
							Tel.: {$order->ordtel}
              <br>
              {if !empty($order->ordstname)}
              <br>Dodací adresa:<br>
              {if !empty($order->ordstfirname)}{$order->ordstfirname}, {/if}
              {$order->ordstname} {$order->ordstlname}<br />
              {$order->ordststreet} {$order->ordststreetno}, {$order->ordstpostcode}, {$order->ordstcity}
              <br>
              {/if}
              </font>
              </p>
              </td>
				</tr>
			</table>
			<table width="19cm"  style=" margin-top: 10px;border-collapse:collapse;" border="0" align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Číslo dokladu'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordcode}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob platby'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$payMode->delname}</font></td>
				</tr><tr>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob Dodání'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$delMode->delname}</font></td>

        </tr>
			</table>
							<table style="background:#fff;border-collapse:collapse;margin-top: 10px;" width="19cm" border="0" cellspacing="0" cellpadding="2" align="center">
        <tr>
          <td colspan="5"><b>{_'Objednané zboží'}:</b></td>
        </tr>
        <tr>
          <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;" align="center"><font size="3">{_'Kód'}</font></td>
          <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;" align="left"><font size="3">{_'Název'}</font></td>
          <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;" align="center"><font size="3">{_'Kusů'}</font></td>
          <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;" align="center"><font size="3">{_'Cena'}</font></td>
        </tr>

      {foreach $ordItems as $row}
        <tr>
          <td style="padding:0.1em 0.5em 0.1em 0;" align="center"><font size="3">{$row->oriprocode}</font>{if !empty($row->procode2)}<br><font size="2">EAN: {$row->procode2}</font>{/if}</td>
          <td style="padding:0.1em 0.5em 0.1em 0;" align="left"><font size="3">{$row->oriname}
              {if !empty($row->orisn)}<br />IMEI/SN: {php echo str_replace("|", ", ", $row->orisn) } {/if}</font>
              {if $row->origiftdisc > 0}<br><small>(původní cena {($row->oriprice + $row->origiftdisc)|formatPrice})</small>{/if}
          </td>
          <td style="padding:0.1em 0.5em 0.1em 0;" align="center"><font size="3">{if $row->oriqty > 1}<span style="font-weight: bold; font-size: 16px; background: #0b0b0b; color: white">&nbsp;{$row->oriqty}&nbsp;</span>{else}{$row->oriqty}{/if}</font></td>
          <td style="padding:0.1em 0.5em 0.1em 0;" align="right"><font size="3">{$row->oriprice|formatPrice}</font></td>
        </tr>
        {ifset $orderItemsBlock[$row->oriid]}
        <tr>
          <td></td>
          <td colspan="10">
          {foreach $orderItemsBlock[$row->oriid] as $brow}
            {if $iterator->isFirst()}
            <p style="font-style: italic; font-size: 13px">
            {/if}
              Sklad: {$brow->stoname} |
            {if !empty($brow->stistoidfrom)}
              přesunuto z {$enum_stoid[$brow->stistoidfrom]} |
            {/if}
            {$brow->deldate|date:'d.m.Y'} |
            {if $admin->admrole !== 'prodavac'}
              Dodavatel: {$brow->venname} |
            {/if}
            {$brow->stiqty} ks
            {if !empty($orderItemsTransInfo[$brow->stiid])}
                {foreach $orderItemsTransInfo[$brow->stiid] as $iBlock}
                  {if $iBlock->sttid > 0}
                    <br>Přesun: {$iBlock->sttdate|date:'d.m.Y'}|{$enum_stores[$iBlock->sttstoidfrom]} -> {$enum_stores[$iBlock->sttstoidto]}
                    {if !$iterator->isLast()}
                      <br>
                    {/if}
                  {/if}
                {/foreach}
            {/if}
            {if $iterator->isLast()}
            </p>
            {/if}
          {/foreach}
          {if $row->oristtids != ''}
            <strong>AUTOMATICKÝ PŘESUN</strong>
          {/if}
          </td>
        </tr>
        {/ifset}
        <tr><td style="border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;" colspan="10"></td></tr>
      {/foreach}
      </table>
							
							<table width="19cm" style="border-collapse:collapse" border="0" cellspacing="0" cellpadding="2" align="center">
								<tr>
									<td style="font-weight: bold;padding:0.1em 0.5em 0.1em 0;"  width="40%" align="left"><font size="2">Celková cena {if $order->ordnodph == 0}s DPH{else}bez DPH{/if}</font></td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="font-weight: bold;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$order->ordpricevat|formatPriceAdmin:$decimalsItems:$curCode}</font></td>
								</tr>
							</table>
              {if $vendor->usrvat == 1 && $order->ordnodph == 0}
              <br />
              <table style="width: 200mm; text-align: center; margin-left: 100mm;" class="border">
                <tr>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">Sazba</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">bez daně</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">DPH</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid #e0e0e0;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">s daní</font></td>
                </tr>
                {foreach $vatSumary as $key=>$rowi}
                {if $vatSumary[$key]['pricevat'] > 0}
                <tr>
                  <td align="center"><font size="2">{$vatSumary[$key]['vatLevel']}%</font></td>
                  <td align="right"><font size="2">{$vatSumary[$key]['price']|formatPriceAdmin:$decimals:$curCode}</font></td>
                  <td align="right"><font size="2">{$vatSumary[$key]['vat']|formatPriceAdmin:$decimals:$curCode}</font></td>
                  <td align="right"><font size="2">{$vatSumary[$key]['pricevat']|formatPriceAdmin:$decimals:$curCode}</font></td>
                </tr>
                {/if}
                {/foreach}
               </table>
              {elseif $order->ordnodph == 1}
               <p>Jedná se o dodání zboží do jiného členského státu dle par.64 odst.1) ZDPH. Daň odvede zákazník.</p> 
              {/if}
              {if $isSpecMode}<p>Prodej použitého zboží ve zvláštním režimu dle §90 zákona č. 235/2004 Sb</p>{/if}
              <table width="19cm" style="border-collapse:collapse;margin-bottom: 10px;" cellspacing="0" cellpadding="0">
								
									<tr bgcolor="#e6e6e6">
										<td style="padding:0.5em" align="left" width="50%" bgcolor="#e6e6e6"><font face="Arial" size="2">{if $order->ordnodph == 0}<strong><big>&nbsp; <b>Dodavatel je plátce DPH</b></big></strong></font>{/if}</td>
									  <td style="padding: 0.5em "  align="right" width="50%"><font face="Arial" size="2">
                    {if $order->ordpaystatus == 1}
                    <strong><big>Celkem: {$order->ordpricevat|formatPrice:2}<br>
                    Neplaťte! Již uhrazeno {if !empty($order->ordprocode)}zálohou {$order->ordprocode}{/if} {if !empty($payDate)}{$payDate|date:'d.m.Y'}{/if}</big></strong></font>
                    {else}
										<strong><big>{_'Celkem k úhradě'}:</big></strong></font> <font face="Arial" size="2"><strong><big>{$order->ordpricevat|formatPriceAdmin:$decimalsItems:$curCode}</big></strong></font>
                    {/if}
                    {if !empty($order->orddisccoupon)}<br>
                    Uplatněná sleva: {$order->orddisccoupon|formatPrice} (kód kupónu: {$order->ordcoucode})
                    {/if}
                    </td>
									</tr>
								  {if ($curCode === 'EURR' && $order->ordcurrate > 0)}
									<tr bgcolor="#e6e6e6">
										<td colspan="2" style="padding:0.5em" align="left" bgcolor="#e6e6e6"><font face="Arial" size="2">Plnění je osvobozeno od daně dle §64 o DPH č.235/2004 sb.<br>Daň odvede zákazník</td>
									</tr>
								  {/if}
							</table>
  <table style="border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm" border="0">
	<tr valign="top">
		<td style="padding:0.1em 0.5em 0.1em 0" align="right"><br>
			<img src="{php echo constant("WWW_DIR")}/img/{$serverId}/razitko_{$vendor->usrid}.jpg" width="280" />
    </td>
	</tr>
  </table>