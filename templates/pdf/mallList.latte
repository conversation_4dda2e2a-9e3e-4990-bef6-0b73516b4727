{php
$date = new \DateTime();
}
<h3>Mall doprava - soup<PERSON><PERSON></h3>
<p>
  <strong>Datum vytvoření soupisky:</strong> {$date|date:'d.m.Y H:i:s'}<br>
  <strong>Odes<PERSON>latel:</strong> <PERSON><PERSON> s.r.o., <PERSON>, 344/70, 72400 Ostrava<br>
  IČ: 27760456, DIČ: CZ27760456
</p>
<h4><PERSON>znam předaných zásilek (celkem zásilek: {count($rows)})</h4>
<table style="width: 200mm">
<tr style="background-color: #E0E0E0;" >
  <th>Č. obj.</th>
  <th>Příjemce</th>
  <th>Balíky</th>
  <th>Dob<PERSON>rka</th>
</tr>
{foreach $rows as $key => $row}
{php
if (!empty($row->ordstname)) {
  $row->ordiname = $row->ordstname;
  $row->ordilname = $row->ordstlname;
  $row->ordifirname = $row->ordstfirname;
  $row->ordistreet = $row->ordststreet;
  $row->ordistreetno = $row->ordststreetno;
  $row->ordicity = $row->ordstcity;
  $row->ordipostcode = $row->ordstpostcode;
  $row->ordicoiud = $row->ordstcouid;
}
}

<tr>
  <td>{$row->ordcode}</td>
  <td>{$row->ordiname} {$row->ordilname} {$row->ordifirname}</td>
  <td>{$row->ordparcode}</td>
  <td style="text-align: right">{if $row->delcode == 'dobirka'}{$row->ordpricevat|formatPrice}{/if}</td>
</tr>
{/foreach}
</table>
<br>
<br>
<br>
<br>
<table style="width: 200mm">
<tr>
<td width="50%" style="text-align: center;">
....................................... <br>
Předal/a
</td>
<td width="50%" style="text-align: center;">
.......................................<br>
Přijal/a
</td>

</tr>
</table>