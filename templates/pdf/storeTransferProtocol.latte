<h3><PERSON><PERSON><PERSON><PERSON><PERSON> př<PERSON>un č. {$dataRow->sttid} - {$dataRow->sttdatec|date:'d.m.Y'}</h3>
<h4>{$enum_stoid[$dataRow->sttstoidfrom]} &rArr; {$enum_stoid[$dataRow->sttstoidto]}</h4> 
<br>
<table>
<tr  style="background-color: #E0E0E0;" >
  <th>posl. datum nákupu</th>
  <th>Kód</th>
  <th>Ná<PERSON>v</th>
  <th>Množství</th>
</tr>
{foreach $items as $key => $row}
{if $iterator->isFirst()}
{/if}
<tr  style="background-color: {if $iterator->isOdd()}#C0C0C0{else}#E0E0E0{/if};" >
  <td>{ifset $row->deldate}{$row->deldate|date:'d.m.Y'}{/ifset}</td>
  <td>{$row->procode}<br>{$row->procode2}</td>
  <td>{$row->proname}</td>
  <td style="text-align: right;">{$row->ttiqty}</td>
</tr>
{if $iterator->isLast()}
{/if}
{/foreach}
</table>
<br>
<br>
<br>
<br>
<table width="100%">
<tr>
<td width="50%" style="text-align: center;">
....................................... <br>
Předal/a
</td>
<td width="50%" style="text-align: center;">
.......................................<br>
Přijal/a
</td>

</tr>
</table>
