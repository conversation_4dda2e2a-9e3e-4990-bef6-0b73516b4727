{* loader helperů *}

{php
if ($order->ordcurcode === 'EURR') {
	$curCode = 'EUR';
	$decimals = 2;
	$decimalsItems = 2;
} else {
  $curCode = 'Kč';
	$decimals = 2;
	$decimalsItems = 0;
}


$vatMode = 'new';
$dateFrom = strtotime(VAT_CHANGED_FROM);
$curDate = !empty($order->ordinvdate) ? $order->ordinvdate : $order->orddatec;
if ($curDate->getTimestamp() < $dateFrom) {
  $vatMode = 'old';
}


$isNoveZvlastniRezimActive = TRUE;
$dateFrom = strtotime('2021-02-03');
$curDate = !empty($order->ordinvdate) ? $order->ordinvdate : $order->orddatec;
if ($curDate->getTimestamp() < $dateFrom) {
  $isNoveZvlastniRezimActive = FALSE;
}

}
      <table style=" border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm"  border="0">
	      <tr>
		      <td style="padding:0.1em 0.5em;"><img src="{php echo constant("WWW_DIR")}/img/{$serverId}/faktura-logo.png"></td>
          <td style="padding:0.1em 0.5em;"><p align="right">&nbsp;<font size="5"><b><font size="4">Faktura - daňový doklad číslo: {$order->ordinvcode}</font></b></font></p></td>
	      </tr>
      </table>
		  <table width="19cm" border="0" style="border-collapse:collapse;margin-top:10px;"  align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;" valign="top" width="50%"><p><font size="2">{_'Dodavatel'}:<br>
							{if !empty($vendor?->usrifirname)}{$vendor?->usrifirname}<br />{/if}
              {$vendor?->usriname} {$vendor?->usrilname}<br />
              {$vendor?->usristreet} {$vendor?->usristreetno}<br />
              {$vendor?->usripostcode}, {$vendor?->usricity}<br />
              <br />
              {if !empty($vendor?->usric)}{_'IČ'}:{$vendor?->usric}{/if} {if !empty($vendor?->usrdic)}{_'DIČ'}:{$vendor?->usrdic}{/if}<br />
              Bankovní spojení:
                {if $curCode === 'EUR'}
                  <br>
                  IBAN: {$presenter->config["SERVER_ACCIBAN"]}<br>
                  SWIFT: {$presenter->config["SERVER_ACCSWIFT"]}<br>
                {else}
								{if !empty($order->ordbankacc)}
									{$order->ordbankacc}<br />
								{else}
                  {$vendor?->usrbankaccount}/{$vendor?->usrbankcode}<br />
								{/if}
                {/if}
              E-mail: {$presenter->config["SERVER_MAIL"]}<br />
              Tel.: {$vendor?->usrtel}<br />
              <i><small>Dodavatel je registrován pod spisovou značkou oddíl C, vložka číslo
                  51125 ze dne 13.02.2006 veden u Krajského soudu v Ostravě</small></i>
              </font></p></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;" colspan="2" valign="top"><p><font size="2">{_'Odběratel'}: <br>
							{if !empty($order->ordifirname)}{$order->ordifirname}<br />{/if}
              {$order->ordiname} {$order->ordilname}<br />
							{$order->ordistreet} {$order->ordistreetno}<br />
							{$order->ordipostcode}, {$order->ordicity}<br />
							{if !empty($order->ordic)}<br />{_'IČ'}: {$order->ordic}{/if} {if !empty($order->orddic)}{_'DIČ'}: {$order->orddic}{/if}<br />
							E-mail: {$order->ordmail}<br />
							Tel.: {$order->ordtel}
              <br>
              {if !empty($order->ordstname)}
              <br>Dodací adresa:<br>
              {if !empty($order->ordstfirname)}{$order->ordstfirname}, {/if}
              {$order->ordstname} {$order->ordstlname}<br />
              {$order->ordststreet} {$order->ordststreetno}, {$order->ordstpostcode}, {$order->ordstcity}
              <br>
              {/if}
              </font>
              </p>
              </td>
				</tr>
			</table>
			<table width="19cm"  style=" margin-top: 10px;border-collapse:collapse;" border="0" align="center">
				<tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Faktura číslo'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordinvcode}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum vytvoření'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordinvdate|date:'d.m.Y'}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Variabilní symbol'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordcode}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum splatnosti'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$dateDue|date:'d.m.Y'}</font></td>
				</tr><tr>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob platby'}:</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$payMode->delname}</font></td>
					<td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Datum zdanitelného plnění'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordinvdate|date:'d.m.Y'}</font></td>
				</tr><tr>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{_'Způsob Dodání'}:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$delMode->delname}</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">Vaše číslo objednávky:</font></td>
          <td style="border: 1px solid #c0c0c0;padding:0.1em 0.5em;"  width="25%"><font size="2">{$order->ordcodecus}</font></td>

        </tr>
			</table>
							<table style="background:#fff;border-collapse:collapse;margin-top: 10px;" width="19cm" border="0" cellspacing="0" cellpadding="2" align="center">
								<tr>
                  <td colspan="5"><b>{_'Objednané zboží'}:</b></td>
                </tr>
                <tr>
                  <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{_'Kód'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="40%" align="left"><font size="2">{_'Název'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{_'Kusů'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{$curCode} bez DPH/ {_'kus'}</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">Sazba</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{$curCode} s DPH/ {_'kus'}</font></td>
									<td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$curCode} {if $order->ordnodph == 0}s DPH{else}bez DPH{/if}</font></td>
								</tr>
								{php
                $sum = 0;
                $sumNoVat = 0;
                $isNoveZvlastniRezim = FALSE;
                $isSpecModeUsed = FALSE;
                }
								{foreach $ordItems as $row}
								{php
                if ($order->ordnodph == 1 || $curCode === 'EURR') {
                  $row->oriprice = $row->oripricenovat;
                }

                $sumNoVat += ($row->oripricenovat*$row->oriqty);
                $sum += ($row->oriprice*$row->oriqty);


                if (isset($row->prospecmode) && $row->prospecmode==1 && $row->proused==1 && $row->orivatid==3) {
                  $isNoveZvlastniRezim = TRUE;
                }

                if (isset($row->prospecmode) && $row->prospecmode==1 && $row->proused!=1) {
                  $isSpecModeUsed = TRUE;
                }

                }
								<tr>
                  <td style="border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{$row->oriprocode}</font></td>
									<td style="border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="40%" align="left"><font size="2">{$row->oriname}
                      {if !empty($row->orisn)}<br />IMEI/SN: {php echo str_replace("|", ", ", $row->orisn) } {/if}</font>
                      {if $row->origiftdisc > 0}<br><small>(původní cena {($row->oriprice + $row->origiftdisc)|formatPrice})</small>{/if}
                      {if isset($row->orirecfee) && $row->orirecfee > 0}<br><small><i>recyklační příspěvek {$row->orirecfee|formatPrice:2}/ks</i></small>{/if}
                  </td>
									<td style="border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">{$row->oriqty}</font></td>
									<td style="border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$row->oripricenovat|formatPriceAdmin:$decimals:$curCode}</font></td>
									<td style="border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$row->orivat}%</font></td>
									<td style="border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$row->oriprice|formatPriceAdmin:$decimalsItems:$curCode}</font></td>
									<td style="border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$row->oriprice*$row->oriqty|formatPriceAdmin:$decimalsItems:$curCode}</font></td>
								</tr>
								{/foreach}
                {php
                if ($order->ordnodph == 1) {
                  $order->ordpricevat = $sum;
                } else if ($curCode === 'EURR' && $order->ordcurrate > 0) {
									$order->ordpricevat = $sum;
								}
                }
							</table>

							<table width="19cm" style="border-collapse:collapse" border="0" cellspacing="0" cellpadding="2" align="center">
								<tr>
									<td style="font-weight: bold;padding:0.1em 0.5em 0.1em 0;"  width="40%" align="left"><font size="2">Celková cena {if $order->ordnodph == 0}s DPH{else}bez DPH{/if}</font></td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="padding:0.1em 0.5em 0.1em 0;"  width="15%">&nbsp;</td>
									<td style="font-weight: bold;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="right"><font size="2">{$order->ordpricevat|formatPriceAdmin:$decimalsItems:$curCode}</font></td>
								</tr>
							</table>
              {if $vendor?->usrvat == 1 && $order->ordnodph == 0}
              <br />
              <table style="width: 200mm; text-align: center; margin-left: 100mm;" class="border">
                <tr>
                  <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">Sazba</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">bez daně</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">DPH</font></td>
                  <td style="font-weight: bold;border-bottom: 1px solid black;padding:0.1em 0.5em 0.1em 0;"  width="15%" align="center"><font size="2">s daní</font></td>
                </tr>
                {foreach $vatSumary as $key=>$rowi}
                {if $vatSumary[$key]['pricevat'] > 0}
                <tr>
                  <td align="center"><font size="2">{$vatSumary[$key]['vatLevel']}%</font></td>
                  <td align="right"><font size="2">{$vatSumary[$key]['price']|formatPriceAdmin:$decimals:$curCode}</font></td>
                  <td align="right"><font size="2">{$vatSumary[$key]['vat']|formatPriceAdmin:$decimals:$curCode}</font></td>
                  <td align="right"><font size="2">{$vatSumary[$key]['pricevat']|formatPriceAdmin:$decimals:$curCode}</font></td>
                </tr>
                {/if}
                {/foreach}
               </table>
              {elseif $order->ordnodph == 1}
               <p>Jedná se o dodání zboží do jiného členského státu dle par.64 odst.1) ZDPH. Daň odvede zákazník.</p>
              {/if}
              {if $isNoveZvlastniRezim > 0}
                <p>Zboží uvedené v sazbě 0% dle rozhodnutí MF – daň prominuta.</p>
              {/if}
              {if $isSpecModeUsed}
                <p>Prodej použitého zboží ve zvláštním režimu dle §90 zákona č. 235/2004 Sb</p>
              {/if}
            {if $order->ordnoteint}
                <p ><font size="2">{$order->ordnoteint}</font></p>
            {/if}
              <table width="19cm" style="border-collapse:collapse;margin-bottom: 10px;" cellspacing="0" cellpadding="0">

									<tr bgcolor="#e6e6e6">
										<td style="padding:0.5em" align="left" width="50%" bgcolor="#e6e6e6"><font face="Arial" size="2">{if $order->ordnodph == 0}<strong><big>&nbsp; <b>Dodavatel je plátce DPH</b></big></strong></font>{/if}</td>
									  <td style="padding: 0.5em "  align="right" width="50%">
                    {if $order->ordpaystatus == 1}
                    <font face="Arial" size="2">
                    <strong>
                      <big>Celkem: {$order->ordpricevat|formatPrice:2}<br>
                      Neplaťte! Již uhrazeno {if !empty($order->ordprocode)}zálohou {$order->ordprocode}{/if} {if !empty($payDate)}{$payDate|date:'d.m.Y'}{/if}
                      </big>
                    </strong>
                    </font>
                    {else}
                    <font face="Arial" size="2">
										<strong><big>{_'Celkem k úhradě'}:</big></strong></font>
                    <font face="Arial" size="2"><strong><big>{$order->ordpricevat|formatPriceAdmin:$decimalsItems:$curCode}</big></strong></font>
                    {/if}
                    {if $payMode->delcode == 'cash'}<br><font face="Arial" size="2"><strong><big>V hotovosti přijato od zákazníka.</big></strong></font>{/if}
                    {if !empty($order->orddisccoupon)}<br>
                    Uplatněná sleva: {$order->orddisccoupon|formatPrice} (kód kupónu: {$order->ordcoucode})
                    {/if}
                    </td>
									</tr>
								  {if ($curCode === 'EURR' && $order->ordcurrate > 0)}
									<tr bgcolor="#e6e6e6">
										<td colspan="2" style="padding:0.5em" align="left" bgcolor="#e6e6e6"><font face="Arial" size="2">Plnění je osvobozeno od daně dle §64 o DPH č.235/2004 sb.<br>Daň odvede zákazník</td>
									</tr>

								  {/if}
                  {if ($payMode->delcode == 'cetelem' || $payMode->delcode == 'cofidis') && $order->ordfirstpay > 0}
                  <tr>
                    <td style="padding:0.5em" bgcolor="#e6e6e6"></td><td style="padding:0.5em" align="right" bgcolor="#e6e6e6"><font face="Arial" size="2"> Přímá platba při převzetí {$order->ordfirstpay|formatPrice} </td>
                  </tr>
                  {/if}
							</table>
  <table style="border-collapse:collapse;font-size:11pt"  cellspacing=0 cellpadding=0 width="19cm" border="0">
    {if !empty($eet)}
			<tr valign="top">
				<td style="padding:0.1em 0.5em 0.1em 0" align="left"><br>
					<font face="Arial" size="1">
						<strong>Tržba evidovaná v běžném režimu</strong><br>
						<strong>Datum: </strong>{$eet->logdatec|date:'d.m.Y H:i:s'}<br>
						<strong>FIK: </strong> {$eet->logfik}<br>
						<strong>BKP: </strong> {$eet->logbkp}<br>
						<strong>Id provozovny: </strong> {$eet->logprovozid}<br>
						<strong>Id pokladny: </strong> {$eet->logpoklid}<br>
						<strong>Poř. číslo: </strong> {$eet->logid}<br>
					</font>
				</td>
			</tr>
    {/if}
	<tr valign="top">
    <td style="padding:0.1em 0.5em 0.1em 0" align="left"><br>
      {if !empty($qrCodeImage)}
        <strong>QR platební kód:</strong><br />
        <img src="{$qrCodeImage|dataStream}" title="QR Platba FIO"  width="120px" height="120px" /><br>
      {/if}
    </td>
		<td style="padding:0.1em 0.5em 0.1em 0" align="right"><br>
			<img src="{php echo constant("WWW_DIR")}/img/{$serverId}/razitko_{$vendor?->usrid}.jpg" width="280" />
    </td>
	</tr>
  </table>
  {if !empty($blockOrderFooter)}
    {$blockOrderFooter->pagbody|noescape}
  {/if}