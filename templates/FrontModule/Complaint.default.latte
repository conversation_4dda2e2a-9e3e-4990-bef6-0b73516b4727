{default $pageTitle       = $presenter->translator->translate('Podání reklamace')}
{default $pageRobots      = "nofollow,noindex"}

{block #content}
  <h1><span>{_'Podání reklamace'}</span></h1>
  <div id="order1">
  <div class="userform complaint">
  {form itemSearchForm}
  <ul class="error" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$presenter->translator->translate($error)}</li>
  </ul>
<fieldset><legend>Vyhledání nákupu na našem eshopu</legend>
  <p>Vyplňte jeden z údajů abychom mohli ověřit Váš nákup na našem eshopu a na základě toho Vám potřebné údaje pro reklamaci předplníme.</p>
  <table>
      <tr>
        <td>{php echo $form['copsn']->getLabel() }:<br />
        {php echo $form['copsn']->control->size(40) }</td>
      </tr>
      <tr>
        <td>{php echo $form['ordcode']->getLabel() }:<br />
        {php echo $form['ordcode']->control->size(40) } </td>
      </tr>
      <tr>
        <td>{php echo $form['ordinvcode']->getLabel() }:<br />
        {php echo $form['ordinvcode']->control->size(40) } </td>
      </tr>
      <tr>
        <td>{php echo $form['antispam']->getLabel() }:
{php echo $form['antispam']->control->cols(15) } <small>  {_'test proti robotum'}</small></td>
      </tr>
  </table>
  </fieldset>

  <div class="submit"><p>{php echo $form['save']->getControl() }</p></div>

  {/form}

    <p>Zadat PLACENOU opravu můžete <a href="{plink editRepair 'mod' => 2}">ZDE</a>.</p>

  </div></div>
  <script>
  $('#antispam').val({$presenter->config["ANTISPAM_NO"]}).closest('tr').hide();
  </script>
{/block}