{varType string[] $availabilityRank}
{varType string[] $enumProaccess}
{varType App\FrontModule\Presenters\LuigisboxPresenter $presenter}
{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<items>
  {var $lastId=0}
  {while}
        {var $fetched =$presenter->orm->product->getAllActiveProducts($presenter->web,1000,$lastId)}
        {foreach $fetched as $product}
    {var
      string $catPath = str_replace('|', ' | ', $product->getCatalog()->catpath);
    }
    <item>
      <title>{$product->name}</title>
      <url>{plink //:Front:Product:detail, $product->id, $product->getUrlKey()}</url>
      <availability>1</availability>
      <availability_rank>{$availabilityRank[$product->getAccess()]}</availability_rank>
      <availability_rank_text>{$product->getAccessText($presenter->web,$enumProaccess)}</availability_rank_text>
      <category primary="true">{$catPath}</category>
      <brand>{$product->manufacturer->name}</brand>
      <price>{$product->getPrice(true,$presenter->web)}</price>
      {if $product->getPrice(true,$presenter->web,App\Orm\Product::PRICE_COM)  > $product->getPrice(true,$presenter->web)}<price_old>{$product->getPrice(true,$presenter->web,App\Orm\Product::PRICE_COM)}</price_old>{/if}
      <image_link_s>{$baseUrl}{$product->getImage()->getPathList()}</image_link_s>
      <image_link_m>{$baseUrl}{$product->getImage()->getPathDetail()}</image_link_m>
      <image_link_l>{$baseUrl}{$product->getImage()->getPathBig()}</image_link_l>
      <description>{$product->description??''}</description>
      <labels>{$product->getTagsList($delFreeLimitByVendor,$catalogGifts,$discGifts,$presenter->web)}</labels>
      <product_code>{$product->code}</product_code>
      {if strlen($product->code2) == 13}<ean>{$product->code2}</ean>{/if}
      <to_cart_id>{$product->id}</to_cart_id>
      <parameters>
        {foreach $product->getLuigiParams() as $paramProduct}
          <param>
          <name>{$paramProduct->param->name}</name>
          <value>{$paramProduct->value}</value>
        </param>
        {/foreach}
      </parameters>
      <introduced_at>{$product->datec|date:'Y-m-d'}</introduced_at>
    </item>
    {var $lastId=$product->id}
    {php unset($product)}
    {*breakIf true*}
  {/foreach}
    {*breakIf true*}
  {php $presenter->orm->product->doClear()}
  {/while !empty($fetched)}


</items>