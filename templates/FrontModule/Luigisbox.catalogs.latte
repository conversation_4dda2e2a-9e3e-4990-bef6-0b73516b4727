{varType App\Orm\Catalog[] $catalogs}
{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<categories>
  {foreach $catalogs as $catalog}
    {php
      $catPath = str_replace('|', ' | ', $catalog->catpath);
    }
    <category>
      <name>{$catalog->catname}</name>
      <url>{plink //:Front:Catalog:detail, $catalog->id, $catalog->getURLKey()}</url>
      <hierarchy>{$catPath}</hierarchy>
      <image_link n:if="$catalog->getImagSrc()">{$baseUrl}{$catalog->getImagSrc()}</image_link>
      <description>{$catalog->catdesc}</description>
    </category>
  {/foreach}
</categories>