{varType App\Orm\Delivery[] $deliveries}
{varType App\Orm\Basket $basketOrm}
{default $pageTitle=$presenter->translator->translate('Výběr dopravy a platby')}
{default $pageRobots      = "nofollow,noindex"}

{block #ogHead}
  <script type="text/javascript" src="https://www.ppl.cz/sources/map/main.js" async></script>
  <link rel="stylesheet" href="https://www.ppl.cz/sources/map/main.css">
  <link rel="stylesheet" href="{$baseUrl}/style/pplcustom.css">
{/block}

{block content}

    <script type="text/javascript">

    </script>

    {include 'basketProgress.latte'}

    <section class="section section--cart">

        <article class="container container--md" role="article">

            <h1 class="cart-headline">Doprava a platba pro {if $couId === 2}Slovensko{elseif $couId === 1}Českou republiku{/if}</h1>

            <p class="order-language">
                Změnit cílovou zemi:
                <img src="{$baseUrl}/img/cz.jpg" alt="Česká republika"> {if $couId === 2}<a href="{plink this ci=>1}"> Česká republika</a>{else}<strong>Česká republika</strong>{/if}
                    |
                <img src="{$baseUrl}/img/sk.jpg" alt="Slovensko"> {if $couId === 1}<a href="{plink this ci=>2}"> Slovensko</a>{else}<strong>Slovensko</strong>{/if}
            </p>
            {if $couId === 2}<p style="font-weight: bold; color: red">upozorňujeme, že u doprav do zahraničí nemusí platit "Doprava zdarma"</p>{/if}
            {form ordDelModeForm}
                {include 'formError.latte', form: $form}

                <div class="row">
                    <div class="col col--2">
                        <h3 class="headline-stripe">Vyberte způsob dopravy</h3>
                        {foreach $deliveries as $delivery}
                            <div class="form-radio cart-radio">
                                <input type="radio" name="radio-choice-shipping" data-delid="{$delivery->id}" id="chkdelid_{$delivery->id}" value="{$delivery->id}" class="delModeCheckBox{if $delivery->delcode == 'ZASILKOVNA' } packeta-selector-open{/if}">
                                <span class="form-radio__box"></span>
                                <label class="form-label" for="chkdelid_{$delivery->id}"><img src="{$baseUrl}/{$delivery|getDelLogo}" alt="{$delivery->delname}"> {$delivery->delname}
                                    <strong>
                                        {if $delivery->delspecdel===1}
                                            Individuální nacenění
                                        {elseif $delivery->isDeliveryFree($basketOrm)}
                                          Zdarma
                                        {else}
                                        od {$delivery->delprice|formatPrice} {$delivery->delprice|formatPriceEur:$config["EUR_RATE"]|noescape}
                                        {/if}
                                    </strong>
                                </label>
                                <div class="cart-radio__content">
                                    {if !empty($delivery->deldesc) || !empty($delivery->delurlmap) || !empty($delivery->delsrcphoto)}
                                        <p>
                                          {if !empty($delivery->deldesc)}{$delivery->deldesc|breakLines}{/if}
                                          {if !empty($delivery->delurlmap)}<a class="map" href="{$delivery->delurlmap}" target="_blank">Orientační mapa</a>{/if}
                                          {if !empty($delivery->delsrcphoto)}<br><img src="{$delivery->delsrcphoto}" alt="{$delivery->delname}" />{/if}
                                        </p>
                                    {/if}

              {if $delivery->delcode == 'ULOZENKA'}
                                        {$form['orddelspec']->control}<br />
                                    {/if}
              {if $delivery->delcode == 'CESKA_POSTA_BALIKOMAT'}
                                        {$form['orddelspecbalikomat']->control}<br />
                                    {/if}
              {if $delivery->delcode == 'CESKA_POSTA_NA_POSTU'}
                                        <strong>{$form['ordpostcode_napostuName']->label->class('required')}</strong> {$form['ordpostcode_napostuName']->control} <span id="cpname"></span></strong> <br>
                                        <a href="#modal-info" class="js-modal-open openTypepostOffice" >Vybrat</a>
                                    {/if}
              {if $delivery->delcode == 'CESKA_POSTA_BALIKOVNA'}
                                        <strong>{$form['orddelspecbalikovnaName']->label->class('required')}</strong> {$form['orddelspecbalikovnaName']->control} <span id="balname">{if !empty($balName)}{$balName}{/if}</span></strong> <br>
                                        <a href="#modal-info" class="js-modal-open openTypeBalikovna" >Vybrat</a>
                                    {/if}
              {if $delivery->delcode == 'ZASILKOVNA'}
                                        {$form['orddelspeczasilkovna_name']->control} <br> <a href="#" class="packeta-selector-open"> vybrat </a> <br />
                                    {/if}
              {if $delivery->delcode == 'PPL_PARCELSHOP'}
                                        {$form['orddelspecppl_name']->control} <br> <a href="#modal-ppl" class="js-modal-open" id="modal-button-ppl">Vybrat</a> <br />
                                    {/if}
              {if $delivery->id == 1}

                                        {$form['orddelspecintime']->control}<br />
                                    {/if}
              {if $delivery->id == 43}
                                        {var $link = $presenter->link('//this')}
                                        <p>ID odběrného místa: {$form['orddelspecgeis']->control->size(5)} <a href="https://plugin.geispoint.cz/map.php?CustAddress=&ReturnURL={urlencode($link)}#delid_43" class="choose_geis">vybrat odběrné místo</a></p>
                                    {/if}

                                </div>
                            </div>

                        {/foreach}
                        {if count($deliveries) === 0}
                            {include 'alert.latte', type:'info', text:'Nutné nacenit individuálně, kontaktujte proto obchodní oddělení: <a href="mailto:' . $config["SERVER_MAIL"] . '">' . $config["SERVER_MAIL"] . '</a>, děkujeme.'}
                        {/if}
                    </div>

                    <div class="col col--2" >
                        <div class="payment" style="display: none;">
                        <h3 class="headline-stripe">Jak budete platit?</h3>
                            {foreach $deliveries as $delivery}
                                {foreach $delivery->payments as $payment}
                                {if $payment->delcode == 'creditcard' && $delivery->delcode != 'OSOBNE' && !$basketOrm->payMethodEnable()}
                                  {* platby kartou nezobrazovat, pokud není dosaženo požadované marže *}
                                {else}
                                <div class="form-radio cart-radio payments payForDelivery{$delivery->id}">
                                    {$form['orddelid']->getControlPart($payment->id)}
                                    <span class="form-radio__box"></span>
                                    <label class="form-label" n:name="orddelid:$payment->id"><img src="{$baseUrl}/img/payment/{$payment->delcode}.jpg" alt="{$payment->delname}"> {$form['orddelid']->getLabelPart($payment->id)->getHtml()}
                                      <strong>
                                      	{if $payment->delprice == 0 || (($delivery->delcode == 'CESKA_POSTA_BALIKOVNA' || $delivery->delcode == 'CESKA_POSTA_NA_POSTU' || $delivery->delcode == 'CESKA_POSTA') && $payment->delcode == 'dobirka' && $delivery->isDeliveryFree($basketOrm))}
                                        {*if $payment->delprice == 0*}
                                            Zdarma
                                        {else}
                                        {$payment->delprice|formatPrice} {$payment->delprice|formatPriceEur:$config["EUR_RATE"]|noescape}
                                        {/if}
                                    </strong>
                                    </label>
                                    <div class="cart-radio__content" n:if="$payment->deldesc&&$payment->deldesc!=''">{$payment->deldesc|noescape}</div>
                                </div>
                                {/if}
                            {/foreach}
                        {/foreach}
                        </div>
                    </div>
                </div>

                <div class="cart-sumprice">

                    <h2>Celková cena za zboží</h2>

                    <dl>
                        {*
                        <dt>Cena bez DPH:</dt>
                        <dd class="cart-sumprice__vatless">{$basket->priceSumTotal|formatPrice}</dd>
                        *}
                        <dt>Celková cena s DPH:</dt>
                        <dd class="cart-sumprice__vatinclude">{$basketOrm->sumPrice()|formatPrice}</dd>
                    </dl>

                </div>

                <nav class="cart-pagination" role="navigation" aria-label="Nákupní košík">
                    <p><a href="{plink default}" class="btn btn--txt"><i class="fas fa-caret-left"></i>Zpět do košíku</a></>
                    <p>
                        <button n:name="submit" class="btn" >Pokračovat<i class="fas fa-caret-right"></i></button>
                    </p>
                </nav>

            {/form}
        </article>

    </section>



        <div class="modal modal--info" id="modal-info" role="dialog" aria-labelledby="dialog-title-1" >

            <div class="modal__body" style="border: none; width: 100%; height: 100%; max-width: 1000px;padding: 65px 0px 0px 0px">

            <span class="modal__close js-modal-close" id="modalClose">
              <span class="icon icon--times-solid">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="/img/icons.svg#times-solid" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </span>
            <iframe title="Výběr místa pro vyzvednutí zásilky" style="border: none; width: 100%; height: 99%;" id="iframeCpost" src="https://b2c.cpost.cz/locations/?type=BALIKOVNY&disableBoxes=true" allow="geolocation"/>
            </div>

        </div>

        <div class="modal modal--wide" id="modal-ppl" role="dialog" aria-labelledby="dialog-title-6">

          <div class="modal__body" style="border: none; width: 100%; height: 100%; max-width: 1000px;padding: 65px 0px 0px 0px">

            <span class="modal__close js-modal-close">
              <span class="icon icon--times-solid">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="/img/icons.svg#times-solid" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </span>

              <div class="modal-box" >
                <div id="ppl-parcelshop-map" data-countries="{if $couId == 2}sk{else}cz{/if}" data-country="{if $couId == 2}sk{else}cz{/if}"></div>
              </div>

          </div>

        </div>

    <script>
      var modalBox = document.querySelector(".modal-box");
      var modalButton = document.querySelector("#modal-button-ppl");
      modalBox.style.display = "block";
      modalButton.addEventListener("click", function () {
        // Create a link element to load the main.css file
        var link = document.createElement("link");
        link.rel = "stylesheet";
        link.href = "https://www.ppl.cz/sources/map/main.css";

        // Create a script element to load the main.js file
        var script = document.createElement("script");
        script.src = "https://www.ppl.cz/sources/map/main.js";

        // Add the script+href link to the document head
        document.head.appendChild(link);
        document.head.appendChild(script);

      });

      var pplName = document.querySelector("#orddelspecppl_name");
      pplName.addEventListener("click", function () {
        modalButton.click();
      });

      document.addEventListener("ppl-parcelshop-map", (event) =>
        {
          // Zde je možné získat data výdejního místa skrze atribut event.detail
          $('.modal').removeClass('is-open');
          $('body').removeClass('is-unscrollable');
          $('#orddelspecppl_name').val(event.detail.name);
          $('#orddelspecppl').val(event.detail.code);
        }
      );
    </script>


    <script>
        const packetaApiKey = 'd89c2ca905f729a7';
        const packetaCountry = {$couId == 2 ? 'sk' : 'cz'};
        const packetaOptions = {
            language: "cs",
            valueFormat: "\"Packeta\",id,carrierId,carrierPickupPointId,name,city,street",
            view: "modal"
        };

        const openButton = document.querySelector('.packeta-selector-open');

        if (openButton) {
            openButton.addEventListener('click', function() {
                Packeta.Widget.pick(packetaApiKey, function(point) {
                    $('.packeta-selector-branch-name').val(point.name);
                    $('.packeta-selector-branch-id').val(point.id);
                }, {
                    country: packetaCountry,
                    packetaOptions: packetaOptions
                });
            });
        }
    </script>

    <script src="https://widget.packeta.com/v6/www/js/library.js"></script>
{/block}