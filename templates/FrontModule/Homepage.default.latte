{varType App\Orm\Catalog[] $catalogFavourites}
{default $pageTitle       = $presenter->config["INDEX_TITLE"]}
{default $pageDescription = $presenter->config["INDEX_DESC"]}
{default $pageKeywords = $presenter->config["INDEX_KEYWORDS"]}
{varType App\Orm\Product[] $homepageTopSaleProducts}

{block #content}

    <div class="slider slider--front">
        <div class="container">
            <div class="slider__slick">

                {foreach $menuIndexs as $row}
                    {if !empty($row->meiurl)}
                        <a href={$row->meiurl} {if $row->meiurlinnewtab == 1} target="_blank"{/if}><img src="{$baseUrl}/pic/{$serverId}/menuindex/{$row->meiid}.jpg" width="1120" height="370" alt="slider"></a>
                    {/if}
                {/foreach}
            </div>
        </div>
    </div>

    <section class="section">

        <article class="container" role="article">

            {foreach $catalogFavourites as $oneCatalog}
                {if $iterator->first}
                    <div class="favourites favourites--top">
                    <div class="favourites-slick">
                {/if}
                <a n:href="Catalog:detail, $oneCatalog->id, $oneCatalog->getURLKey()">
                    <strong>{$oneCatalog->catname}</strong>
                    <img n:if="$oneCatalog->getImagSrc()" src="{$baseUrl}/{$oneCatalog->getImagSrc()}" alt="{$oneCatalog->catname}">

                </a>
                {if $iterator->last}
                    </div>
                    </div>
                {/if}
            {/foreach}
            {if !empty($textBlocks["homepageinfo_red"]->pagbody)}
            <div class="alert alert--danger" style="margin-top:-{=$serverId=='web2' ? 10 : 25}px;margin-bottom: 15px">{$textBlocks["homepageinfo_red"]->pagbody|noescape}</div>
            {/if}
            {if !empty($textBlocks["homepageinfo_blue"]->pagbody)}
            <div class="alert alert--info" style="{if empty($textBlocks["homepageinfo_red"]->pagbody)}margin-top:-{=$serverId=='web2' ? 10 : 25}px;{/if}margin-bottom: 15px;color: #055160; background-color: #cff4fc; border-color: #b6effb;">
              {$textBlocks["homepageinfo_blue"]->pagbody|noescape}
            </div>
            {/if}
      <div class="product-list product-list--slider">
                    <h2 class="title center">Dnes pro vás vybíráme</h2>

                    <div class="row row--start product-slick-autoplay" >
                        {foreach $homepageTopSaleProducts as $product}
                            {*continueIf !$product->canBuy()*}

                            <div class="col col--5">
                                {include 'productItemOrm.latte', product:$product}
                            </div>

                        {/foreach}

                    </div>
      </div>


      {include 'banners.latte'}

      {include 'actions.latte'}

      {include 'favourites.latte', catalogs:$catalogFavourites}


        </article>

    </section>

{/block}
