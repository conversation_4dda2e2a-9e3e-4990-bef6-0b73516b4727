{varType App\Orm\Product $product}
{varType float $delFreeLimitByVendor}
<ul class="tag-list">
{if $product->getDiscountValue() > 0}<li><span class="tag tag--coupon">Kupón/sleva</span></li>{/if}
{if $product->isDelFree($delFreeLimitByVendor)}<li><span class="tag tag--delfree">Doprava zdarma</span></li>{/if}
{if $product->haveGift($catalogGifts)}
  <li>
    <span class="tag tag--gift">+ Dárek
      {if !empty($proGiftsList) && is_array($proGiftsList)}
        <span class="tooltip">
        <i class="fas fa-info-circle js-tooltip-open" aria-describedby="tooltip-N"></i>
        <span class="tooltip__box" style="width: fit-content;">
          <span class="tooltip__content" role="tooltip">
            <div style="display: flex; flex-direction: row">
            <div class="product" n:foreach="$product->getGifts() as $gift" style=" width: {=round(100/count($proGiftsList))}%">
              <div class="product__top" >
                <div class="product__name"  >
                  <a n:href="Product:detail, $gift->id, $gift->getUrlKey()">{$gift->name}</a>
                </div>
              </div>
              <a  n:href="Product:detail, $gift->id, $gift->getUrlKey()" class="product__image" style="min-height:inherit">
                <img src="{$baseUrl}{$gift->getImage()->getPathDetail()}"  alt="{$gift->name}">
              </a>
            </div>
              </div>
            <span class="tooltip__close js-tooltip-close">
              <span class="icon icon--times-solid">
                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                  <use xlink:href="{$baseUrl}/img/icons.svg#times-solid" x="0" y="0" width="100%" height="100%"></use>
                </svg>
              </span>
            </span>
          </span>
        </span>
      {/if}
    </span>

  </li>
{/if}
  {if isset($product->prodatedisc) && $product->prodatedisc !== null && $product->prodatedisc >= date('Y-m-d')}<li><span class="tag tag--sale">Akce do {$product->prodatedisc|date:'d.m.Y'}</span></li>{/if}
  {if $product->protypid2}<li><span class="tag tag--new">Novinka</span></li>{/if}
  {*if $product->protypid3}<li><span class="tag tag--action">Tip</span></li>{/if*}
  {*if $product->protypid4}<li><span class="tag tag--topsale">Nejprodávanější</span></li>{/if*}
  {if $product->protypid5}<li><span class="tag tag--sale">Výprodej</span></li>{/if}
  {if $product->protypid6}<li><span class="tag">Proplácí pojišťovna</span></li>{/if}
{if isset($product->proorigin)}
  {if $product->proorigin == 'CZ'}
    <li>
      <span class="tag tag--cz">CZ DISTRIBUCE
      {if !empty($textBlocks["distribuce_i_" . $product->proorigin]->pagbody)}
        {include 'toolTip.latte', tTitle:$textBlocks["distribuce_i_" . $product->proorigin]->pagtitle, tText:$textBlocks["distribuce_i_" . $product->proorigin]->pagbody}
      {/if}
      </span>
    </li>
    {/if}
  {if $product->proorigin == 'EU'}
    <li>
      <span class="tag tag--cz">EU DISTRIBUCE
      {if !empty($textBlocks["distribuce_i_" . $product->proorigin]->pagbody)}
        {include 'toolTip.latte', tTitle:$textBlocks["distribuce_i_" . $product->proorigin]->pagtitle, tText:$textBlocks["distribuce_i_" . $product->proorigin]->pagbody}
      {/if}
      </span>
    </li>
  {/if}
  {if $product->proorigin == 'mimo EU'}
    <li>
      <span class="tag tag--cz">DISTRIBUCE MIMO EU
      {if !empty($textBlocks["distribuce_i_" . $product->proorigin]->pagbody)}
        {include 'toolTip.latte', tTitle:$textBlocks["distribuce_i_" . $product->proorigin]->pagtitle, tText:$textBlocks["distribuce_i_" . $product->proorigin]->pagbody}
      {/if}
      </span>
    </li>
  {/if}
  {if $product->proorigin == 'oficiál'}
    <li>
      <span class="tag tag--cz">OFICIÁLNÍ DISTRIBUCE
      {if !empty($textBlocks["distribuce_i_" . $product->proorigin]->pagbody)}
        {include 'toolTip.latte', tTitle:$textBlocks["distribuce_i_" . $product->proorigin]->pagtitle, tText:$textBlocks["distribuce_i_" . $product->proorigin]->pagbody}
      {/if}
      </span>
    </li>
  {/if}
{/if}
</ul>