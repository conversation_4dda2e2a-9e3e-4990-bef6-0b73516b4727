{contentType 'application/json'}

{foreach $productsData as $row}
  {if $iterator->isFirst()}
{"rows":[
  {/if}
  {
  "proId" : "{$row->proid}" ,
  "picSrc" : "{$baseUrl}/{getPicName($row, 'product/list')}",
  "detailUrl" : "{plink Product:detail, $row->proid, getProKey($row)}" ,
  "proPrice" : "{$row->proprice|formatPrice}" ,
  "proName" : {php echo json_encode($row->proname) }
  }
  {if $iterator->isLast()}
]}
  {else}
  ,
  {/if}
{/foreach}