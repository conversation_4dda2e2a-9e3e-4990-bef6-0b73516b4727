{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>
{default $utmSource = 'heurekacz'}
{php
$heurekaPath = [];
}
{foreach $rows as $row}
{php
  $catPath = str_replace('|', ' | ', $row->catpath);

  if ($utmSource === 'srovnamecz') {
    $key = $row->proid . "-" . substr(md5($row->proid . $row->prodatec), 0, 6);
  }

  $heurekaPath = ($row|getCatalogPath:$heurekaPath:$heurekaCatalogs);

  $catName = $row->catname;
  if (isset($heurekaPath[$row->catid])) {
    $catName = $heurekaPath[$row->catid];
  }

  $proname = ($row|getFeedProName:$catName);

  $giftsByType = ($row|getFeedGiftsByType:$gifts:$catalogGifts:$defGiftsNames:FALSE);
}

{php
  $price = $row->propricea;
  if($preferPriceE && $row->propricee > 0) {
    $price = $row->propricee;
  }
}

<SHOPITEM>
<ITEM_ID>{$row->procode}</ITEM_ID>
<MANUFACTURER>{$row->manname}</MANUFACTURER>
{if strlen($row->procode2) == 13}<EAN>{$row->procode2}</EAN>{/if}
<PRODUCTNAME>{$proname}</PRODUCTNAME>
<PRODUCT>{$proname}{if !empty($row->pronameadd)} {$row->pronameadd}{/if}</PRODUCT>
<DESCRIPTION>{$proname} {$row->prodescs}</DESCRIPTION>
{if !empty($key)}
<URL>{plink //:Front:Product:detail, $row->proid, getProKey($row), "g"=>$key}?utm_source={$utmSource}</URL>
{else}
<URL>{plink //:Front:Product:detail, $row->proid, getProKey($row)}?utm_source={$utmSource}</URL>
{/if}
<DELIVERY_DATE>{$row|getProAccess}</DELIVERY_DATE>
<IMGURL>{$baseUrl}/{getPicName($row, 'product/detail')}</IMGURL>
{if !empty($row->procodegro)}<ITEMGROUP_ID>{$row->procodegro|webalize|substr:0:36}</ITEMGROUP_ID>{/if}
{foreach $giftsByType as $gType => $items}
  {foreach $items as $item}
  {if $gType == 'GIFT'}
  <{$gType}>{$item["name"]}</{$gType}>
  {elseif $gType == 'EXTENDED_WARRANTY'}
  <{$gType}>
    {if !empty($item["warranty"])}<VAL>{$item["warranty"]}</VAL>{/if}
    <DESC>{$item["name"]}</DESC>
  </{$gType}>
  {elseif $gType == 'SALES_VOUCHER'}
    {*php
      $row->propricea = ($row->propricea|calculatePriceFromVoucher:$item);
    *}
  <{$gType}>
    {if !empty($item["code"])}<CODE>{$item["code"]}</CODE>{/if}
    <DESC>{$item["name"]}</DESC>
  </{$gType}>
  {else}
  <{$gType}>{$item["name"]}</{$gType}>
  {/if}
  {/foreach}
{/foreach}
<PRICE_VAT>{$price}</PRICE_VAT>
{if isset($heurekaPath[$row->catid])}
<CATEGORYTEXT>{$heurekaPath[$row->catid]}</CATEGORYTEXT>
{else}
<CATEGORYTEXT>{$catPath}</CATEGORYTEXT>
{/if}

{if !empty($row->proorigin)}
<PARAM>
  <PARAM_NAME>Distribuce</PARAM_NAME>
  <VAL>{$row->proorigin}</VAL>
</PARAM>
{/if}
{if isset($params[$row->proid])}
  {foreach $params[$row->proid] as $param}
<PARAM>
  <PARAM_NAME>{$param->prpname}</PARAM_NAME>
  <VAL>{$param->prpvalue}{if !empty($param->prpunit)}{$param->prpunit}{/if}</VAL>
</PARAM>
  {/foreach}
{/if}
{php
  $delCost = -1;
}
{foreach $delModes as $key=>$rowd}
  {php

    if ($row->provenid=='nosreti' && $key != 'GEIS')  {
      continue;
    }

    if ($row->provenid!='nosreti' && $key == 'GEIS') {
      continue;
    }
  }

  <DELIVERY>
    <DELIVERY_ID>{if $key == "CESKA_POSTA_NA_POSTU"}CESKA_POSTA_NAPOSTU_DEPOTAPI{elseif $key == "CESKA_POSTA_BALIKOVNA"}BALIKOVNA_DEPOTAPI{else}{$key}{/if}</DELIVERY_ID>
    {if isset($delModes[$key]["paybefore"])}
    {php
      $prcDel = ($delModes[$key]["paybefore"]->dellimitfrom <= $row->propricea && $delModes[$key]["paybefore"]->dellimitto > 0 ? 0 : $delModes[$key]["paybefore"]->delprice);
      $prcPay = ($delModes[$key]["paybefore"]->paylimitfrom <= $row->propricea && $delModes[$key]["paybefore"]->paylimitto > 0 ? 0 : $delModes[$key]["paybefore"]->payprice);

      if (!isset($delIdsExcludedDelFree[$delModes[$key]["paybefore"]->delid]) && ($row->prodelfree === 1 && ($key == "CESKA_POSTA_BALIKOVNA" || $key == "CESKA_POSTA"))) {
        $prcDel = 0;
      }

      if ((int)$row->prodelfree === 2) {
        $prcDel = $delModes[$key]["paybefore"]->delprice;
      }

      $prc = $prcDel + $prcPay;
      $delCost = $prc;
    }
    <DELIVERY_PRICE>{$prc}</DELIVERY_PRICE>
    {/if}
    {if isset($delModes[$key]["dobirka"])}
    {php
      $prcDel = ($delModes[$key]["dobirka"]->dellimitfrom <= $row->propricea && $delModes[$key]["dobirka"]->dellimitto > 0 ? 0 : $delModes[$key]["dobirka"]->delprice);
      $prcPay = ($delModes[$key]["dobirka"]->paylimitfrom <= $row->propricea && $delModes[$key]["dobirka"]->paylimitto > 0 ? 0 : $delModes[$key]["dobirka"]->payprice);

      if (!isset($delIdsExcludedDelFree[$delModes[$key]["dobirka"]->delid]) && $row->prodelfree === 1) {
        $prcDel = 0;
      }

      if ((int)$row->prodelfree === 2) {
        $prcDel = $delModes[$key]["dobirka"]->delprice;
      }
			if (!isset($delIdsExcludedDelFree[$delModes[$key]["paybefore"]->delid]) &&  ($row->prodelfree === 1 && ($key == "CESKA_POSTA_BALIKOVNA" || $key == "CESKA_POSTA"))) {
        $prcDel = 0;
        $prcPay = 0;
      }
      $prc = $prcDel + $prcPay;

      if ($prc < $delCost) {
        $delCost = $prc;
      }
    }
    <DELIVERY_PRICE_COD>{$prc}</DELIVERY_PRICE_COD>
    {/if}
  </DELIVERY>
{/foreach}
{if $delCost >= 0}
  <DELIVERY_COST>{$delCost}</DELIVERY_COST>
{/if}
  {if $serverId === 'web2'}
    {if !empty($row->procpcheureka)}
    <HEUREKA_CPC>{$row->procpcheureka|number:2:',':' '}</HEUREKA_CPC>
    {else}
    <HEUREKA_CPC></HEUREKA_CPC>
    {/if}
  {/if}
  {if $row->protypid5==1 || $row->proheko!=1}
  <HEUREKA_CART>0</HEUREKA_CART>
  {/if}
  {php
  $acsCnt = 0;
  }
  {if !empty($row->prooptions)}
    {php
    $arr = explode("|", trim($row->prooptions, "|"));
    }
    {foreach $arr as $proCode}
      {if isset($proAccess[$proCode])}
      {php
      $acsCnt ++;
      }
  <ACCESSORY>{$proCode}</ACCESSORY>
      {/if}
    {/foreach}
  {/if}
  {if !empty($proCatAcs[$row->procode])}
    {if !empty($prfCats[$proCatAcs[$row->procode]])}
      {foreach $prfCats[$proCatAcs[$row->procode]] as $proCode=>$proId}
      {php
      $acsCnt ++;
      }
  <ACCESSORY>{$proCode}</ACCESSORY>
      {/foreach}
    {/if}
  {/if}
  {if $acsCnt === 0 && !empty($drogAcs)}
  {foreach $drogAcs as $pc}
  <ACCESSORY>{$pc->procode}</ACCESSORY>
  {/foreach}
  {/if}
</SHOPITEM>
{/foreach}
</SHOP>