{default $pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{default $pageKeywords    = $page->pagkeywords}
{default $pageDescription = $page->pagdescription}

{block #content}
<main role="main">

  <section class="section">

    <article class="container" role="article">

      <div class="breadcrumbs" role="navigation" aria-label="Drobečková navigace">

        <a n:href="Homepage:default"><i class="fas fa-home"></i><span class="sr-only">Domů</span></a>
        <span class="breadcrumbs__separator" aria-hidden="true">&gt;</span>
        {$pageTitle}
      </div>

      {$page->pagbody|noescape}

      <h4>Přilohy</h4>
      {if count($attachments)}
        <ul>
        {foreach $attachments as $item}
        <li><a href="{$baseUrl}/files/{$item->atafilename}" target="_blank">{$item->ataname} ({$item->atatype})</a></li>
        {/foreach}
        </ul>
      {/if}


      {include banners.latte}

      {include actions.latte}

      {include 'favourites.latte', catalogs:$catalogFavourites}

    </article>

  </section>

</main>

