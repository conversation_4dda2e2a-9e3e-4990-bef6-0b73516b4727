{varType App\Orm\Catalog $catalog}
{default $pageTitle       = $catalog->catname}
{default $pageKeywords    = $catalog->catkeywords}
{default $pageDescription = empty($catalog->catdescription) ? strip_tags($catalog->catdesc) : $catalog->catdescription}
{var $canonicalUrl = $presenter->link('//detail', array('id'=>$catalog->id, 'key'=>$catalog->getURLKey(), 's'=>'', 'o'=>'', 'm'=>array()))}

{php
  $GLOBALS["ecommProId"] = '';
  $GLOBALS["ecommPageType"] = 'category';
  $GLOBALS["ecommTotalValue"] = 0;
}

{block adminMenu}
{if $adminLogIn}
  <div class="container">
    <a href="{plink :Admin:Catalog:edit, $catalog->id}" target="admin">&#9998;</a>
  </div>
{/if}
{/block}




{block content}
{var $proIds = []}

<section class="section">

    <article class="container" role="article">

      <div class="row row--start category-section">

        <aside class="col col--2 sidebar" role="complementary">

            <nav class="sidebar-nav" role="navigation">

                <h2><a n:href="Catalog:detail, $catalog->root->id, $catalog->getURLKey()">{$catalog->root->catname}</a></h2>

                 <ul class="list sidebar-nav__ul-primary">
                    {include menu, $catalog->root->catalogs,1}
                                   {define menu,$catalogs,int $level}
                        {varType App\Orm\Catalog[] $catalogs}
                        <li n:foreach="$catalogs as $catalogMenu">

                            <a n:href="Catalog:detail, $catalogMenu->id, $catalogMenu->getURLKey()" n:class="$catalogMenu->id == $catalog->id ? active">
                                {if $level == 1}<strong>{/if}
                                   {$catalogMenu->catname}
                                {if $level == 1}</strong>{/if}
                            </a>
                            <ul n:if="in_array($catalogMenu,$catalog->getTree(),true) && count($catalogMenu->catalogs)">
                                {include menu, $catalogMenu->catalogs,$level+1}
                            </ul>
                        </li>
                    {/define}
                </ul>


            </nav>

          <div class="sidebar-filter" role="region" aria-label="Filtrování produktů">

            <div class="sidebar-filter__mobile">
              <span class="modal__close js-filter-close">
                <span class="icon icon--times-solid">
                  <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <use xlink:href="{$baseUrl}/style/{$serverId}/img/icons.svg#times-solid" x="0" y="0" width="100%" height="100%"></use>
                  </svg>
                </span>
              </span>

              <h2>Upřesnit parametry</h2>
            </div>

            {form catalogSearchForm}

            <div class="sidebar-filter__block is-open">
              <h2 class="sidebar-filter__toggle js-parent-toggle">Značky</h2>

              <div class="sidebar-filter__show-hide">

                {foreach $form['mans']->controls as $item}

                <p class="form-checkbox{if $iterator->getCounter() > 10}  js-more is-hidden{/if}">
                  <input n:name="$item">
                  <span class="form-checkbox__box"></span>
                  <label n:name="$item" class="form-label"/>
                </p>

                {/foreach}

                {if iterator_count($form['mans']->getControls()) > 10}
                <p class="more-control js-more-control">
                  <span class="more-control__open">Další značky<i class="fas fa-caret-down"></i></span>
                  <span class="more-control__close">Skrýt další značky<i class="fas fa-caret-up"></i></span>
                </p>
                {/if}

              </div>
            </div>

            {*
            <div class="sidebar-filter__block is-open">
              <h2 class="sidebar-filter__toggle js-parent-toggle">Cena</h2>
              <div class="sidebar-filter__show-hide">
                {php //include('components/range.php'); }
              </div>
            </div>
            *}

            {*
            <div class="sidebar-filter__block is-open">
              <h2 class="sidebar-filter__toggle js-parent-toggle">Typ</h2>
              <div class="sidebar-filter__show-hide">
                <p class="form-checkbox">
                  <input type="checkbox" name="checkbox" id="compatible" checked="checked">
                  <span class="form-checkbox__box"></span>
                  <label class="form-label" for="compatible">Kompatibilní <span>48</span></label>
                </p>
                <p class="form-checkbox">
                  <input type="checkbox" name="checkbox" id="original">
                  <span class="form-checkbox__box"></span>
                  <label class="form-label" for="original">Originální <span>12</span></label>
                </p>
              </div>
            </div>

            <div class="sidebar-filter__block">
              <h2 class="sidebar-filter__toggle js-parent-toggle">Barva</h2>
              <div class="sidebar-filter__show-hide">
                <p class="form-checkbox">
                  <input type="checkbox" name="checkbox" id="white">
                  <span class="form-checkbox__box"></span>
                  <label class="form-label" for="white">Bílá <span>12</span></label>
                </p>
                <p class="form-checkbox">
                  <input type="checkbox" name="checkbox" id="black">
                  <span class="form-checkbox__box"></span>
                  <label class="form-label" for="black">Černá <span>24</span></label>
                </p>
              </div>
            </div>

            <div class="sidebar-filter__block">
              <h2 class="sidebar-filter__toggle js-parent-toggle">Další</h2>
              <div class="sidebar-filter__show-hide">
                <p class="form-checkbox">
                  <input type="checkbox" name="checkbox" id="next">
                  <span class="form-checkbox__box"></span>
                  <label class="form-label" for="next">Další možnosti</label>
                </p>
              </div>
            </div>
            *}
            {input search}
            {/form}

          </div>

        </aside>


        <div class="col col--2 col--grow">

          <div class="breadcrumbs" role="navigation" aria-label="Drobečková navigace">

            <a n:href="Homepage:default"><i class="fas fa-home"></i><span class="sr-only">Domů</span></a>

            {foreach $catalog->getTree() as $oneCatalog}
              <span class="breadcrumbs__separator" aria-hidden="true">&gt;</span>
              {if $iterator->isLast()}
                {$oneCatalog->catname}
              {else}
                <a n:href="Catalog:detail, $oneCatalog->id, $oneCatalog->getURLKey()">{$oneCatalog->catname}</a>
              {/if}
            {/foreach}

          </div>


          <h1>{$catalog->catname}</h1>



           {* popis kategorie *}
        {if !empty($catalog->catdesc)}
        <div class="category-description
          {if $catalog->root && $catalog->root->id == 13212}
          category-description--xxx
          {elseif $catalog->root && $catalog->root->id == 1135}
          category-description--profi
          {/if}
          ">
          {if  $catalog->root && $catalog->root->id == 13212}
          <div class="category-description__legend"><i class="fas fa-mask"></i> Diskrétní balení</div>
          {elseif  $catalog->root && $catalog->root->id == 1135}
          <div class="category-description__legend"><i class="fas fa-screwdriver-wrench"></i> Profesionální montáž</div>
          {/if}
          {$catalog->catdesc|noescape}
          {if  $catalog->root && $catalog->root->id == 13212}
          <i class="fas fa-mask category-description__icon"></i>
          {elseif  $catalog->root && $catalog->root->id  == 1135}
          <i class="fas fa-screwdriver-wrench category-description__icon"></i>
          {/if}
        </div>
        {/if}

          {*
          <div class="slider slider--banner">
            <div class="slider__slick">
              <a href=#><img src="/img/banner-1.png" width="1100" height="180" alt=""></a>
              <a href=#><img src="/img/banner-2.png" width="1100" height="180" alt=""></a>
            </div>
          </div>
          *}


          <div class="centerMenu">
            <div class="row row--start">
              <div class="col col--6" n:foreach="$catalog->catalogs as $childrn">
                <a n:href="Catalog:detail, $childrn->id, $childrn->getURLKey()">
                  <strong>{$childrn->catname}</strong>
                  <img n:if="$childrn->getImagSrc()" src="{$baseUrl}/{$childrn->getImagSrc()}" alt="{$childrn->catname}">
                </a>
              </div>
            </div>
          </div>


          {*
          <div class="manufacturer-top">
            <p>Top značky</p>
            <a href="#"><img src="/img/logo-aeg.png" alt=""></a>
            <a href="#"><img src="/img/logo-aeg.png" alt=""></a>
            <a href="#"><img src="/img/logo-aeg.png" alt=""></a>
          </div>
          *}

          <div class="quick-filter">

          <div class="row">
            <div class="col col--2">
              <div class="quick-filter__sort">
                <a href="{plink this o=>NULL}" class="btn{if empty($presenter->o)} is-open{/if}">Nejoblíbenější</a>
                <a href="{plink this o=>'price'}" class="btn{if $presenter->o === 'price'} is-open{/if}">Nejlevnější</a>
                <a href="{plink this o=>'price_'}" class="btn{if $presenter->o === 'price_'} is-open{/if}">Nejdražší</a>
              </div>
            </div>

            <div class="col col--2">
              <div class="quick-filter__tag">
                <p class="form-checkbox">
                  <input type="checkbox" name="stock" id="stock" n:attr="checked: $presenter->s=='onStore'" onchange="document.getElementById('onStoreBtn').click();">
                  <span class="form-checkbox__box"></span>
                  <label class="form-label" for="stock" >Skladem</label><a id="onStoreBtn" n:href="this , s:$presenter->s=='onStore'?'':'onStore'"></a>
                </p>
                <p class="form-checkbox" n:if="false">
                  <input type="checkbox" name="shipping" id="shipping" n:attr="checked: $presenter->freeDelivery" onchange="document.getElementById('freeDeliveryfiltr').click();">
                  <span class="form-checkbox__box"></span>
                  <label class="form-label" for="shipping">Doprava zdarma</label><a id="freeDeliveryfiltr" n:href="this , freeDelivery:!$presenter->freeDelivery"></a>
                </p>
                <p class="form-checkbox" n:if="false">
                  <input type="checkbox" name="unboxed" id="unboxed" n:attr="checked: $presenter->unpacked" onchange="document.getElementById('unpackedfiltr').click();">
                  <span class="form-checkbox__box"></span>
                  <label class="form-label" for="unboxed"> Rozbaleno</label><a id="unpackedfiltr" n:href="this , unpacked:!$presenter->unpacked"></a>
                </p>
              </div>
            </div>
          </div>

          <button class="btn quick-filter__mobile js-filter"><i class="fas fa-filter"></i> Upřesnit parametry</button>

        </div>

          {control paginator}

          <div class="row row--start product-row">

            {foreach $productsData as $row}

              {php
              $proIds[] = $row->proid;
              $GLOBALS["ecommTotalValue"] += $row->proprice;

              $proaccess = $enum_proaccess[$row->proaccess];
              if (isset($onRoad[$row->proid])) {
                if ($row->proaccess != 0 && $onRoad[$row->proid]->qty > 0) $proaccess = 'Na cestě';
              }
              }

              <div class="col col--4">
                {include 'productItem.latte', product:$row}
              </div>

           {/foreach}
           {if count($productsData) == 0}
            <p>Omlouváme se, momentálně zde nic nenabízíme.</p>
           {/if}
          </div>

          {control paginator}

          {*
          <div class="center">
            <a href="#" class="btn">Dalších 24 produktů</a>
          </div>
          *}
        </div>

      </div>

      <hr>

       {include 'banners.latte'}

      {include 'actions.latte'}

      {include 'favourites.latte', catalogs:$catalogFavourites}

    </article>

  </section>

{php
$GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
}

<script>

  $('.storeChkFilter').click(function() {
      var val = $(this).prop('checked');
      $('.storeChkFilter').prop('checked', false);
      $(this).prop('checked', val);
  });

</script>
{/block}
