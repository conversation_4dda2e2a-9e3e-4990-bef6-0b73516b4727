{contentType application/xml; charset=utf-8}
<?xml version="1.0"?>
<rss version="2.0" xmlns:g="http://base.google.com/ns/1.0">
  <channel>
    <title>{$presenter->config["SERVER_NAME"]}</title>
    <link>{$baseUrl}</link>
    <description>{$presenter->config["HEADER_H2"]}</description>
    {php $googlePath = [];}
    {foreach $rows as $row}
      {php
        $catPath = str_replace('|', ' | ', $row->catpath);

        $googlePath = ($row|getCatalogPath:$googlePath:$googleCatalogs);

        $catName = $row->catname;
        if (isset($heurekaPath[$row->catid])) {
          $catName = $heurekaPath[$row->catid];
        }

        $proname = ($row|getFeedProName:$catName);

        $giftsNames = ($row|getFeedGiftsNames:$gifts:$catalogGifts:$defGiftsNames);

        $key = $row->proid."-".substr(md5($row->proid . $row->prodatec), 0, 6);

        if ($priceType === 'a') {
          $price = $row->propricea;
        } else if ($priceType === 'e') {
          $price = ($row->propricee > 0 ? $row->propricee : $row->propricea);
        }

        if (isset($eurRate)) {
          $price = round($price / $eurRate, 1);
          $curCode = 'EUR';
        } else {
          $curCode = 'CZK';
        }
      }

      <item>
        <title>{$proname}{if !empty($row->pronameadd)} {$row->pronameadd}{/if} {if !empty($giftsNames) && $row->propricee == 0} +DÁREK{/if}</title>
        {if !empty($key)}
        <link>{plink //:Front:Product:detail, $row->proid, getProKey($row), "g"=>$key}</link>
        {else}
        <link>{plink //:Front:Product:detail, $row->proid, getProKey($row)}</link>
        {/if}

        <description>{if !empty($row->prodescs)}<![CDATA[{$row->prodescs|striptags}]]>{/if}</description>
        <g:brand>{$row->manname}</g:brand>
        {if !empty($row->procode2)}<g:gtin>{$row->procode2}</g:gtin>{else}<g:identifier_exists>FALSE</g:identifier_exists>{/if}
        <g:image_link>{$baseUrl}/{getPicName($row, 'product/detail')}</g:image_link>
        <g:price>{$price} {$curCode}</g:price>
        <g:condition>new</g:condition>
        <g:id>{$row->proid}</g:id>
        <g:availability>{if getProAccess($row) <= 7}in stock{else}out of stock{/if}</g:availability>
        {foreach $delModes as $key=>$rowd}
          {php
          if ($row->provenid=='nosreti' && $key != 'GEIS')  continue;
          if ((string)$row->provenid=='' && $key == 'GEIS')  continue;
          }
          {if isset($delModes[$key]["paybefore"])}
            {php
            $prcDel = ($delModes[$key]["paybefore"]->dellimitfrom <= $row->propricea && $delModes[$key]["paybefore"]->dellimitto > 0 ? 0 : $delModes[$key]["paybefore"]->delprice);
            $prcPay = ($delModes[$key]["paybefore"]->paylimitfrom <= $row->propricea && $delModes[$key]["paybefore"]->paylimitto > 0 ? 0 : $delModes[$key]["paybefore"]->payprice);

            if (!isset($delIdsExcludedDelFree[$delModes[$key]["paybefore"]->delid]) && $row->prodelfree === 1) {
              $prcDel = 0;
            }

            $prc = $prcDel + $prcPay;
           }
          <g:shipping>
            <g:country>CZ</g:country>
            <g:service>{$delModes[$key]["paybefore"]->delname} {$delModes[$key]["paybefore"]->payname}</g:service>
            <g:price>{$prc} {$curCode}</g:price>
          </g:shipping>
          {/if}
          {if isset($delModes[$key]["dobirka"])}
            {php
            $prcDel = ($delModes[$key]["dobirka"]->dellimitfrom <= $row->propricea && $delModes[$key]["dobirka"]->dellimitto > 0 ? 0 : $delModes[$key]["dobirka"]->delprice);
            $prcPay = ($delModes[$key]["dobirka"]->paylimitfrom <= $row->propricea && $delModes[$key]["dobirka"]->paylimitto > 0 ? 0 : $delModes[$key]["dobirka"]->payprice);

            if (!isset($delIdsExcludedDelFree[$delModes[$key]["dobirka"]->delid]) && $row->prodelfree === 1) {
              $prcDel = 0;
            }

            $prc = $prcDel + $prcPay;
            }
          <g:shipping>
            <g:country>CZ</g:country>
            <g:service>{$delModes[$key]["dobirka"]->delname} {$delModes[$key]["dobirka"]->payname}</g:service>
            <g:price>{$prc} {$curCode}</g:price>
          </g:shipping>
          {/if}
        {/foreach}
        {php
          $catPath = mb_strtolower($row->catpath, 'UTF-8');
          $catPath = str_replace('|', ' > ', $catPath);

          if ($row->protypid4 == 1) {
            $catPath = "TOP produkty";
          } else {
            $catPath = mb_strtolower($row->catpath, 'UTF-8');
            $catPath = str_replace('|', ' > ', $catPath);
          }
        }
        <g:product_type><![CDATA[{$catPath|noescape}]]></g:product_type>
      {if $row->protypid4 == 1}
        <g:google_product_category>TOP produkty</g:google_product_category>
      {elseif isset($googlePath[$row->catid])}
        <g:google_product_category>{$googlePath[$row->catid]}</g:google_product_category>
      {/if}
    </item>
    {/foreach}
  </channel>
</rss>