{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  {foreach $products as $row}
  <url>
    <loc>{plink //Product:detail, $row->proid, getProKey($row)}</loc>
    <lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
  {/foreach}
  {foreach $catalogs as $catalog}
  <url>
    <loc>{plink //Catalog:detail, $catalog->catid, getURL<PERSON>ey($catalog->catname, $catalog->catkey)}</loc>
    <lastmod>{$catalog->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.7</priority>
  </url>
    {foreach $catalog->subCats as $item}
  <url>
    <loc>{plink //Catalog:detail, $item->catid, getUR<PERSON><PERSON><PERSON>($item->catname, $item->catkey)}</loc>
    <lastmod>{$item->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.6</priority>
  </url>
    {/foreach}
  {/foreach}
  {foreach $menuTopExport as $row}
  <url>
    <loc>{plink //Page:detail, $row->pagurlkey}/</loc>
    <lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.5</priority>
  </url>
  {/foreach}
  {foreach $menuIndexs as $iRow}
  <url>
    <loc>{plink //Page:detail $iRow->pagurlkey}</loc>
    <lastmod>{$iRow->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
  {/foreach}
</urlset>