{* nastaveni promennych *}
{default $pageTitle = $presenter->translator->translate('Stav <PERSON> o<PERSON>')}
{default $pageRobots = "nofollow,noindex"}

{php
  $GLOBALS["ecommProId"] = $order->proids;
  $GLOBALS["ecommPageType"] = 'purchase';
  $GLOBALS["ecommTotalValue"] = $order->ordpriceinclvat;
}

{block #ogHead}
{if $order->ordpublished != 1 && $isProduction}
  {if !empty($presenter->config["ZBOZICZ"]["IdMereniKonverzi"])}
    <script type="text/javascript" src="https://c.seznam.cz/js/rc.js"></script>
    <script>
      var conversionConf = {
          zboziId: {$presenter->config["ZBOZICZ"]["IdMereniKonverzi"]},
          orderId: {$order->ordcode},
          zboziType: "standard",
          id: 100021291,
          value: {round($order->ordpricevat)},
          consent: {($order->ordheurekagdpr == 1 || $order->ordheurekaoff == 1 ? 0 : 1)},
      };

      if (window.rc && window.rc.conversionHit) {
          window.rc.conversionHit(conversionConf);
      }
    </script>
  {/if}
{/if}
{/block}

{block #content}

{if $order->ordpublished != 1 && $isProduction}
  {include @OrderMeasurement.latte}
{/if}

<main role="main">

  <section class="section">

    <article class="container" role="article">

    {if $order->ordpublished != 1}
      {if $blockOrderAccepted}
       <div id="accepted">
        <h1>{$blockOrderAccepted->pagname}</h1>
        {$blockOrderAccepted->pagbody|noescape}
        </div>
      {/if}
    {else}
      <h1>Stav Vaší objednávky je: {$enum_ordstatus[$order->ordstatus]}</h1>
      {if $order->ordpaystatus == 1}
      <p>Objednávka je <strong>uhrazena.</strong></p>
      {/if}
      <br>
      <p>
        <h3>Zvolený způsob dopravy:</h3>
        <h4>{$delivery->delname}</h4>
        {$delivery->deldesc|breakLines}<br>
        {if !empty($order->ordparcode)}
    <h4>{_'Vaše číslo balíku je'}: {$order->ordparcode}.</h4>
          {php
            if (!empty($delivery->delurlparcel)) {
              $url = str_replace('#CODE#', $order->ordparcode, $delivery->delurlparcel);
            }
          }
          {if !empty($url)}{_'Balík můžete sledovat'} <a href="{$url|noescape}" target="_blank">{_'na stránkách přepravce'}</a>.<br>{/if}
          <br>
        {/if}
      <br>
      <h3>Zvolený způsob platby:</h3>
      <h4>{$payment->delname}</h4>

      {if $payment->delcode == 'platimpak'}
          <p><strong>PlatímPak – platba až za 30 dnů</strong><br />
            PlatímPak - kupte si zboží teď, zaplaťte do 30 dnů bez navýšení.<br />
            Vyhodnocení online do 1 minuty.<br />
            PlatímPak poskytuje Raiffeisenbank, a.s. Bez nutnosti založení běžného účtu.<br />
            <a href="https://www.platimpak.cz" target="_blank"><img src="{$baseUrl}/img/platim-pak-logo.png" alt="Platim pak"> </a></p>
      {/if}


      </p>
    {/if}

    {if $order->ordpaystatus != 1}

      {if !empty($qrPlatbaFio)}
        <br />
        <h4>Zvolili jste platbu <strong>převodem na náš účet</strong>.</h4>
        <strong>QR platební kód pro Vaši mobilní banku:</strong><br />
        {if !empty($qrPlatbaFio)}
          <strong>platba na náš účet ve FIO bance:</strong><br />
          <img src="{$qrPlatbaFio|dataStream}" title="QR Platba FIO"  width="150px" height="150px" /><br>
        {/if}
      {/if}

      {if $payment->delcode == 'paybefore'}
      <br>
      <strong>Platební údaje pro platbu předem převodem:</strong><br />
        {if $delivery->delcouid === 1}
        Číslo účtu:<br>
        FIO banka {$fioAccountNo}<br />
        {else}
        {$presenter->config["SERVER_ACCNAME"]}<br />
        IBAN: {$presenter->config["SERVER_ACCIBAN"]}<br />
        SWIFT: {$presenter->config["SERVER_ACCSWIFT"]}<br />
        {/if}
      Variabilní symbol: {$order->ordcode}<br />
      Částka: {$order->ordpricevat|formatPrice} {$order->ordpricevat|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}
      {/if}

      {if $payment->delcode == 'creditcard' && $order->ordpaystatus === 0 &&  $delivery->delcode != 'OSOBNE'}
        <div class="cofidis1">
        <h3 style="color: red">Zvolili jste platbu platební kartou.</h3>
        <p>Objednávka zatím není uhrazena.</p>
        <form action="{plink //:Front:Payment:openGate, $order->ordid.substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8)}" method="get">
        <button name="submit" class="btn" >Zaplatit {$order->ordpricevat|formatPrice}</i></button>
        </form>

          {* dělá to neplechu, když klikne v okamžiku přesměrování, brána se otevře 2x a pokud je to lama může i 2x zaplatit
          {if $order->ordpublished != 1}
          <script>setTimeout(function(){ window.location = {plink //:Front:Payment:openGate $order->ordid.substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8)}; }, 3000);</script>
          <p>Za chvíli budete přesměrování na platební bránu automaticky, nebo klikněte na tlačítko zaplatit.</p>
          {/if}
          *}

        </div>
      {/if}

      {if $payment->delcode == 'platimpak'}

        <div class="cofidis1">
          {if $order->ordpaystatus === 0}
            {if $order->ordpaysesstatus === 'FIN_KO'}
              <p style="color: red; font-weight: bold">Platba PlatímPak byla zamítnuta.</p>
            {else}
          <p>Objednávka zatím není uhrazena.</p>
          <form action="{plink //:Front:Order:platimPakPay, $order->ordid.substr(md5($order->ordid.$order->orddatec->getTimestamp()), 0, 8)}" method="get">
            <button name="submit" class="btn" >Zaplatit s PlatímPak {$order->ordpricevat|formatPrice}</button>
          </form>
            {/if}
          {/if}
        </div>

      {/if}

      {if $payment->delcode == 'cetelem'}
      <div class="cetelem1">
      <div id="cetelem1"></div>
      <form action="{$cetelem->conf["url"].$cetelem->conf["urlreqs"]}" method="post" id="cetelemForm">
      <input type="hidden" name="kodProdejce" value="{$cetelem->kodProdejce}" />
      <input type="hidden" name="cenaZbozi" value="{(int)$cetelem->cenaZbozi}" />
      <input type="hidden" name="calc" value="1" />
      <input type="hidden" name="url_back_ok" value="{plink Cetelem:backOk}" />
      <input type="hidden" name="url_back_ko" value="{plink Cetelem:backKo}" />
      <input type="hidden" name="obj" value="{$order->ordcode}" />
      <input type="hidden" name="numklient" value="{$order->ordcode}_{$order->ordusrid}" />
      <p class="submit"><input class="button" type="submit" name="submit" value="Zažádat o splátky" /></p>
      </form>
      </div>
      <script type="text/javascript">
      $(document).ready(function(){
          $('#cetelemForm').submit();
      });
      </script>
      {/if}

      {if $payment->delcode == 'cofidis'}
      <div class="cofidis1">
      <div id="cofidis1"></div>
      <form action="{$cofidisUrl}" method="get">
      <p class="submit"><input class="button" type="submit" name="submit" value="Zažádat o splátky" /></p>
      </form>
      </div>
      {/if}

    {/if}
      <br>
      <br>
      <br>
      <div>
      <strong>Navštivte náš instagram: </strong>
      <a href="https://www.instagram.com/shopcom.cz?utm_source=qr&igsh=MzR3bTZleHNvN2t2" target="_blank">@SHOPCOM.CZ</a>
      </div>
    </article>

  </section>

</main>

{/block}