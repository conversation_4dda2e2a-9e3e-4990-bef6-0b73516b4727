transport_service;address_state;register_branch;destination_branch;order_number;partner_consignment_id;parcel_count;cash_on_delivery;currency;password;variable;customer_name;customer_surname;company_name;customer_phone;customer_email;address_street;address_town;address_zip;insurance;weight;allow_card_payment;require_full_age;note
{foreach $rows as $row}
{php
  $number = App\classes\ClassTools::formatPhone($order->ordtel, $order->ordicouid);

  $row->ordipostcode = trim(str_replace(" ", "", $row->ordipostcode));

  if (!empty($row->ordstname)) {
    $row->ordiname = $row->ordstname;
    $row->ordilname = $row->ordstlname;
    $row->ordifirname = $row->ordstfirname;
    $row->ordistreet = $row->ordststreet;
    $row->ordistreetno = $row->ordststreetno;
    $row->ordicity = $row->ordstcity;
    $row->ordipostcode = $row->ordstpostcode;
  }
  
}
ulozenka;;;{$row->orddelspec};{$row->ordcode};;1;{if $row->paycode=='dobirka'}{$row->ordpricevat}{/if};CZK;;;{$row->ordiname};{$row->ordilname};{$row->ordifirname};{$number};{$row->ordmail};{$row->ordistreet} {$row->ordistreetno};{$row->ordicity};{$row->ordipostcode};;;1;;{$row->ordnotedel}
{/foreach}