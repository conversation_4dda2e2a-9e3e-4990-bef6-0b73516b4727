{default $pageTitle = "Doručovací údaje"}
{default $pageRobots = "nofollow,noindex"}

{block content}

    {include 'basketProgress.latte'}

    <section class="section section--cart">

        <article class="container container--md" role="article">

            <h1 class="cart-headline">{$pageTitle}</h1>

            {form contactForm}
                {include 'formError.latte', form: $form}

                <div class="cart-form">

                    <h2>Status zákazníka</h2>

                    <div class="cart-form__top">
                        <p class="form-checkbox">
                            <input type="radio" n:name="ordcustype:osoba" id="personal" class="js-check-company" checked>
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" for="personal">Soukromá osoba</label>
                        </p>

                        <p class="form-checkbox">
                            <input type="radio"  n:name="ordcustype:firma" id="diffcompany"  class="js-check-company js-check-company-open">
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" for="diffcompany">Firma</label>
                        </p>

                        <p class="form-checkbox">
                            <input type="radio"  n:name="ordcustype:organ" id="difforganization"  class="js-check-company js-check-company-open">
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" for="difforganization">Státní organizace <span>(školy, obce, nemocnice, státní i polostátní firmy, příspěvkové organizace, apod.)</span></label>
                        </p>

                        <div class="cart-form__hidden {if $form["ordcustype"]->getValue()!='firma'&&$form["ordcustype"]->getValue()!='organ'}is-hidden{/if} js-check-data-company">
                            <div class="form-row">
                                <p>
                                    <label for="ico">IČ</label>
                                    <input type="text" id="ico">
                                </p>

                                <p>
                                    <label for="dic">DIČ</label>
                                    <input type="text" id="dic">
                                </p>
                            </div>

                            <div class="form-row">
                                <p>
                                    <label for="company-name">Název firmy</label>
                                    <input type="text" id="company-name">
                                </p>

                                <p>
                                    <label for="order-number">Číslo objednávky dodavatele</label>
                                    <input type="text" id="order-number">
                                </p>
                            </div>

                            <p class="form-checkbox">
                                <input type="checkbox" name="company" id="vatpayercompany">
                                <span class="form-checkbox__box"></span>
                                <label class="form-label" for="vatpayercompany">Jsem plátce DPH</label>
                            </p>

                        </div>

                    </div>

                    <h2>Kontaktní údaje</h2>

                    <div class="form-row">
                        <p>
                            {label ordiname class=>"is-required" /}
                            {input ordiname required=>"required"}
                        </p>

                        <p>
                            {label ordilname class=>"is-required" /}
                            {input ordilname required=>"required"}
                        </p>

                    </div>

                    <div class="form-row">
                        <p>
                            {label ordifirname /}
                            {input ordifirname autocomplete=>"organization"}
                        </p>

                        <p>
                            {label ordistreet class=>"is-required" /}
                            {input ordistreet required=>"required"}
                        </p>
                    </div>

                    <div class="form-row">
                        <p>
                            {label ordistreetno class=>"is-required" /}
                            {input ordistreetno required=>"required"}
                        </p>

                        <p>
                            {label ordicity class=>"is-required" /}
                            {input ordicity required=>"required"}
                        </p>
                    </div>

                    <div class="form-row">
                        <p>
                            {label ordipostcode class=>"is-required" /}
                            {input ordipostcode required=>"required"}
                        </p>
                        <p>
                            Země
                            {$enum_delcouid[$couId]}
                        </p>
                    </div>

                    <div class="form-row">
                        <p><!--ato-->
                            {label ordmail class=>"is-required" /}
                            {input ordmail required=>"required"}
                        </p>

                        <p>
                            {label ordtel class=>"is-required" /}
                            {input ordtel required=>"required"}
                        </p>
                    </div>

                    <p>
                        {label ordnote /}
                        {input ordnote}
                    </p>

                    <div class="cart-form__bottom">
                        <p class="form-checkbox">
                            <input type="checkbox" name="diffaddress" id="diffaddress" class="js-check">
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" for="diffaddress">Chci doručit na jinou adresu</label>
                        </p>

                        <div class="cart-form__hidden is-hidden js-check-data">
                            <div class="form-row">
                                <p>
                                    {label ordstname class=>"is-required" /}
                                    {input ordstname required=>"required", autocomplete=>"given-name"}
                                </p>

                                <p>
                                    {label ordstlname class=>"is-required" /}
                                    {input ordstlname required=>"required", autocomplete=>"family-name"}
                                </p>
                            </div>

                            <div class="form-row">
                                <p>
                                    {label ordstfirname /}
                                    {input ordstfirname autocomplete=>"organization"}
                                </p>

                                <p>
                                    {label ordststreet class=>"is-required" /}
                                    {input ordststreet required=>"required", autocomplete=>"street-address"}
                                </p>
                            </div>

                            <div class="form-row">
                                <p>
                                    {label ordststreetno class=>"is-required" /}
                                    {input ordststreetno required=>"required", autocomplete=>"street-address"}
                                </p>

                                <p>
                                    {label ordstcity class=>"is-required" /}
                                    {input ordstcity required=>"required", autocomplete=>"address-level2"}
                                </p>
                            </div>

                            <div class="form-row">
                                <p>
                                    {label ordstpostcode class=>"is-required" /}
                                    {input ordstpostcode required=>"required", autocomplete=>"given-name"}
                                </p>
                                <p>
                                    Země
                                    {$enum_delcouid[$couId]}
                                </p>

                            </div>
                        </div>
                    </div>

                    {ifset $form['maillist']}
                       <p class="form-checkbox">
                           <input type="checkbox" n:name="maillist">
                           <span class="form-checkbox__box"></span>
                           <label class="form-label" n:name="maillist">Souhlasím se zasíláním slevových kupónů a newsletterů a vyjadřuji tímto souhlas s <a href="/text-gdpr" target="_blank">podmínkami zpracování osobních údajů</a>.</label>
                       </p>
                    {/ifset}

                    <p class="form-checkbox">
                       <input type="checkbox" n:name="ordheurekagdpr">
                       <span class="form-checkbox__box"></span>
                       <label class="form-label" n:name="ordheurekagdpr">Nesouhlasím se zasláním dotazníku spokojenosti v rámci programu Ověřeno zákazníky.</label>
                    </p>

                </div>

                <div class="cart-sumprice">

                    <h2>Celková cena za zboží</h2>

                    <dl>
                        {*
                        <dt>Cena bez DPH:</dt>
                        <dd class="cart-sumprice__vatless">{$basket->priceSumTotal|formatPrice}</dd>
                        *}
                        <dt>Celková cena s DPH:</dt>
                        <dd class="cart-sumprice__vatinclude">{$basket->priceSumTotalVat|formatPrice}</dd>
                    </dl>

                </div>

                <p><strong>Odesláním objednávky a stisknutím tlačítka "Odeslat objednávku zavazující k platbě" potvrzuji, že jsem se seznámil s
                    <a href="{$baseUrl}/text-obchodni-podminky" target="_blank">obchodními podmínkami</a> internetového obchodu {$presenter->config["SERVER_NAMESHORT"]} a
                    <a href="{$baseUrl}/text-gdpr" target="_blank">zásadami ochrany osobních údajů.</a>
                    </strong>
                </p>

                <nav class="cart-pagination" role="navigation" aria-label="Nákupní košík">

                    <p>
                        <a n:href="Basket:" class="btn btn--txt"><i class="fas fa-caret-left"></i>Zpět do košíku</a>
                    </p>
                    <p>
                        <button n:name="submit" class="btn" >Odeslat objednávku zavazující k platbě<i class="fas fa-caret-right"></i></button>
                    </p>
                </nav>

            {/form}

        </article>

    </section>
{/block}
