{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<item_list>
  {foreach $rows as $row}
  <item id="{$row["procode"]}">
    <stock_quantity>{$row["proqty"]}</stock_quantity>
    <delivery_time orderDeadline="{$row["deldatedeadline"]|date:'Y-m-d H:i'}">{$row["deldate"]|date:'Y-m-d'}</delivery_time>
    {if isset($row["depos"])}
    {foreach $row["depos"] as $stoId => $depo}
      {if $depo["proqty"] > 0 && isset($depoIds[$stoId])}
      <depot id="{$depoIds[$stoId]}">
      <stock_quantity>{$depo["proqty"]}</stock_quantity>
      <pickup_time orderDeadline="{$depo["pickupdatedeadline"]|date:'Y-m-d H:i'}">{$depo["pickupdate"]|date:'Y-m-d'}</pickup_time>
      </depot>
      {/if}
    {/foreach}
    {/if}
  </item>
  {/foreach}
</item_list>