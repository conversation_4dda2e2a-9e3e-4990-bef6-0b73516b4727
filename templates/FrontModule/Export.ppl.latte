
{contentType: text/plain; charset=UTF-8}
{foreach $rows as $row}
{php 
$phone = "";
if (!empty($row->ordtel)) {
  $phone = trim($row->ordtel);
  $phone = '+420 '.substr($phone, 0, 3)." ".substr($phone, 3, 3)." ".substr($phone, 6, 3);
}
//pokud je jina dodaci adresa 
if (!empty($row->ordstname)) {
  $row->ordiname = $row->ordstname; 
  $row->ordilname = $row->ordstlname; 
  $row->ordifirname = $row->ordstfirname; 
  $row->ordistreet = $row->ordststreet; 
  $row->ordistreetno = $row->ordststreetno; 
  $row->ordicity = $row->ordstcity; 
  $row->ordipostcode = $row->ordstpostcode;
  $row->ordicoiud = $row->ordstcouid;
}
$row->ordipostcode = trim(str_replace(" ", "", $row->ordipostcode));

$codPrice = 0;
if ($row->paycode == 'dobirka' || $row->paycode == 'cash') {
  $codPrice = round((double)$row->ordpricevat);
} else if (($row->paycode == 'cofidis' || $row->paycode == 'cetelem') && $row->ordfirstpay > 0) {
  $codPrice = round((double)$row->ordfirstpay);
}

}
"{if empty($row->ordifirname)}{$row->ordiname} {$row->ordilname}{else}{$row->ordifirname}{/if}";"{$row->ordistreet}";"{$row->ordistreetno}";"{$row->ordicity}";"{$row->ordipostcode}";{if $row->delid==9}"SK"{else}"CZ"{/if};"{if $codPrice>0}PRIDOB{else}PRI{/if}";;1;{if $codPrice>0}{$codPrice}{else}{/if};"CZK";"{$row->ordcode}";"{$row->ordiname} {$row->ordilname}";"{$phone}";"{$row->ordmail}";"Obj. {$row->ordcode}";{round($row->ordpricevat, 0)};;;"Obj. {$row->ordcode}";
{/foreach}