{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>
{foreach $rows as $row}
  {php
    $pos = strpos($row->catpath, '|');
    $catPath = substr($row->catpath, $pos + 1);
  }

<ITEM>
  <PROID>{$row->proid}</PROID>
  <PROCODE>{$row->procode}</PROCODE>
  <PROCODE2>{$row->procode}</PROCODE2>
  <PROCODEVEN>{$row->procode2}</PROCODEVEN>
  <PRONAME>{$row->proname}</PRONAME>
  <PROSEARCHKEYWORDS>{$row->prosearchkeywords}</PROSEARCHKEYWORDS>
  <MANNAME>{$row->manname}</MANNAME>
  <CATPATH>{$catPath}</CATPATH>
  <PRODESC>{strip_tags($row->prodesc)}</PRODESC>
  <PROACCESS_WEB2>{$row->proaccess_web2}</PROACCESS_WEB2>
  <PROACCESS_WEB3>{$row->proaccess_web3}</PROACCESS_WEB3>
  <PROSTATUS>{$row->prostatus}</PROSTATUS>
  {if isset($stores[$row->proid])}
    {foreach $stores[$row->proid] as $stoid => $sto}
  <STOQTY_{$stoid}>{$sto->pqsqty}</STOQTY_{$stoid}>
    {/foreach}
  {/if}
</ITEM>
{/foreach}
</SHOP>