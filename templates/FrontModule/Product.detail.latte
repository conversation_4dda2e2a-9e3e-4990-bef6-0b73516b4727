{varType App\classes\Image[] $images}
{varType App\Orm\Catalog $catalog}
{varType App\Orm\Product $productOrm}
{varType int $minServicePrice}
{varType int $maxServiceVariant}
{default $pageTitle       = empty($product->protitle) ? $product->proname : $product->protitle}
{default $pageKeywords    = $product->prokeywords}
{default $pageDescription = empty($product->prodescription) ? strip_tags($product->prodescs) : $product->prodescription}

{php
    $GLOBALS["ecommProId"] = $product->proid;
    $GLOBALS["ecommPageType"] = 'product';
    $GLOBALS["ecommTotalValue"] = $product->proprice;
}

{block ogHead}
    <meta property="og:title" content="{$pageTitle} - {$presenter->config["SERVER_NAMESHORT"]}" />
    {if !empty($pageDescription)}<meta property="og:description" itemprop="description" name="twitter:description" content="{$pageDescription|strip|truncate:160, ''}">{/if}
    <meta property="og:url" content="{plink '//this'}" />
    <meta property="og:image" content="{$baseUrl.'/' . getPicName($product, 'product/detail')}" />
{/block}

{block content}
    {* ritch snippets http://schema.org/Product *}
    {var $imgUrl = getPicName($product, 'product/detail')}
    <script type="application/ld+json">
      {
        "@context": "http://schema.org",
        "@type": "Product",
        "name": {$product->proname},
        {if !empty($imgUrl)}"image": {$baseUrl.'/' . $imgUrl},{/if}
        "description": {$pageDescription},
        "itemCondition" : "http://schema.org/NewCondition",
        "manufacturer": {$manufacturer->manname},
        "url": {plink '//this'},
        "offers": {
          "@type": "Offer",
          "availability": {if $product->proaccess == 0}"http://schema.org/InStock"{else}"http://schema.org/OutOfStock"{/if},
          "price": {$product->proprice},
          "priceCurrency": "CZK",
          "url": {plink '//this'}
        }
      }
      </script>

    <section class="section">

        <article class="container" role="article">
            <div class="breadcrumbs" role="navigation" aria-label="Drobečková navigace">

                <a n:href="Homepage:default"><i class="fas fa-home"></i><span class="sr-only">Domů</span></a>

                {if $catalog}
                    {foreach $catalog->getTree() as $oneCatalog}
                        <span class="breadcrumbs__separator" aria-hidden="true">&gt;</span>
                        <a n:href="Catalog:detail, $oneCatalog->id, $oneCatalog->getURLKey()">{$oneCatalog->catname}</a>
                    {/foreach}
                {/if}

                <span class="breadcrumbs__separator" aria-hidden="true">&gt;</span>
                {$product->proname} {if $adminLogIn}<a href="{plink :Admin:Product:edit, $product->proid}" target="admin">&#9998;</a>{/if}
            </div>

            <h1>{$product->proname}</h1>

            <div class="row row--space product-detail">

                <div class="col col--2 product-detail__col">

                    {* další obrázky *}
                    {*foreach $images as $image}
                        {if $iterator->isFirst()}
                            <div class="product__gallery">
                        {/if}
                        <a href="{$image->getPathBig()}" title="{_'Detail výrobku - kliknutím zvětšíte'}"> <img src="{$baseUrl.'/pic/'.$sid.'/product/list/'.$row["name"]}" width="{$row["w"]}"  height="{$row["h"]}" alt="{$product->proname}"></a>
                        {if $iterator->isLast()}
                            </div>
                        {/if}
                    {/foreach*}

                    {if count($images)}
                        <div class="preview">

                            <div class="preview__slider">
                                <div class="preview__image js-gallery">
                                    {foreach $images as $image}
                                        <a href="{$image->getPathBig()}" title="{_'Detail výrobku - kliknutím zvětšíte'}"> <img src="{$image->getPathDetail()}" width="570" height="500" alt="{$product->proname}"></a>
                                    {/foreach}
                                </div>

                                <div class="preview__control preview__control--prev">
                                    <i class="fas fa-chevron-left"></i>
                                </div>
                                <div class="preview__control preview__control--next">
                                    <i class="fas fa-chevron-right"></i>
                                </div>

                                {*
                                <div class="preview__badge-list">
                                  <img src="/img/from-tv.png" loading="lazy" alt="">
                                  <img src="/img/3v1.png" loading="lazy" alt="">
                                  <img src="/img/product-2019.png" loading="lazy" alt="">
                                </div>
                                *}

                            </div>

                            <div class="preview__thumbnails">
                                {foreach $images as $image}
                                    <img src="{$image->getPathList()}" alt="{$product->proname}" />
                                {/foreach}
                            </div>

                        </div>
                    {/if}

                </div>

                <div class="col col--2 product-detail__col">
                    {php
                      $isDelFree = isDelFree($product, $delFreeLimitByVendor);
                      $haveGift = haveGift($product, $catalogGifts);
                      $isDiscount = isDiscount($product,$discGifts) && isset($discGifts);

                      $haveValidDateDisc = (isset($product->prodatedisc) && $product->prodatedisc !== null && $product->prodatedisc >= date('Y-m-d'));

                      $showDiscount = $haveGift || $isDiscount;

                      $haveTag = ($haveValidDateDisc || $isDelFree || $haveGift || $product->protypid2 || $product->protypid3 || $product->protypid4 || $product->protypid5 || $product->protypid6 || $isDiscount ||  (isset($product->proorigin) && $product->proorigin == 'CZ'));
                    }

                    {if $haveTag}
                        <div class="bonus">
                            <h2>Výhody k&nbsp;nákupu!</h2>
                            {include 'productTagList.latte', product:$product}
                        </div>
                    {/if}

                    {if FALSE} {* slevy vypnuty *}
                        <div class="action action--detail">
                            <div class="action-col">
                                <p>CENA DŘÍVE <br><span class="action-price">{$product->propricecom|formatPrice}{$product->propricecom|formatPriceEur:$config["EUR_RATE"]|noescape}</span></p>
                            </div>
                            <div class="action-col">
                                <p>SLEVA <br><strong>{$product->propricecom|getDiscountInPer:$product->proprice}</strong></p>
                            </div>
                        </div>
                    {/if}

                  {form basketAddForm class=>"ajax w-100"}
                  <div class="buy">
                    <div class="row">
                        <div class="col col--2">
                          {if $product->proprice==0}
                            <p>Zboží momentálně nelze zakoupit. Prosím kontaktujte nás.</p>
                          {elseif !isSalable($product) || !isset($catalog)}
                            <p>Zboží momentálně nelze zakoupit.</p>
                          {elseif $product->prostatus==0}
                            <h2 class="buy__price">{$product->proprice|formatPrice}{$product->proprice|formatPriceEur:$config["EUR_RATE"]|noescape} <small>{$product|formatPriceNoVat}</small></h2>
                            <span class="buy__control">
                              <span class="form-count ">
                                <div class="form-count__control">
                                  <div class="form-count__plus">&plus;</div>
                                  <div class="form-count__minus">&minus;</div>
                                </div>
                                {label qty class=>"sr-only" /}
                                {input qty min=>"0", max=>"999"}
                              </span>
                              <button type="submit" class="btn btn--lg"><i class="fas fa-shopping-basket"></i> Koupit</button>
                            </span>
                          {/if}
                        </div>

                        <div class="col col--2">
                            {if $product->proprice >= 3000}
                                <h2 class="buy__price">{($product->proprice/10)|formatPrice}<small>1/10, 10% akontace</small></h2>
                                <a href="{$cofidisCalcUrl}" class="btn btn--lg js-modal-open" target="iPlatba"><i class="fas fa-calculator"></i> Spočítat splátky</a>
                            {/if}
                        </div>
                    </div>
                  </div>


                  {* není skladem *}
                  {if $storesQty}
                    {* stav skladu po prodejnách *}
                    {foreach $storesQty as $stoQty}
                    <div class="stock stock--detail ">
                        Skladem {$stoQty->stoname} {$stoQty->pqsqty|getQtyLimited} ks
                        {php
                            $storeInfoKey = "storeinfo_0" . "_" . $product->provenid;
                            if (empty($textBlocks[$storeInfoKey]->pagbody)) {
                              $storeInfoKey = "storeinfo_0";
                            }
                        }
                        {if !empty($textBlocks[$storeInfoKey]->pagbody)}
                            {include 'toolTip.latte', tTitle:$textBlocks[$storeInfoKey]->pagtitle, tText:$textBlocks[$storeInfoKey]->pagbody}
                        {/if}
                    </div>
                    {/foreach}

                  {elseif $product->proaccess > 0}
                    <div class="stock stock--detail {if $product->proqty_atc +$product->proqty_web3 +$product->proqty_web2 +$product->proqty <= 0 && $product->proaccess>99}stock--unavailable{/if}">
                      {if $product->proqty_atc +$product->proqty_web3 +$product->proqty_web2 +$product->proqty <= 0 && $product->proaccess>99}<i class="fas fa-times"></i>{else} <i class="fas fa-check"></i>{/if} <strong>{$product|getProAccessText,$enum_proaccess,TRUE}</strong>
                      {php
                      $storeInfoKey = "storeinfo_" . $product->proaccess . "_" . $product->provenid;
                      if (empty($textBlocks[$storeInfoKey]->pagbody)) {
                      $storeInfoKey = "storeinfo_" . $product->proaccess;
                      }
                      }
                      {if !empty($textBlocks[$storeInfoKey]->pagbody)}
                          {include 'toolTip.latte', tTitle:$textBlocks[$storeInfoKey]->pagtitle, tText:$textBlocks[$storeInfoKey]->pagbody}
                      {/if}
                    </div>

                  {/if}

                  {* stav skladu u dodavatele *}
                  {if $product->proqty_atc > 0 && $product->proaccess == 0 && $product->provenid == ""}
                  <div class="stock stock--detail">
                      <i class="fas fa-check"></i> {$product|getProAccessVendorText}
                          {php
                          $storeInfoKey = "storeinfo_1" . "_" . $product->provenid;
                          if (empty($textBlocks[$storeInfoKey]->pagbody)) {
                          $storeInfoKey = "storeinfo_1";
                          }
                          }
                      {if !empty($textBlocks[$storeInfoKey]->pagbody)}
                          {include 'toolTip.latte', tTitle:$textBlocks[$storeInfoKey]->pagtitle, tText:$textBlocks[$storeInfoKey]->pagbody}
                      {/if}
                  </div>
                  {/if}

                  {ifset $form[ordgifts]}
                  <div class="gift">
                    <h3 id="gifts">Vyberte si dárek</h3>
                    <div class="additional">
                      <i class="fas fa-gift"></i>

                      {foreach $form[ordgifts]->items as $key => $label}
                      <p class="form-radio">
                        <input n:name="ordgifts:$key">
                        <span class="form-radio__box"></span>
                        <label class="form-label" n:name="ordgifts:$key">{$label}</label>
                      </p>
                      {/foreach}

                    </div>
                  </div>
                  {/ifset}



                  <div class="properties">
                      <ul>
                        {if !empty($enum_proused[$product->proused])}
                            <li>Stav zboží:
                              <strong>{$enum_proused[$product->proused]|upper}
                              {php
                                if (!empty($textBlocks["usedinfo_".$product->proused."_".$product->prospecmode])) {
                                  $usedInfo = "usedinfo_".$product->proused."_".$product->prospecmode;
                                } else {
                                  $usedInfo = "usedinfo_".$product->proused;
                                }
                              }

                              {if !empty($textBlocks[$usedInfo])}
                                {include 'toolTip.latte', tTitle:'Další informace', tText:$textBlocks[$usedInfo]->pagbody}
                              {/if}
                              </strong>
                            </li>
                        {/if}
                        {if !empty($product->prowarranty)}<li>Záruka: <strong>{$product->prowarranty|formatWarranty}</strong></li>{/if}
                        <li>Kód: <strong>{$product->procode}</strong></li>
                          <li n:if="$product->procode2">EAN: <strong>{$product->procode2}</strong></li>
                          <li n:if="$productOrm->manufacturer">Výrobce: <strong>{$productOrm->manufacturer->name}</strong></li>
                      </ul>
                  </div>

                    <div class="row extras" >
                        <div class="col col--2" n:if=" isset($form['ins_1']) || isset($form['ins_2'])">
                            <a href="#modal-insurance" class="extras__btn js-modal-open">
                              <span class="icon icon--insurance-linear">
                                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                  <use xlink:href="{$baseUrl}/style/{$serverId}/img/icons.svg#insurance-linear" x="0" y="0" width="100%" height="100%"></use>
                                </svg>
                              </span>
                              <u>Pojištění produktu</u> <small>až na {$maxServiceVariant} roky od {$minServicePrice} Kč</small>
                            </a>
                        </div>
                        <div class="col col--2" n:ifset="$form['ordservices']">
                            <a href="#modal-gift-package" class="extras__btn js-modal-open">
                            <span class="icon icon--ribbon">
                              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <use xlink:href="{$baseUrl}/style/{$serverId}/img/icons.svg#ribbon" x="0" y="0" width="100%" height="100%"></use>
                              </svg>
                            </span>
                            <u>Dárkové balení</u> <small>{if count($ordservices)>1}od {/if}{$ordservices[1]?->orsprice|formatPrice}</small>
                            </a>
                        </div>
                    </div>

                    <div class="modal modal--wide" id="modal-insurance" role="dialog"
                             aria-labelledby="dialog-title-6">

                            <div class="modal__body">

                            <span class="modal__close js-modal-close">
                              <span class="icon icon--times-solid">
                                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                  <use xlink:href="/img/icons.svg#times-solid" x="0" y="0" width="100%"
                                       height="100%"></use>
                                </svg>
                              </span>
                            </span>

                                <div class="modal__content">

                                    <h2 class="center" id="dialog-title-6">Pojištění produktu</h2>

                                    <div class="row insurance">
                                        <div class="col col--2" n:ifset="$form['ins_2']">
                                            <h3>Pojištění před poškozením a odcizením</h3>
                                            <div class="additional">
                                                <i class="fas fa-user-check"></i>
                                                {foreach $form['ins_2']->items as $key => $label}
                                                    <p class="form-radio">
                                                        <input n:name="ins_2:$key">
                                                        <span class="form-radio__box"></span>
                                                        <label n:name="ins_2:$key" class="form-label"> {$label}</label>
                                                    </p>
                                                {/foreach}


                                                <br>

                                                <p><a href="{$baseUrl}/text-pojisteni-pred-poskozenim-a-odcizenim" target="_blank">Více
                                                        informací zde</a></p>
                                            </div>
                                        </div>

                                        <div class="col col--2" n:ifset="$form['ins_1']">
                                            <h3>Prodloužení záruky</h3>
                                            <div class="additional">
                                                <i class="fas fa-ribbon"></i>


                                                {foreach $form['ins_1']->items as $key => $label}
                                                    <p class="form-radio">
                                                        <input n:name="ins_1:$key">
                                                        <span class="form-radio__box"></span>
                                                        <label n:name="ins_1:$key" class="form-label"> {$label}</label>
                                                    </p>
                                                {/foreach}


                                                <p><a href="{$baseUrl}/text-prodlouzena-zaruka" target="_blank">Více informací zde</a>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <p class="modal__control">
                                    <span class="btn btn--txt js-modal-close"><i class="fas fa-caret-left"></i>Zpět do
                                        obchodu
                                    </span>
                                    <span class="btn js-modal-close " ><i class="fas fa-save"></i>Uložit</span>
                                </p>

                            </div>

                        </div>



                        <div class="modal" id="modal-gift-package" role="dialog" aria-labelledby="dialog-title-7">

                            <div class="modal__body">

                            <span class="modal__close js-modal-close">
                              <span class="icon icon--times-solid">
                                <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                  <use xlink:href="/img/icons.svg#times-solid" x="0" y="0" width="100%" height="100%"></use>
                                </svg>
                              </span>
                            </span>

                                <div class="modal__content">

                                    <h2 id="dialog-title-7">Dárkové balení</h2>

                                    <div class="additional">
                                        <i class="fas fa-gifts"></i>
                                        {foreach $ordservices as $orsItem}
                                            {continueIf !isset($form[ordservices][$orsItem->orsid])}
                                            {var $input=$form[ordservices][$orsItem->orsid]}
                                            <p class="form-checkbox">
                                                <input n:name="$input">
                                                <span class="form-checkbox__box"></span>
                                                <label class="form-label" n:name="$input">{$orsItem->orsname} za příplatek {$orsItem->orsprice|formatPrice}</label>

                                                {$orsItem->orsdescription}
                                            </p>
                                        {/foreach}

                                    </div>

                                    <p class="modal__control">
                                        <span class="btn btn--txt js-modal-close"><i class="fas fa-caret-left"></i>Zpět do obchodu</span>
                                        <span class="btn js-modal-close"><i class="fas fa-save"></i>Uložit</span>
                                    </p>

                                </div>

                            </div>

                        </div>

                    {/form}

                  {if $productOrm->needHelpContact()}

                        <div class="contactBox">
                           <div class="m-auto" style="width: fit-content;">
                               <i class="far fa-question-circle"></i>
                               <span>Máte otázku na produkt?<br> Zavolejte nám! <strong>+420 603 818 482</strong></span>
                           </div>
                        </div>
                    {/if}


                    <div class="controls">
                        <a href="{plink compareAdd $product->proid}" class="btn btn--secondary btn--sm"><i class="fas fa-cog"></i> Porovnat</a>
                        <a href="#" class="btn btn--secondary btn--sm" onclick="window.print();"><i class="fas fa-print"></i> Vytisknout</a>
                        {if !empty($manual)}
                          {if $manual->isfile == 1}
                            <a href="{$baseUrl}/files/{$manual->atafilename}" class="btn btn--secondary btn--sm" target="_blank">{$manual->ataname}</a>
                          {else}
                            <a href="{plink getAttta, $manual->ataid}" class="btn btn--secondary btn--sm" target="_blank">{$manual->ataname}</a>
                          {/if}<br>
                        {/if}
                        {if $product->proaccess > 0}<a href="{plink User:watchDog, $product->proid}" class="btn btn--secondary btn--sm"><i class="fas fa-dog"></i> {if $watchDog}Vymazat hlídání{else}Hlídat{/if}</a>{/if}
                    </div>

                </div>

            </div>

            <div class="tabs">
                <div class="tabs__nav">
                    {php
                    $tab = (string)$presenter->getParameter('tab');
                    if ($tab == "" && $serverId != 'web1' && count($accessories) > 0) $tab = 'recommend';
                    }
                    <a href="#description" class="btn js-tab-open{if $tab == ''} is-open{/if}">Popis produktu</a>
                    <a href="#technical" class="btn js-tab-open{if $tab == 'technical'} is-open{/if}">Technické parametry</a>
                    <a href="#recommend" class="btn js-tab-open{if $tab == 'recommend'} is-open{/if}">Doporučujeme</a>
                    {if !empty($attachments)}<a href="#atta" class="btn js-tab-open{if $tab == 'atta'} is-open{/if}"><i class="fas fa-save"></i> Návod nebo jiné přílohy</a>{/if}
                    {if $showDiscount}<a href="#gift" class="btn js-tab-open{if $tab == 'gift'} is-open{/if}"><i class="fas fa-gift"></i> Dárek k produktu</a>{/if}
                </div>

                <div class="tabs__tab {if $tab != ''}is-hidden{/if}" id="description">
                    <h2>Popis</h2>
                    {* popis z kategorii *}
                    {*
                    {ifset $catalogPath}
                        {foreach $catalogPath as $key => $catPI}
                            {if !empty($catPI->catdesc)}{$catPI->catdesc|noescape}{/if}
                        {/foreach}
                    {/ifset}
                    *}

                    {if!empty($catDesc)}
                    {$catDesc|noescape}
                    {/if}

                    {$product->prodesc|noescape}

                    {if $product->provenid==='bal' && (!empty($product->prodesc2) || !empty($product->prodesc3))}
                        <h3>Bezpečnostní upozornění</h3>
                        {if !empty($product->prodesc3)}
                           {var $arr = explode(",", $product->prodesc3)}

                            {foreach $arr as $code}
                                {if $iterator->first}
                                    <div>
                                {/if}
                                <img src="{$baseUrl}/img/icons/ghs/{$code}.png" width="100" alt="{$code}"><br>{$enum_proghs[$code]}<br>
                                {if $iterator->last}
                                    </div>
                                    <br><br>
                                {/if}
                            {/foreach}
                        {/if}

                        {$product->prodesc2|noescape}
                    {/if}

                    {* youtube video *}
                    {if !empty($product->provideo)}
                        <div id="video"> <h4>Prohlédněte si video</h4><div><iframe width="560" height="315" src="https://www.youtube.com/embed/{$product->provideo}"  allowfullscreen></iframe></div></div>
                    {/if}

                </div>

                {if count($proParams) > 0}
                    <div class="tabs__tab is-hidden" id="technical">
                        <h2>Technické parametry</h2>
                        {foreach $proParams as $row}
                            {if $iterator->isFirst()}
                                <table>
                            {/if}
                            <tr {if $iterator->isEven()} class="even"{/if}>
                                <th>{$row->prpname}:</th>
                                {if !empty($row->prpunit)}
                                    <td>{$row->prpvalue|getUnits:$row->prpunit}</td>
                                {else}
                                    <td>{$row->prpvalue}</td>
                                {/if}
                            </tr>
                            {if $iterator->isLast()}
                                </table>
                            {/if}
                        {/foreach}
                    </div>
                {/if}

                {* vypis prislusenstvi *}
                {foreach $accessories as $row}
                    {if $iterator->isFirst()}
                        <div class="tabs__tab" id="recommend">
                        <div class="row row--start">
                    {/if}
                    <div class="col col--4">
                        {include 'productItem.latte', product:$row}
                    </div>
                    {if $iterator->isLast()}
                        </div>
                        </div>
                    {/if}
                {/foreach}

                {php $arr = []; $proprice=0;}
                {if $product->progiftdisc > 0 || !empty($proGiftsList)}
                <div class="tabs__tab tabs__tab--gift is-hidden" id="gift">
                    <div class="row row--start">
                        {if !empty($proGiftsList)}
                        <div class="col col--2 col--center gift-box">
                        <a href="#gifts" class="gift-box__link">
                        {foreach $proGiftsList as $row}
                                  <img src="{$baseUrl}/{getPicName($row, 'product/list')}" alt="{$row->proname}">
                            {php
                              $arr[] = $row->proname;
                              $proprice += $row->proprice;
                            }
                        {/foreach}
                        </a>

                        <a n:href="Product:detail $row->proid, getProKey($row)" class="form-radio">
                          <label class="form-label" for="giftproduct-secondary2">K objednávce zdarma {implode(", ", $arr)}{if $proprice > 0} v hodnotě {$proprice|formatPrice}{/if}</label>
                        </a>

                        </div>
                        {/if}
                      {if $product->progiftdisc > 0}
                        {if !empty($proGiftsList)}
                        <div class="col col--4 col--middle">
                            <p><strong>nebo</strong></p>
                        </div>
                        {/if}
                        <div class="col col--4 col--center gift-box">
                            <a href="#gifts" class="gift-box__link">
                                <img src="{$baseUrl}/style/{$serverId}/img/sleva.png" alt="sleva">
                            </a>
                            <p class="form-radio">
                                <label class="form-label" for="giftproduct-secondary">Sleva {$productOrm->getDiscountValue()} {$productOrm->getDiscountUnit()} {if ($productOrm->getDiscountUnit() === "%")} (-{$productOrm->getDiscount()|formatPrice}){/if} <br> <strong>Okamžitá sleva bude uplatněna po vložení zboží do košíku.</strong></label>
                            </p>
                        </div>
                      {/if}
                    </div>
                </div>

                {/if}

                {if count($attachments) > 0}
                    <div class="tabs__tab is-hidden" id="atta">
                        <h2>Návod nebo jiné přílohy</h2>
                        {foreach $attachments as $row}
                            {if $iterator->isFirst()}
                                <table>
                            {/if}
                            <tr {if $iterator->isEven()} class="even"{/if}>
                                <td>
                                  {if $row->isfile == 1}
                                    <a href="{$baseUrl}/files/{$row->atafilename}" target="_blank">{$row->ataname}</a>
                                  {else}
                                    <a href="{plink getAttta, $row->ataid}" target="_blank">{$row->ataname}</a>
                                  {/if}
                                </td>
                            </tr>
                            {if $iterator->isLast()}
                                </table>
                            {/if}
                        {/foreach}
                    </div>
                {/if}
            </div>

            <hr>


            {include 'banners.latte'}

      {include 'actions.latte'}

      {include 'favourites.latte', catalogs:$catalogFavourites}

        </article>

    </section>

    <!-- Heureka.cz PRODUCT DETAIL script -->
        <script>
            (function(t, r, a, c, k, i, n, g) {
                t['ROIDataObject'] = k;
                t[k] = t[k] || function() {
                    (t[k].q = t[k].q || []).push(arguments)
                },
                    t[k].c = i;
                n = r.createElement(a),
                    g = r.getElementsByTagName(a)[0];
                n.async = 1;
                n.src = c;
                g.parentNode.insertBefore(n, g)
            })(window, document, 'script', '//www.heureka.cz/ocm/sdk.js?version=2&page=product_detail', 'heureka', 'cz');
        </script>
    <!-- End Heureka.cz PRODUCT DETAIL script -->
{/block}
