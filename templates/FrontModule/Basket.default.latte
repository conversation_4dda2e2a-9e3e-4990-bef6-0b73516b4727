{varType App\FrontModule\Presenters\BasketPresenter $presenter}
{varType string $baseUrl}

{varType string $serverId}

{varType array $onRoad}

{varType App\Orm\Basket $basketOrm}


{varType array $nextDelsFree}
{varType array $enum_proaccess}
{varType array $accessories}


{default $pageTitle       = $presenter->translator->translate('Košík')}
{default $pageRobots      = "nofollow,noindex"}

{block content}
    {if $presenter->getParameter('ok') != 1}

        {include 'basketProgress.latte'}

        <section class="section section--cart" n:snippet="basket">

            <article class="container container--md" role="article">

                <h1 class="cart-headline">Obsah nákupního košíku</h1>

                {if count($basketOrm->basketItems) > 0}

                    {*if $basketOrm->isOnlySale() && $serverId === 'web3'}
                        <div class="flash">Máte v košíku POUZE zboží s př<PERSON>znakem VÝPRODEJ, možno vyzvednout na na našich prodejnách. Chcete-li zaslat na adresu, nutno doobjednat jakékoli další zboží bez příznaku VÝPRODEJ</div>
                    {/if*}

                    {form basketForm}
                        {foreach $form->getErrors() as $error}
                                    <p>{php dump($error)}</p>
                        {/foreach}

                        <div class="cart-products is-editable">

                            <ul aria-hidden="true">
                                <li class="cart-product__top-name">Produkt</li>
                                <li class="cart-products__top-amount">Počet kusů</li>
                                <li class="cart-products__top-stock">Dostupnost</li>
                                <li class="cart-products__top-price">Cena za kus</li>
                                <li class="cart-products__top-price-total">Celková cena</li>
                            </ul>

                            <ol>
                                {var $isNotOnStock = false}
                                {var $isDelFreeBlocked = false}
                                {foreach $basketOrm->basketItems as $item}

                                    {var $proqtyonroad =0}
                                    {ifset $onRoad[$item->product->id]}
                                        {var $proqtyonroad = (int)$onRoad[$item->product->id]->qty}
                                    {/ifset}

                                       {*php
                                        if ($productRows[$id]->proaccess >= 100) {
                                        $isNotOnStock = TRUE;
                                        }


                                        $giftdisc = 0;
                                        if (isset($basket->ordgifts[$id]["discount"]) && $basket->ordgifts[$id]["discount"]) {
                                        $giftdisc = $productRows[$id]->progiftdisc;

                                        }



                                   *}

                                    <li>
                                        <dl>
                                            <dt class="sr-only">Produkt</dt>
                                            <dd class="cart-products__overview">
                                                <img src="{$baseUrl}/{$item->product->getImage()->getPathList()}" alt="{$item->product->name}">
                                                <a href="{plink Product:detail, $item->product->id, $item->product->getUrlKey()}"><strong>{$item->product->name}</strong></a>
                                                {if $item->product->deliveryFree == App\Orm\Product::DELIVERYFREE_BLOCK}
                                                    <br>
                                                    <strong style="color: red">POZOR! Nezapočítává se do dopravy zdarma.</strong>
                                                    {var $isDelFreeBlocked = true}
                                                {/if}
                                            </dd>

                                            <dt class="sr-only">Počet kusů</dt>
                                            <dd class="cart-products__count">

                                              <span class="form-count">
                                                <div class="form-count__control">
                                                  <div class="form-count__plus">&plus;</div>
                                                  <div class="form-count__minus">&minus;</div>
                                                </div>
                                                <label class="sr-only" for="name">Množství</label>
                                                  {php echo $form['count_'.$item->product->id]->getControl() }
                                              </span>

                                            </dd>

                                            <dt class="sr-only">Dostupnost</dt>
                                            <dd class="cart-products__stock">
                                                {if $item->product->getQuantity() > 0 && $proqtyonroad > 0} Skladem {$item->product->getQuantity()|getUnits:'kus'}<br>(na cestě {$proqtyonroad|getUnits:'kus'})
                                                {elseif $item->product->getQuantity() > 0} Skladem {$item->product->getQuantity()|getUnits:'kus'}
                                                {elseif $proqtyonroad > 0} Na cestě {$proqtyonroad|getUnits:'kus'}
                                                {else}
                                                    {$item->product->getAccessText(enum_proaccess:$enum_proaccess)}
                                                {/if}
                                            </dd>

                                            <dt class="sr-only">Celková cena</dt>
                                            <dd class="cart-products__price-total">
                                                <strong>{$item->getProductPrice(true)|formatPrice}</strong>
                                                {if $item->giftType==App\Orm\BasketItem::GIFT_DISCOUNT}<br>-{$item->product->getDiscount()|formatPrice}{/if}
                                            </dd>

                                            <dt class="sr-only">Celková cena</dt>
                                            <dd class="cart-products__price">
                                                <strong>{$item->getProductPrice()|formatPrice}</strong>
                                                {if $item->giftType==App\Orm\BasketItem::GIFT_DISCOUNT}<br>-{$item->product->getDiscount() * $item->qty|formatPrice}{/if}
                                            </dd>

                                            <span class="cart-products__remove">
                                              <a href="{plink Basket:delete, $item->product->id}" onclick="return DeleteConfirmFront('{_'Opravdu chcete smazat položku'} {$item->product->name}');" title="{_'Odstranit z nákupního košíku'}" title="Odebrat produkt">&times;</a>
                                            </span>
                                        </dl>

                                        {* dárky *}
                                        {if isset($form[ordgifts][$item->product->id])}
                                            <div class="cart-products__additional" n:formContainer="ordgifts">
                                                <div class="gift" n:formContainer="$item->product->id">
                                                    <h3>Dárek/y k produktu</h3>
                                                    <div class="additional">
                                                        <i class="fas fa-gift"></i>
                                                        {foreach $form[ordgifts][$item->product->id][ordgifts]->items as $key => $label}
                                                            <p class="form-radio">
                                                                <input n:name="ordgifts:$key" class="ordgifts" data-item="{$item->id}">
                                                                <span class="form-radio__box"></span>
                                                                <label class="form-label" n:name="ordgifts:$key">{$label}</label>
                                                            </p>
                                                        {/foreach}
                                                    </div>
                                                </div>
                                            </div>
                                        {/if}

                                        {* pojištění *}
                                        {if isset($form['ins2_'.$item->product->id]) || isset($form['ins1_'.$item->product->id])}
                                            <div class="cart-products__additional">
                                                <div class="row insurance">
                                                    {ifset $form['ins2_'.$item->product->id]}
                                                        <div class="col col--2">
                                                            <h3>Pojištění před poškozením a odcizením</h3>
                                                            <div class="additional">
                                                                <i class="fas fa-user-check"></i>
                                                                {foreach $form['ins2_'.$item->product->id]->items as $key => $label}
                                                                    <p class="form-radio">
                                                                        <input n:name="'ins2_'.$item->product->id:$key"  class="guaranteeSelect" data-item="{$item->id}">
                                                                        <span class="form-radio__box"></span>
                                                                        <label n:name="'ins2_'.$item->product->id:$key" class="form-label"> {$label}</label>
                                                                    </p>
                                                                {/foreach}



                                                                <br>

                                                                <p><a href="{$baseUrl}/text-pojisteni-pred-poskozenim-a-odcizenim" target="_blank">Více informací</a></p>
                                                            </div>
                                                        </div>
                                                    {/ifset}
                                                    {ifset $form['ins1_'.$item->product->id]}
                                                        <div class="col col--2">
                                                            <h3>Prodloužení záruky</h3>
                                                            <div class="additional">
                                                                <i class="fas fa-ribbon"></i>
                                                                {foreach $form['ins1_'.$item->product->id]->items as $key => $label}
                                                                    <p class="form-radio">
                                                                        <input n:name="'ins1_'.$item->product->id:$key" class="insuranceSelect" data-item="{$item->id}">
                                                                        <span class="form-radio__box"></span>
                                                                        <label n:name="'ins1_'.$item->product->id:$key" class="form-label"> {$label}</label>
                                                                    </p>
                                                                {/foreach}

                                                                <br>

                                                                <p><a href="{$baseUrl}/text-prodlouzena-zaruka" target="_blank">Více informací</a></p>
                                                            </div>
                                                        </div>
                                                    {/ifset}
                                                </div>
                                            </div>
                                        {/if}

                                        {* dárkove baleni *}
                                        {if isset($form['ordservices'][$item->product->id]) && $item->product->getOrderService()}
                                            <div class="cart-products__additional" n:formContainer="ordservices">
                                                <div class="gift" n:formContainer="$item->product->id">
                                                    <h3>Dárkové balení</h3>
                                                    <div class="additional">
                                                        <i class="fas fa-gifts"></i>

                                                            {var $input=$form[ordservices][$item->product->id][$item->product->getOrderService()->id]}
                                                            <p class="form-checkbox">
                                                                <input n:name="$input" class="ordservices" data-item="{$item->id}">
                                                                <span class="form-checkbox__box"></span>
                                                                <label class="form-label" n:name="$input">{$item->product->getOrderService()->name} za příplatek {$item->product->getOrderService()->price|formatPrice}</label>

                                                                {$item->product->getOrderService()->description}
                                                            </p>

                                                    </div>
                                                </div>
                                            </div>

                                        {/if}


                                        {* dodatkové služby *}






                                    </li>

                                {/foreach}
                            </ol>

                        </div>

                        {*if $nextDisc}
                            <div class="alert alert--info">
                                <p >Pokud ještě objednáte za <strong>{$nextDisc->diff|formatPrice} {$nextDisc->diff|formatPriceEur:$config["EUR_RATE"]|noescape}</strong> získáte <strong>slevu {$nextDisc->dispercent}%</strong> na celý nákup.</p>
                            </div>
                        {/if*}


                        {if $basketOrm->getweightSum() < 50}


                            <p class="free-delivery" >
                            		{if $basketOrm->gethaveDelFree()}
                            		<i class="fas fa-truck"></i>  Na tento nákup máte vybranou <strong>dopravu zdarma</strong>.
                                {elseif $basketOrm->sumPriceForDeliveryLimit() >= $delFreeLimit || $basketDelFree}
                                    <i class="fas fa-truck"></i>  Na tento nákup máte vybranou <strong>dopravu zdarma</strong>.
                                {else}
                                    Nakupte ještě za <span class="color-green">{($delFreeLimit-$basketOrm->sumPriceForDeliveryLimit())|formatPrice}
                                        {($delFreeLimit-$basketPriceSumForDelFree)|formatPriceEur:$config["EUR_RATE"]|noescape}</span>

                                    a máte vybranou dopravu <span class="color-green">ZDARMA!!</span>.
                                {/if}




                                {*if $basketOrm->gethaveDelFree()}
                                    <i class="fas fa-truck"></i> Získáváte dopravu <span class="color-green">ZDARMA!</span>
                                {elseif isset($nextDelsFree)}

                                    {foreach $nextDelsFree as $mkey => $rows}
                                        <i class="fas fa-truck"></i> Objednejte ještě za <strong>{$mkey|formatPrice}</strong> a doprava
                                        {foreach $rows as $key => $row}
                                            <strong>{$row->delname}</strong>{if !$iterator->isLast()}, {/if}
                                        {/foreach}
                                        bude <span class="color-green">ZDARMA!</span>
                                        {if !$iterator->isLast()}<br>{/if}
                                    {/foreach}

                                {/if*}

                                {if $isDelFreeBlocked}
                                    <br>
                                    <span style="color: red">POZOR! Košík obsahuje položky, které se <strong>nezapočítavají do dopravy zdarma.</strong></span>
                                {/if}
                            </p>
                        {else}

                            {if !$basketOrm->getisDelCheckOff() && !$basketOrm->getisNosreti() && !$basketOrm->getisHomelux() && !$basketOrm->getisMujperfekt()}
                            <div class="alert alert--danger">
                                <p class="basket__warning"><strong>Byl překročen max. váhový limit 50Kg pro stadnardní spedici kurýrní služby. Po uzavření objednávky Vám z naší strany indivudálně neceníme nadrozměrnou dopravu nebo můžete zvolit osobní odběr. </p>
                            </div>
                            {/if}
                        {/if}


                        <div class="cart-properties">
                          <div class="cart-properties__discount">
                            <label for="discount">Kód dárkového poukazu či slevy:</label>
                            <div class="form-group">
                              <input id="discount" type="text" value="{$basketOrm->coupon?->code}" placeholder="Zadejte kód poukazu či slevy">
                              <a href="#" class="btn" id="btnAddCoupon">OK</a>

                            </div>
                          </div>
                        </div>


                        {snippet couponInfo}
                        <div class="coupon-info" n:if="$basketOrm->coupon">
                        <h2 style="">{$basketOrm->coupon->textInfo()} <span style="font-size: 1rem;">{$basketOrm->coupon->textInfoOn()}</span></h2>
                            <dl>
                                {if $basketOrm->sumPrice()-$basketOrm->sumPrice(sale:false)==0}
                                    <dt>Sleva se nevztahuje na žadný produkt v košíku.</dt>
                                {else}
                                <dt>Sleva s DPH:</dt>
                                <dd class="cart-sumprice__vatinclude">{$basketOrm->sumPrice()-$basketOrm->sumPrice(sale:false)|formatPrice}</dd>
                                {/if}
                            </dl>
                        </div>
                        {/snippet}


                        <div class="cart-sumprice">

                            <h2>Celková cena za zboží</h2>

                            <dl>
                                {*
                                <dt>Cena bez DPH:</dt>
                                <dd class="cart-sumprice__vatless">{$basket->priceSumTotal|formatPrice}</dd>
                                *}
                                <dt>Celková cena s DPH:</dt>
                                <dd class="cart-sumprice__vatinclude">{$basketOrm->sumPrice()|formatPrice}</dd>
                            </dl>

                        </div>



                        {if $isNotOnStock}
                            <div class="alert alert--info">
                                <p class="basket__warning"><strong>Berte na vědomí, že máte v košíku zboží, které má delší dobu dodání, což může prodloužit či znemožnit realizaci objednávky, týká se konkrétního zboží. Nebudeme-li schopni nedostupné zboží zajistit, provedeme storno nebo budeme realizovat zbývající část objednávky. Děkujeme za pochopení.</strong></p>
                            </div>
                        {/if}

                        <nav class="cart-pagination" role="navigation" aria-label="Nákupní košík">
                            <p><a href="javascript:history.go(-1)" class="btn btn--txt"><i class="fas fa-caret-left"></i>Pokračovat v nákupu</a></>
                            <p>
                                {*input recalc class=>'btn btn--info'*}

                                <button type="submit" n:name="submit" class="btn" >Vybrat dopravu<i class="fas fa-caret-right"></i></button>
                            </p>
                        </nav>


                    {/form}
                {else}
                    <div class="alert alert--info">
                        <p><strong>Košík je prázdný</strong></p>
                    </div>
                {/if}

            </article>

        </section>

        <hr>

        {foreach $accessories as $row}
            {breakIf $iterator->getCounter() > 40}
            {if $iterator->first}
                <section class="section">
                <div class="container">
                <div class="product-list ">
                <h2 class="title center">Mohlo by Vás zajímat</h2>

                <div class="row row--start ">
            {/if}
            <div class="col col--5">
                {include 'productItem.latte', product:$row}
            </div>
            {if $iterator->last}
                </div>
                </div>
                </div>
                </section>
            {/if}
        {/foreach}

    {else}

    {/if}
{/block}
{block scripts}
    {include parent}
    <script>
        $(function () {
            $('body').on('change', '.countInput', function () {

                naja.makeRequest('POST', {link basketChangeCount!}, { productid : $(this) . data("productid"), count: $(this).val()},  { history:false });
            });
            $('body').on('click', '#btnAddCoupon', function () {

                naja.makeRequest(
                        'POST',
                        {link addCoupon!},
                        { code : $("#discount").val()},
                        { history:false }
                );
            });
            $('body').on('change', '.ordservices', function () {
                var val = $(this).val();
                if ($(this).attr('type') == 'checkbox') {
                    val = $(this).is(":checked") ? 1 : 0;
                }
                naja.makeRequest('POST', {link basketChangeOrdservices!}, { itemid : $(this).data("item"), val: val}, { history:false });
            });

            $('body').on('change', '.guaranteeSelect', function () {
                var val = $(this).val();
                if ($(this).attr('type') == 'checkbox') {
                    val = $(this).is(":checked") ? 1 : 0;
                }
                naja.makeRequest('POST', {link basketChangeGuarantee!}, { itemid : $(this).data("item"), val: val}, { history:false });
            });

            $('body').on('change', '.insuranceSelect', function () {
                var val = $(this).val();
                if ($(this).attr('type') == 'checkbox') {
                    val = $(this).is(":checked") ? 1 : 0;
                }
                naja.makeRequest('POST', {link basketChangeInsurance!}, { itemid : $(this).data("item"), val: val}, { history:false });
            });

            $('body').on('change', '.ordgifts', function () {
                var val = $(this).val();
                if ($(this).attr('type') == 'checkbox') {
                    val = $(this).is(":checked") ? $(this).data("type") : 'gifts';
                }
                naja.makeRequest('POST', {link basketChangeGifts!}, { itemid : $(this).data("item"), val: val}, { history:false });
            });



        });

    </script>
{/block}
