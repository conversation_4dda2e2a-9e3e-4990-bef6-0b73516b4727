{varType App\Orm\Catalog[] $catalogs}
{varType App\Orm\Catalog $rootCatalog}
<div class="menu-detail">
    <div class="row row--start">
        {* první dvě kategorie *}
    {for $i = 0; $i < 3; $i++}
            {ifset $catalogs[$i]}
                <div class="col col--4">
                    <a n:href="Catalog:detail, $catalogs[$i]->id, $catalogs[$i]->getURLKey()" class="menu-detail-name">
                        {if isset($catalogs[$i]->menuImage)}
                            <img src="{$baseUrl}/{getPicName($catalogs[$i], 'catalog/list')}" alt="{$catalogs[$i]->catname}">
                        {else}
                            <span class="img"></span>
                        {/if}
                        {$catalogs[$i]->catname}
                    </a>
                    {if $catalogs[$i]->catalogs}
                        <ul class="menu-detail__list">
                            {foreach $catalogs[$i]->catalogs as $subCatalog}
                                {if $iterator->getCounter() <= 14}
                                    <li> <a n:href="Catalog:detail, $subCatalog->id, $subCatalog->getURLKey()">{$subCatalog->catname}</a></li>
                                {elseif $iterator->getCounter() == 15}
                                    <li>  <a class="menu-detail__link" n:href="Catalog:detail, $catalogs[$i]->id, $catalogs[$i]->getURLKey()">Další kategorie</a></li>
                                {/if}
                            {/foreach}
                        </ul>
                    {/if}
                </div>
            {/ifset}
        {/for}

    {* ostatní kategorie *}
    {if count($catalogs) > 3}
            <div class="col col--4">
                {for $i = 2; $i < 9; $i++}
                    {ifset $catalogs[$i]}
                        <a n:href="Catalog:detail, $catalogs[$i]->id, $catalogs[$i]->getURLKey()" class="menu-detail-name">
                            {if isset($catalogs[$i]->menuImage)}
                                <img n:if="false" src="{$baseUrl}/{getPicName($catalogs[$i], 'catalog/list')}" alt="{$catalogs[$i]->catname}">
                            {else}
                                <span class="img"></span>
                            {/if}
                            {$catalogs[$i]->catname}
                        </a>
                    {/ifset}
                {/for}
      {if count($catalogs) > 9}
                    <a class="menu-detail__link" n:href="Catalog:detail, $rootCatalog->id, $rootCatalog->getURLKey()">Další kategorie</a>
                {/if}
            </div>
        {/if}

        <div class="col col--4" n:if="false">
            <a href="#">
                <!--<img src="https://via.placeholder.com/265x370" alt="">--><img src="/img/menu-banner.png" alt=""></a>
        </div>
    </div>
    <div class="row row--start" n:if="false">
        <div class="col">
            <ul class="menu-detail__list-brands">
                <li><a href="#"><img src="/img/logo-aeg.png" alt=""></a></li>
                <li><a href="#"><img src="/img/logo-aeg.png" alt=""></a></li>
                <li><a href="#"><img src="/img/logo-aeg.png" alt=""></a></li>
                <li><a href="#"><img src="/img/logo-aeg.png" alt=""></a></li>
            </ul>
        </div>
    </div>
</div>
