{* nastaveni promennych *}
{default $pageTitle       = $presenter->translator->translate('Objednávka byla o<PERSON>')}
{default $pageRobots      = "nofollow,noindex"}

{php
  $GLOBALS["ecommProId"] = $order->proids;
  $GLOBALS["ecommPageType"] = 'purchase';
  $GLOBALS["ecommTotalValue"] = $order->ordpriceinclvat;
}

{block #content}
{if $blockOrderAccepted}
 <div id="accepted">
  <h1>{$blockOrderAccepted->pagname}</h1>
  {$blockOrderAccepted->pagbody|noescape}
  </div>
{/if}

{if $payment->delcode == 'creditcard' && $order->ordpaystatus === 0 &&  $delivery->delcode != 'OSOBNE'}
  <div class="cofidis1">
  <h3 style="color: red"><PERSON><PERSON><PERSON><PERSON> jste platbu platební kartou.</h3>

  <form action="{plink //:Front:Basket:startFioPayment, $order->ordid, substr(md5($order->ordid . $order->ordcode), 0, 6)}" method="get" id="fioForm" name="fioForm" target="_blank">
  <input type="hidden" name="key" value="{substr(md5($order->ordid . $order->ordcode), 0, 6)}">
  <p class="submit" id="final"><input class="button" type="submit" name="submit" id="submitFioForm" value="Zaplatit kartou" /></p>
  </form>
  <p>Za chvíli budete přesměrování na platební bránu automaticky.</p>

    <script type="text/javascript">
    // automaticke odeslání formuláře
      setTimeout(function(){
        $('#submitFioForm').trigger('click');
      }, 3000);
    </script>
  </div>
{/if}

{if $payment->delcode == 'cetelem'}
<div class="cetelem1">
<div id="cetelem1"></div>
<form action="{$cetelem->conf["url"].$cetelem->conf["urlreqs"]}" method="post" id="cetelemForm">
<input type="hidden" name="kodProdejce" value="{$cetelem->kodProdejce}" />
<input type="hidden" name="cenaZbozi" value="{(int)$cetelem->cenaZbozi}" />
<input type="hidden" name="calc" value="1" />
<input type="hidden" name="url_back_ok" value="{plink Cetelem:backOk}" />
<input type="hidden" name="url_back_ko" value="{plink Cetelem:backKo}" />
<input type="hidden" name="obj" value="{$order->ordcode}" />
<input type="hidden" name="numklient" value="{$order->ordcode}_{$order->ordusrid}" />
<p class="submit"><input class="button" type="submit" name="submit" value="Zažádat o splátky" /></p>
</form>
</div>
<script type="text/javascript">
$(document).ready(function(){
    $('#cetelemForm').submit();
});
</script>
{/if}
{if $payment->delcode == 'cofidis'}
<div class="cofidis1">
<div id="cofidis1"></div>
<form action="{$cofidisUrl}" method="get">
<p class="submit"><input class="button" type="submit" name="submit" value="Zažádat o splátky" /></p>
</form>
</div>
{/if}

{if !empty($qrPlatbaFio)}
  <br />
  <h3>Zvolili jste platbu <strong>převodem na náš účet</strong>.</h3>
  <strong>QR platební kód pro Vaši mobilní banku:</strong><br />
  {if !empty($qrPlatbaFio)}
    <strong>platba na náš účet ve FIO bance:</strong><br />
    <img src="{$qrPlatbaFio}" title="QR Platba FIO"  width="150px" height="150px" /><br>
  {/if}
{/if}

{if $payment->delcode == 'paybefore'}
<br>
<strong>Platební údaje pro platbu předem převodem:</strong><br />
{if $delivery->delcouid === 1}
Číslo účtu:<br>
FIO banka {$fioAccountNo}<br />
{else}
{$presenter->config["SERVER_ACCNAME"]}<br />
IBAN: {$presenter->config["SERVER_ACCIBAN"]}<br />
SWIFT: {$presenter->config["SERVER_ACCSWIFT"]}<br />
{/if}
Variabilní symbol: {$order->ordcode}<br />
Částka: {$order->ordpricevat|formatPrice} {$order->ordpricevat|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}
{/if}

{* Google Analytics eComerce *}
{php
$dphSum = $order->ordpriceinclvat - $order->ordpricenovat;
}
<script type="text/javascript">
ga('require', 'ecommerce');
ga('ecommerce:addTransaction', {
  'id': {$order->ordcode},
  'affiliation': 'eShop {$presenter->config["SERVER_NAMESHORT"]},
  'revenue': {$dphSum},
  'shipping': {$order->orddelprice},
});

{ifset $order->items}
{foreach $order->items as $row}

ga('ecommerce:addItem', {
  'id': {$order->ordcode},
  'name': {$row->oriname},
  'sku': {$row->oriproid},
  'price': {$row->oripricenovat},
  'quantity': {$row->oriqty}
});

{/foreach}
{/ifset}

ga('ecommerce:send');
</script>
{* Heureka konverze PPC *}
{if !empty($presenter->config["HEUREKA"]["KeyMereniKonverzi"])}
<script type="text/javascript">
var _hrq = _hrq || [];
    _hrq.push(['setKey', {$presenter->config["HEUREKA"]["KeyMereniKonverzi"]}]);
    _hrq.push(['setOrderId', {$order->ordcode}]);
{foreach $order->items as $row}
    _hrq.push(['addProduct', {$row->oriname}, {$row->oriprice}, {$row->oriqty}]);
{/foreach}
    _hrq.push(['trackOrder']);

(function() {
    var ho = document.createElement('script'); ho.type = 'text/javascript'; ho.async = true;
    ho.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.heureka.cz/direct/js/cache/1-roi-async.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ho, s);
})();
</script>
{/if}
{* srovname.cz konverze PPC *}
{if !empty($presenter->config["SROVNAMECZ"]["KeyMereniKonverzi"])}
<script type="text/javascript">
        var _srt = _srt || [];
        _srt.push(['_setShop', {$presenter->config["SROVNAMECZ"]["KeyMereniKonverzi"]}]);
        _srt.push(['_setTransId', {$order->ordcode}]);
{foreach $order->items as $row}
        _srt.push(['_addProduct', {$row->oriname}, {$row->oriprice}, {$row->oriqty}]);
{/foreach}
        _srt.push(['_trackTrans']);
        (function() {
            var s = document.createElement("script");
            s.type = "text/javascript";
            s.async = true;
            s.src = ("https:" == document.location.protocol ? "https" : "http") +
                        "://www.srovname.cz/js/track-trans.js";
            var x = document.getElementsByTagName("script")[0];
            x.parentNode.insertBefore(s, x);
        })();
</script>
{/if}
{* hledejceny.cz *}
{if !empty($presenter->config["HLEDEJCENYCZ"]["KeyMereniKonverzi"])}
<script type="text/javascript">
var _htr = _htr || [];
_htr.push(["_setKey", {$presenter->config["HLEDEJCENYCZ"]["KeyMereniKonverzi"]}]);
_htr.push(["_setOrderId", {$order->ordcode}]);
_htr.push(["_addProduct", {$row->oriname}, {$row->oriprice}, {$row->oriqty}, {$row->procode}]);
_htr.push(["_trackOrder", true]);
(function() {
var s = document.createElement("script");
s.type = "text/javascript";
s.async = true;
s.src = ("https:" == document.location.protocol ? "https://ssl" :
"http://www") + ".hledejceny.cz/js/app/affiliate/track-order.js";
var x = document.getElementsByTagName("script")[0];
x.parentNode.insertBefore(s, x);
})();
</script>
{/if}
{if !empty($presenter->config["ZBOZICZ"]["IdMereniKonverzi"])}
{* zbozi.cz konverze PPC *}
<script>
  (function(w,d,s,u,n,k,c,t){
    w.ZboziConversionObject=n;w[n]=w[n]||function(){
    (w[n].q=w[n].q||[]).push(arguments)};w[n].key=k;c=d.createElement(s);
    t=d.getElementsByTagName(s)[0];c.async=1;c.src=u;t.parentNode.insertBefore(c,t)
  })(window,document,"script","https://www.zbozi.cz/conversion/js/conv.js","zbozi",{$presenter->config["ZBOZICZ"]["IdMereniKonverzi"]});

   zbozi("setOrder",{
      "orderId": {$order->ordcode},
      "totalPrice": {($order->ordpriceinclvat+$order->orddelprice)}
   });
   zbozi("send");
</script>
{/if}

<!-- Měřicí kód Sklik.cz -->
<script type="text/javascript">
var seznam_cId = 100021291;
var seznam_value = {$order->ordpricevat};
</script>
<script type="text/javascript" src="//www.seznam.cz/rs/static/rc.js"></script>

<!-- Google Code for Objednavka sccom Conversion Page -->
<script type="text/javascript">
  /* <![CDATA[ */
  var google_conversion_id = 951659273;
  var google_conversion_language = "en";
  var google_conversion_format = "3";
  var google_conversion_color = "ffffff";
  var google_conversion_label = "xNF-CIrpn2wQidbkxQM";
  var google_conversion_value = {$order->ordpricevat};
  var google_conversion_currency = "CZK";
  var google_remarketing_only = false;
  /* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
</script>
<noscript>
  <div style="display:inline;">
    <img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/951659273/?value={$order->ordpricevat}&amp;currency_code=CZK&amp;label=xNF-CIrpn2wQidbkxQM&amp;guid=ON&amp;script=0"/>
  </div>
</noscript>

{* affiliate pixel kód *}

{if (!empty($cjEvent))}
    {var $str = []}
    {var $ids = []}
    {var $cnt = 0}
    {var $priceSum = 0}
    {foreach $order->items as $row}
        {var $cnt=$cnt+1}
        {var $vatLevel = (int)$presenter->config["VATTYPE_".$row->orivatid]}
        {var $price = round($row->oriprice / (1+($vatLevel / 100)), 2)}
        {var $ids[] = $row->oriproid}
        {var $str[] = "item" . $cnt . "=" . $row->oriproid . "&amt" . $cnt . "=" . $price . "&qty" . $cnt . "=" . $row->oriqty . "&dcnt" . $cnt . "=0"}
    {/foreach}
    {var $urlItems = trim(implode("&", $str), '&')}
    {var $sku = trim(implode(",", $ids), ',')}
    {var $url = "https://www.kdukvh.com/u?cid=1559917&oid={$order->ordcode}&type=420229&" . $urlItems .  "&discount=0&coupon=&currency=CZK&CJEVENT=" . $cjEvent . "&method=IMG"}
{/if}


{if !empty($url)}
<img src="{$url}" height="1" width="20">
{/if}

{/block}