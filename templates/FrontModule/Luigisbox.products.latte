{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<items>
  {foreach $rows as $row}
    {php
      $catPath = str_replace('|', ' | ', $row->catpath);
    }
    <item>
      <title>{$row->proname}</title>
      <url>{plink //:Front:Product:detail, $row->proid, getProKey($row)}</url>
      <availability>{$row->proaccess < 100 ? 1 : 0}</availability>
      <availability_rank>{$availabilityRank[$row->proaccess]}</availability_rank>
      <availability_rank_text>{$row|getProAccessText:$enumProaccess}</availability_rank_text>
      <category primary="true">{$catPath}</category>
      <brand>{$row->manname}</brand>
      <price>{$row->propricea}</price>
      {if $row->propricecom > $row->propricea}<price_old>{$row->propricecom}</price_old>{/if}
      <image_link_s>{$baseUrl}/{getPicName($row, 'product/list')}</image_link_s>
      <image_link_m>{$baseUrl}/{getPicName($row, 'product/detail')}</image_link_m>
      <image_link_l>{$baseUrl}/{getPicName($row, 'product/big')}</image_link_l>
      <description>{$row->prodesc}</description>
      <description_short>{if !empty($row->prodescs)}{$row->prodescs}{else}{$row->prodesc|stripHtml|truncate:250}{/if}</description_short>
      <labels>{$row|getTagsList:$delFreeLimitByVendor:$catalogGifts:$discGifts}</labels>
      <product_code>{$row->procode}</product_code>
      {if strlen($row->procode2) == 13}<ean>{$row->procode2}</ean>{/if}
      <to_cart_id>{$row->proid}</to_cart_id>
      <introduced_at>{$row->prodatec|date:'Y-m-d'}</introduced_at>
      {if $row->margin > 0}<margin>{$row->margin}</margin>{/if}
    </item>
  {/foreach}
</items>