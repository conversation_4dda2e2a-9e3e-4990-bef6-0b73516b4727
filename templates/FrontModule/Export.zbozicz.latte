{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP xmlns="http://www.zbozi.cz/ns/offer/1.0">

{php $zboziPath = [];}

{foreach $rows as $row}

  {php
    $isGastro = str_contains($row->catpathids, '|265|');

    $zboziPath = ($row|getCatalogPath:$zboziPath:$zboziCatalogs);

    $catName = $row->catname;
    if (isset($zboziPath[$row->catid])) {
      $catName = $zboziPath[$row->catid];
    }

    $proname = ($row|getFeedProName:$catName);

    $giftsByType = ($row|getFeedGiftsByType:$gifts:$catalogGifts:$defGiftsNames:TRUE);
  }

<SHOPITEM>
<ITEM_ID>{$row->proid}</ITEM_ID>
{if !empty($row->procode2)}<EAN>{$row->procode2}</EAN>{/if}
<PRODUCTNAME>{$proname}</PRODUCTNAME>
<PRODUCT>{$proname}{if !empty($row->pronameadd)} {$row->pronameadd}{/if}</PRODUCT>
{if $row->promanid > 1}<MANUFACTURER>{$row->manname}</MANUFACTURER>{/if}
<DESCRIPTION>{$proname} {$row->prodescs}</DESCRIPTION>
<URL>{plink //:Front:Product:detail, $row->proid, getProKey($row)}?utm_source=zbozicz</URL>
<DELIVERY_DATE>{$row|getProAccess}</DELIVERY_DATE>
<PRICE_VAT>{$row->propricea}</PRICE_VAT>
{if $row->propricecom}<LIST_PRICE>{$row->propricecom}</LIST_PRICE>{/if}
{php
  if (!empty($row->procpczbozi) > 0) {
    $cpc = floatval($row->procpczbozi);
  } else {
    $cpc = floatval($presenter->config["PRODUCT_CPCZBOZI"]);
  }
  if (!empty($row->procpczbozis) > 0) {
    $cpcs = floatval($row->procpczbozis);
  } else {
    $cpcs = floatval($presenter->config["PRODUCT_CPCZBOZIS"]);
  }
}
<MAX_CPC>{$cpc}</MAX_CPC>
{php
  $delCost = -1;
}
{foreach $delModes as $key=>$rowd}

  {php
    if ($row->provenid=='nosreti' && $key != 'GEIS')  {
      continue;
    }
    if ($row->provenid!='nosreti' && $key == 'GEIS') {
      continue;
    }
  }

  <DELIVERY>
    <DELIVERY_ID>{if $key === 'OSOBNE'}VLASTNI_VYDEJNI_MISTA{else}{$key}{/if}</DELIVERY_ID>
    {if isset($delModes[$key]["paybefore"])}
    {php
      $prcDel = ($delModes[$key]["paybefore"]->dellimitfrom <= $row->propricea && $delModes[$key]["paybefore"]->dellimitto > 0 ? 0 : $delModes[$key]["paybefore"]->delprice);
      $prcPay = ($delModes[$key]["paybefore"]->paylimitfrom <= $row->propricea && $delModes[$key]["paybefore"]->paylimitto > 0 ? 0 : $delModes[$key]["paybefore"]->payprice);
      if (!isset($delIdsExcludedDelFree[$delModes[$key]["paybefore"]->delid]) &&  ($row->prodelfree === 1 && ($key == "CESKA_POSTA_BALIKOVNA" || $key == "CESKA_POSTA"))) {
        $prcDel = 0;
      }
      $prc = $prcDel + $prcPay;
      $delCost = $prc;
    }
    <DELIVERY_PRICE>{$prc}</DELIVERY_PRICE>
    {/if}
    {if isset($delModes[$key]["dobirka"])}
    {php
    	
      $prcDel = ($delModes[$key]["dobirka"]->dellimitfrom <= $row->propricea && $delModes[$key]["dobirka"]->dellimitto > 0 ? 0 : $delModes[$key]["dobirka"]->delprice);
      $prcPay = ($delModes[$key]["dobirka"]->paylimitfrom <= $row->propricea && $delModes[$key]["dobirka"]->paylimitto > 0 ? 0 : $delModes[$key]["dobirka"]->payprice);
      if (!isset($delIdsExcludedDelFree[$delModes[$key]["dobirka"]->delid]) && $row->prodelfree === 1) {
        $prcDel = 0;
      }
      if (!isset($delIdsExcludedDelFree[$delModes[$key]["paybefore"]->delid]) &&  ($row->prodelfree === 1 && ($key == "CESKA_POSTA_BALIKOVNA" || $key == "CESKA_POSTA"))) {
        $prcDel = 0;
        $prcPay = 0;
      }
      
      $prc = $prcDel + $prcPay;
      if ($prc < $delCost) {
        $delCost = $prc;
      }
    }
    <DELIVERY_PRICE_COD>{$prc}</DELIVERY_PRICE_COD>
    {/if}
  </DELIVERY>
{/foreach}

{if isset($zboziPath[$row->catid])}
<CATEGORYTEXT>{$zboziPath[$row->catid]}</CATEGORYTEXT>
{/if}
{if $isGastro}
  <IMGURL>{$baseUrl}/export/get-custom-pic?proid={$row->proid}</IMGURL>
{else}
  <IMGURL>{$baseUrl}/{getPicName($row, 'product/detail')}</IMGURL>
  {/if}
<MAX_CPC_SEARCH>{$cpcs}</MAX_CPC_SEARCH>
{foreach $giftsByType as $gType => $items}
  {foreach $items as $item}
    {if $gType == 'SALES_VOUCHER'}
<EXTRA_MESSAGE>voucher</EXTRA_MESSAGE>
<VOUCHER_TEXT>{$item["name"]}</VOUCHER_TEXT>
    {elseif $gType == 'EXTENDED_WARRANTY'}
<EXTRA_MESSAGE>extended_warranty</EXTRA_MESSAGE>
    {elseif $gType == 'GIFT'}
<EXTRA_MESSAGE>free_gift</EXTRA_MESSAGE>
<FREE_GIFT_TEXT>{$item["name"]}</FREE_GIFT_TEXT>
    {else}
<EXTRA_MESSAGE>free_gift</EXTRA_MESSAGE>
<FREE_GIFT_TEXT>{$item["name"]}</FREE_GIFT_TEXT>
    {/if}
  {/foreach}
{/foreach}
{if ($row->propricea >= $delFreeLimit || $row->prodelfree == 1)}
  <EXTRA_MESSAGE>free_delivery</EXTRA_MESSAGE>
{/if}
<EXTRA_MESSAGE>free_store_pickup</EXTRA_MESSAGE>
{if (isErotic($row))}
<EROTIC>1</EROTIC>
{/if}
</SHOPITEM>
{/foreach}
</SHOP>