{contentType application/xml; charset=utf-8}
<?xml version="1.0"?>
<rss version="2.0" xmlns:g="http://base.google.com/ns/1.0">
  <channel>
    <title>{$presenter->config["SERVER_NAME"]}</title>
    <link>{$baseUrl}</link>
    <description>{$presenter->config["HEADER_H2"]}</description>
    {foreach $rows as $row}
      {php
        $catName = "";
        $proname = ($row|getFeedProName:$catName);
        $giftsNames = ($row|getFeedGiftsNames:$gifts:$catalogGifts:$defGiftsNames);

        $key = $row->proid."-".substr(md5($row->proid . $row->prodatec), 0, 6);

        $price = $row->propricea;
        $curCode = 'CZK';
      }

      <item>
        <title>{$proname}{if !empty($row->pronameadd)} {$row->pronameadd}{/if} {if !empty($giftsNames) && $row->propricee == 0} +DÁREK{/if}</title>
        <link>{plink //:Front:Product:detail, $row->proid, getProKey($row)}</link>
        <description>{if !empty($row->prodescs)}<![CDATA[{$row->prodescs|striptags}]]>{/if}</description>
        <g:brand>{$row->manname}</g:brand>
        {if !empty($row->procode2)}<g:gtin>{$row->procode2}</g:gtin>{else}<g:identifier_exists>FALSE</g:identifier_exists>{/if}
        <g:image_link>{$baseUrl}/{getPicName($row, 'product/detail')}</g:image_link>
        <g:price>{$price} {$curCode}</g:price>
        <g:condition>new</g:condition>
        <g:id>{$row->proid}</g:id>
        <g:availability>{if getProAccess($row) <= 3}in stock{else}out of stock{/if}</g:availability>
        {php
        $catPath = mb_strtolower($row->catpath, 'UTF-8');
        $arr = explode("|", $catPath);
        $catPath = str_replace('|', ' > ', $catPath);
        }
        <g:product_type><![CDATA[{$catPath|noescape}]]></g:product_type>
        <g:custom_label_0>gastro zařízení</g:custom_label_0>
    </item>
    {/foreach}
  </channel>
</rss>