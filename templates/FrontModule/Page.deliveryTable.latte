{varType App\Orm\Delivery[] $deliveries}
{default $pageTitle       = 'Dopravy a platby'}

{block content}
    <main role="main">

        <section class="section">

            <article class="container" role="article">

            <h2>Doprava a platba</h2>

              <p style="color: red"><strong>POZOR! Některé produkty se nemusí do limitu na DOPRAVU ZDARMA započítávat. O této skutečnosti budete informováni v košíku. Děkujeme za pochopení.</strong></p>

             <table class="table">
              {foreach $deliveries as $delivery}
                <tr>
                  <th class="vertical-align: center">
                    <h3 style="margin-bottom: 0px;">{$delivery->delname} {if $delivery->dellimitfrom > 0}&nbsp;<small>(doprava zdarma od {$delivery->dellimitfrom|formatPrice})</small>{/if}</h3>

                  </th>
                  <th style="text-align: center;vertical-align: center"><h3 style="margin-bottom: 0px;">{if $delivery->delprice == 0}ZDARMA{else}{$delivery->delprice} Kč{/if}</h3></th>
                </tr>
                {foreach $delivery->payments as $payment}
                  {if $payment->delcode === 'creditcard' && $delivery->delcode !== 'OSOBNE'}
                    {* platby kartou neinzerovat u přeprav *}
                  {else}
                  <tr>
                    <td>&nbsp;&nbsp;&nbsp;{$payment->delname}</td>
                    <td style="text-align: center">{if $payment->delprice == 0}ZDARMA{else}{$payment->delprice} Kč{/if}</td>
                  </tr>
                  {/if}
                {/foreach}
              {/foreach}
            </table>
            <p>Výše zobrazené ceny jsou uvedeny vč. DPH</p>

            </article>

        </section>

    </main>
{/block}