{ifset $basketAdd}


  <script src="{$baseUrl}/js/scripts.js?v2" type="text/javascript"></script>

<div class="modal" id="basket-modal" style="display: block;">

  <div class="modal__body basket-modal">

    <h2 class="basket-modal__header">
      <i class="icon icon_basket2"></i>
      Zboží bylo přidáno do košíku
      <div class="basket-modal__close modal__close"><i class="icon icon_ko"></i></div>
    </h2>

    <div class="basket-modal__content">

      <div class="basket-modal__image"><img src="{$baseUrl}/{$basketAdd|getPicName:'product/list'|noescape}" alt="{$basketAdd->proname}"></a></div>
      <h3 class="basket-modal__name">{$basketAdd->proname}</h3>
      <div class="basket-modal__price"><strong>{$basketAdd->proprice|formatPrice} {$basketAdd->proprice|formatPriceEur:$config["EUR_RATE"]|noescape}</strong> ({$basketAdd|formatPriceNoVat})</div>
      <div class="basket-modal__controls">
        <a class="modal__close" href="#">Zpět do obchodu</a>
        <a href="{plink Basket:default}" class="basket-modal__buy">Zobrazit košík <i class="icon icon_arrow-right"></i></a>
      </div>

    </div>
    {if is_array($basketAccessories)}
    <div class="basket-modal__accesories">

      <h4>Doporučujeme</h4>

      <div class="slider-accesories owl-carousel owl-theme">
        {foreach $basketAccessories as $row}
          <div class="basket-modal__product">
            <span class="basket-modal__image">
              <img src="{$baseUrl}/{$row|getPicName:'product/list'|noescape}" alt="{$row->proname}">
            </span>
            <strong>{$row->proname}</strong><br>
            <span class="basket-modal__info">
              <span class="basket-modal_product-price">{$row->proprice|formatPrice}</span> {$row->proprice|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}
              <a n:href="basketAdd! $row->proid" class="ajax basket-modal__buy"><i class="icon icon_basket2"></i> Koupit</a>
            </span>
          </div>
        {/foreach}
      </div>

    </div>
    {elseif $basketAccessories !== FALSE}
    <a href="{$basketAccessories}"><img src="{$baseUrl}/pic/{$serverId}/basket_banner.jpg" alt="Nabídka v košíku"></a>
    {/if}
  </div>

</div>
{/ifset}
