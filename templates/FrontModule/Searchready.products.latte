{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<PRODUCTS>
  {foreach $rows as $row}
    {php
      $catPath = str_replace('|', ' | ', $row->catpath);
      $labels = ($row|getTagsList:$delFreeLimitByVendor:$catalogGifts:$discGifts:FALSE);
      $isSalable = isSalable($row);
    }
    <PRODUCT>
      <TITLE>{$row->proname}</TITLE>
      <URL>{plink //:Front:Product:detail, $row->proid, getProKey($row)}</URL>
      <IMAGE>{$baseUrl}/{getPicName($row, 'product/big')}</IMAGE>
      <CONTENT>{$row->prodesc}</CONTENT>
      <SHORT_DESCRIPTION>{if !empty($row->prodescs)}{$row->prodescs}{else}{$row->prodesc|stripHtml|truncate:250}{/if}</SHORT_DESCRIPTION>
      <PRICE>{$row->propricea}</PRICE>
      {if $row->propricecom > $row->propricea}<ORIGINAL_PRICE>{$row->propricecom}</ORIGINAL_PRICE>{/if}
      <PRODUCT_CODE>{$row->procode}</PRODUCT_CODE>
      <LABELS>
      {foreach $labels as $label}
        <LABEL>{$label}</LABEL>
      {/foreach}
      </LABELS>
      <BRAND>{$row->manname}</BRAND>
      <DELIVERY>{if $isSalable}{$row|getProAccessText:$enumProaccess}{else}Nedostupné{/if}</DELIVERY>
      <STOCK_AVAILABILITY>{$row->proaccess < 100 ? 1 : 0}</STOCK_AVAILABILITY>
      <CATEGORIES>
          <CATEGORY main_category="1">{$catPath}</CATEGORY>
      </CATEGORIES>
      {if $row->margin > 0}<MARGIN>{$row->margin}</MARGIN>{/if}
      {ifset $saleStats[$row->proid]}<SALES>{$saleStats[$row->proid]}</SALES>{/ifset}
    </PRODUCT>
  {/foreach}
</PRODUCTS>