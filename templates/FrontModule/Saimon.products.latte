{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<SHOP>
  {foreach $rows as $row}
    {php
      $catPath = str_replace('|', ' | ', $row->catpath);
      $catIds = str_replace('|', ',', trim($row->catpathids, '|'));
      $catIdsArr = explode('|', trim($row->catpathids, '|'));
    }
    <ITEM>
      <ITEM_ID>{$row->proid}</ITEM_ID>
      <ITEM_GROUP_ID />
      <CODE>{$row->procode}</CODE>
      <EAN>{$row->procode2}</EAN>
      <PRODUCT>{$row->proname}</PRODUCT>
      <PEREX>{if !empty($row->prodescs)}{$row->prodescs}{else}{$row->prodesc|stripHtml|truncate:250}{/if}</PEREX>
      <DESCRIPTION>{$row->prodesc}</DESCRIPTION>
      <URL>{plink //:Front:Product:detail, $row->proid, getProKey($row)}</URL>
      <IMGURL>{$baseUrl}/{getPicName($row, 'product/detail')}</IMGURL>
      <PRICE_VAT>{$row->propricea}</PRICE_VAT>
      {if $row->propricecom > $row->propricea}<PRICE_OLD>{$row->propricecom}</PRICE_OLD>{/if}
      <CURRENCY>CZK</CURRENCY>
      <BRAND>{$row->manname}</BRAND>
      <AVAILABILITY>{if $row->proaccess_web2 > 0}out_of_stock{else}in_stock{/if}</AVAILABILITY>
      <AVAILABILITY_TEXT>{$row|getProAccessText:$enumProaccess}</AVAILABILITY_TEXT>
      <STOCK>{$row->proqty_web2}</STOCK>
      <CATEGORY_TEXT>{$catPath}</CATEGORY_TEXT>
      <CATEGORY_IDS>{$catIds}</CATEGORY_IDS>
      {if isset($catIdsArr[0])}<CATEGORY_MAIN_ID>{$catIdsArr[0]}</CATEGORY_MAIN_ID>{/if}
      <DELIVERY_DATE>{$row->proaccess_web2}</DELIVERY_DATE>
    </ITEM>
  {/foreach}
</SHOP>