{default $thisCatId=Null}
<ul>
{*hlavni uroven katalogu*}
{foreach $menuCatalog as $level1}
  <li><a href="{plink Catalog:detail, $level1["masid"], getURLKey($level1["masname"], $level1["maskey"])}">{$level1["masname"]}</a>
  {*podrizene urovne katalogu - pokud jsou*}
  {if count($level1["subid"]) > 0}
    {foreach $level1["subid"] as $n => $level2}
      {if $level2["subid"] > 0 }
        {if $iterator->isFirst()}
    <ul>
        {/if}
      <li {if $level2["subid"] == $thisCatId}class="active"{/if}><a href="{plink Catalog:detail, $level2["subid"], getURLKey($level2["subname"], $level2["subkey"])}">{$level2["subname"]}</a></li>
        {if $iterator->isLast()}
    </ul>
        {/if}
      {/if}
    {/foreach}
  {/if}
  {*podrizene urovne katalogu - pokud jsou*}
  </li>
{/foreach}
{*hlavni uroven katalogu*}
</ul>
