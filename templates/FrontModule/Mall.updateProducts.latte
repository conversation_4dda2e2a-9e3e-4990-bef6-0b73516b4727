{default $pageTitle = 'Mall - výsledek aktualizace produktů'}
{default $pageRobots = "nofollow,noindex"}

{block #content}

  {php
  $titles["err"] = "Chyby";

  }

  <h2>Mall - výsledek aktualizace produktů</h2>

  {if (isset($errors) && count($errors) > 0)}
  {foreach $errors as $code => $errs}
    {if $code !== 'info'}
    <h2>{$titles[$code]}</h2>
    <table>
    {foreach $errs as $err}
      <tr>
        <td><a href="{plink :Admin:Product:editCode $err["procode"]}" target="_blank">{$err["procode"]}</a></td>
        <td>{$err["text"]}</td>
      </tr>
    {/foreach}
    </table>
    {/if}
  {/foreach}
  {/if}
{/block}