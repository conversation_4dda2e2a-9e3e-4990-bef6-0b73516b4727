{varType App\classes\CatalogItem[] $menuRoot}
{varType App\Orm\Catalog[]|null $resultCatalogs}
{* Search.default.latte *}

{* nastaveni promennych *}
{default $pageTitle = $presenter->translator->translate('Vyhledávání')}
{default $pageKeywords = ''}
{default $pageDescription = $presenter->translator->translate('Vyhledávání zboží')}
{default $canonicalUrl = $presenter->link('//Search:default', array('name'=>'', 'fulltext'=>'', 's'=>'', 'o'=>'', 'm'=>array()))}

{php
    $GLOBALS["ecommProId"] = '';
    $GLOBALS["ecommPageType"] = 'searchresults';
    $GLOBALS["ecommTotalValue"] = 0;
}

{block content}
    {var $proIds = []}

    <section class="section">

        <article class="container" role="article">

            <div class="row row--start">
                <aside class="col col--2 sidebar" role="complementary">
                    <nav class="sidebar-nav" role="navigation" n:if="isset($menuRoot)&&count($menuRoot)">
                        {foreach $menuRoot as $menu}
                            <h2>
                                <a n:href="Catalog:detail, $menu->id, getURLKey($menu->name, $menu->row->catkey)">{$menu->name}</a>
                            </h2>
                            <ul class="list sidebar-nav__ul-primary">
                                {include menu, $menu->child,1}
                                    {define menu, $childrens,$level}
                                    <li n:foreach="$childrens as $child">
                                        <a n:href="Catalog:detail, $child->id, getURLKey($child->name, $child->row->catkey)" n:class="!$child->child ? active">
                                            {if $level == 1}<strong>{/if}
                                                {$child->name}
                                            {if $level == 1}</strong>{/if}
                                        </a>
                                        <ul n:if="$child->child">
                                            {include menu, $child->child,$level+1}
                                        </ul>
                                    </li>
                                {/define}
                            </ul>
                        {/foreach}
                    </nav>

                    <div class="sidebar-filter" role="region" aria-label="Filtrování produktů">

                        <div class="sidebar-filter__mobile">
                          <span class="modal__close js-filter-close">
                            <span class="icon icon--times-solid">
                              <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                <use xlink:href="{$baseUrl}/style/{$serverId}/img/icons.svg#times-solid" x="0" y="0" width="100%"
                                     height="100%"></use>
                              </svg>
                            </span>
                          </span>

                            <h2>Upřesnit parametry</h2>
                        </div>

                        {form detailSearchForm}

                            <div class="sidebar-filter__block is-open">
                                <h2 class="sidebar-filter__toggle js-parent-toggle">Značky</h2>

                                <div class="sidebar-filter__show-hide">

                                    {foreach $form['mans']->controls as $item}

                                        <p class="form-checkbox{if $iterator->getCounter() > 10}  js-more is-hidden{/if}">
                                            <input n:name="$item">
                                            <span class="form-checkbox__box"></span>
                                            <label n:name="$item" class="form-label"/>
                                        </p>

                                    {/foreach}

                {if iterator_count($form['mans']->getControls()) > 10}
                                        <p class="more-control js-more-control">
                                            <span class="more-control__open">Další značky<i
                                                        class="fas fa-caret-down"></i></span>
                                            <span class="more-control__close">Skrýt další značky<i
                                                        class="fas fa-caret-up"></i></span>
                                        </p>
                                    {/if}

                                </div>
                            </div>

                            {*
                            <div class="sidebar-filter__block is-open">
                              <h2 class="sidebar-filter__toggle js-parent-toggle">Cena</h2>
                              <div class="sidebar-filter__show-hide">
                                {php //include('components/range.php'); }
                              </div>
                            </div>
                            *}

                            {*
                            <div class="sidebar-filter__block is-open">
                              <h2 class="sidebar-filter__toggle js-parent-toggle">Typ</h2>
                              <div class="sidebar-filter__show-hide">
                                <p class="form-checkbox">
                                  <input type="checkbox" name="checkbox" id="compatible" checked="checked">
                                  <span class="form-checkbox__box"></span>
                                  <label class="form-label" for="compatible">Kompatibilní <span>48</span></label>
                                </p>
                                <p class="form-checkbox">
                                  <input type="checkbox" name="checkbox" id="original">
                                  <span class="form-checkbox__box"></span>
                                  <label class="form-label" for="original">Originální <span>12</span></label>
                                </p>
                              </div>
                            </div>

                            <div class="sidebar-filter__block">
                              <h2 class="sidebar-filter__toggle js-parent-toggle">Barva</h2>
                              <div class="sidebar-filter__show-hide">
                                <p class="form-checkbox">
                                  <input type="checkbox" name="checkbox" id="white">
                                  <span class="form-checkbox__box"></span>
                                  <label class="form-label" for="white">Bílá <span>12</span></label>
                                </p>
                                <p class="form-checkbox">
                                  <input type="checkbox" name="checkbox" id="black">
                                  <span class="form-checkbox__box"></span>
                                  <label class="form-label" for="black">Černá <span>24</span></label>
                                </p>
                              </div>
                            </div>

                            <div class="sidebar-filter__block">
                              <h2 class="sidebar-filter__toggle js-parent-toggle">Další</h2>
                              <div class="sidebar-filter__show-hide">
                                <p class="form-checkbox">
                                  <input type="checkbox" name="checkbox" id="next">
                                  <span class="form-checkbox__box"></span>
                                  <label class="form-label" for="next">Další možnosti</label>
                                </p>
                              </div>
                            </div>
                            *}
                            {*input search*}
                            {input detailSearch}
                        {/form}

                    </div>

                </aside>


                <div class="col col--2 col--grow">

                    <div class="breadcrumbs" role="navigation" aria-label="Drobečková navigace">

                        <a n:href="Homepage:default"><i class="fas fa-home"></i><span class="sr-only">Domů</span></a>


                        <span class="breadcrumbs__separator" aria-hidden="true">&gt;</span>
                        Podrobné vyhledávání


                    </div>


                    <h1>{_'Podrobné vyhledávání'}</h1>

                    <div class="centerMenu" n:ifset="$resultCatalogs">
                        <div class="row row--start">
                            <div class="col col--6" n:foreach="$resultCatalogs as $oneCatalog">
                                <a n:href="Catalog:detail, $oneCatalog->id, $oneCatalog->getURLKey()">
                                    <strong>{$oneCatalog->catname}</strong>
                                    <img n:if="false" src="https://via.placeholder.com/68" alt="">
                                </a>
                            </div>
                        </div>
                    </div>


                    <div class="quick-filter">

                        <div class="row">
                            <div class="col col--2">
                                <div class="quick-filter__sort">
                                    <a href="{plink this o=>NULL}" class="btn{if empty($presenter->o)} is-open{/if}">Nejoblíbenější</a>
                                    <a href="{plink this o=>'price'}"
                                       class="btn{if $presenter->o === 'price'} is-open{/if}">Nejlevnější</a>
                                    <a href="{plink this o=>'price_'}"
                                       class="btn{if $presenter->o === 'price_'} is-open{/if}">Nejdražší</a>
                                </div>
                            </div>

                            <div class="col col--2">
                                <div class="quick-filter__tag">
                                    <p class="form-checkbox">
                                        <input type="checkbox" name="stock"
                                               id="stock" n:attr="checked: $presenter->s == 'onStore'"
                                               onchange="document.getElementById('onStoreBtn').click();">
                                        <span class="form-checkbox__box"></span>
                                        <label class="form-label" for="stock">Skladem</label><a
                                                id="onStoreBtn" n:href="this , s:$presenter->s == 'onStore'?'':'onStore'"></a>
                                    </p>
                                    <p class="form-checkbox" n:if="false">
                                        <input type="checkbox" name="shipping"
                                               id="shipping" n:attr="checked: $presenter->freeDelivery"
                                               onchange="document.getElementById('freeDeliveryfiltr').click();">
                                        <span class="form-checkbox__box"></span>
                                        <label class="form-label" for="shipping">Doprava zdarma</label><a
                                                id="freeDeliveryfiltr" n:href="this , freeDelivery:!$presenter->freeDelivery"></a>
                                    </p>
                                    <p class="form-checkbox" n:if="false">
                                        <input type="checkbox" name="unboxed"
                                               id="unboxed" n:attr="checked: $presenter->unpacked"
                                               onchange="document.getElementById('unpackedfiltr').click();">
                                        <span class="form-checkbox__box"></span>
                                        <label class="form-label" for="unboxed"> Rozbaleno</label><a
                                                id="unpackedfiltr" n:href="this , unpacked:!$presenter->unpacked"></a>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <button class="btn quick-filter__mobile js-filter"><i class="fas fa-filter"></i> Upřesnit
                            parametry
                        </button>

                    </div>
                    <!-- sort start -->
                    {*if !empty($catalogsData) && is_array($catalogsData)}
                      {foreach $catalogsData as $row}
                        {if $iterator->first}
                        <h3>Doporučené kategorie:</h3>
                        <fieldset>
                        {/if}
                        <h4><a style="color: black" href="{plink Catalog:detail, $row->catid, getURLKey($row->catname, $row->catkey)}">{$row->catpath}</a></h4>
                        {if $iterator->last}
                        </fieldset>
                        {/if}
                      {/foreach}
                    {/if*}





                        <!-- sort end -->

                    {control paginator}


                    <div class="row row--start product-row">

                            {foreach $productsData as $row}

                                {php
                                    $proIds[] = $row->proid;
                                    $GLOBALS["ecommTotalValue"] += $row->proprice;

                                    $proaccess = $enum_proaccess[$row->proaccess];
                                    if (isset($onRoad[$row->proid])) {
                                    if ($row->proaccess != 0 && $onRoad[$row->proid]->qty > 0) $proaccess = 'Na cestě';
                                    }
                                }

                                <div class="col col--4">
                                    {include 'productItem.latte', product:$row}
                                </div>

                            {/foreach}
                            {if !empty($presenter->name)}
                                <p>Nenašli jste co jste hledali? Zkuste <a class="button button--big"
                                                                           href="{plink 'this' name=>NULL, fulltext=>$presenter->name}">rozšířené
                                        hledání</a>.</p>
                            {else}
                                {if count($productsData) == 0}
                                    <p>Bohužel, nebylo nalezen žádný záznam. Zkuste, prosíme, změnit zadání.</p>
                                {/if}
                            {/if}
                    </div>

                    {control paginator}


                </div>

            </div>

            <hr>

        </article>

    </section>

    </div>
    {php
        $GLOBALS["ecommProId"] = '["' . implode('","', $proIds) . '"]';
    }

    <script>

        $('.storeChkFilter').click(function () {
            var val = $(this).prop('checked');
            $('.storeChkFilter').prop('checked', false);
            $(this).prop('checked', val);
        });

    </script>
{/block}
