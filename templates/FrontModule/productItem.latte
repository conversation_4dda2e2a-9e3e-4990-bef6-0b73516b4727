{var $isSalable = isSalable($product)}

{default $classAdd=""}
{default $basketAddQty=1}

<div class="product {$classAdd}">

    <div class="product__top">

        <div class="product__name">
            <a href="{plink Product:detail, $product->proid, getProKey($product)}" title="{$product->proname}">{$product->proname}</a>
        </div>

        {include 'productTagList.latte', product:$product}

    </div>

    <a href="{plink Product:detail, $product->proid, getProKey($product)}" class="product__image">

        <img src="{$baseUrl}/{getPicName($product, 'product/detail')}" width="280" height="260" alt="{$product->proname}">

        {*
        <div class="product__badge-list">
          <img src="{$baseUrl}/style/{$serverId}/img/from-tv.png" loading="lazy" alt="">
          <img src="{$baseUrl}/style/{$serverId}/img/3v1.png" loading="lazy" alt="">
          <img src="{$baseUrl}/style/{$serverId}/img/product-2019.png" loading="lazy" alt="">
        </div>
        *}

    </a>

    <div class="product__bottom">

        <div class="stock">{if $isSalable}{$product|getProAccessText:$enum_proaccess}{else}Nedostupné{/if}</div>

        <div class="product__desc">


            {if  FALSE} {* slevy vypnuty *}
                <div class="action">
                    <div class="action-col">
                        <p>CENA DŘÍVE <br><span class="action-price">{$product->propricecom|formatPrice}{$product->propricecom|formatPriceEur:$config["EUR_RATE"]|noescape}</span></p>
                    </div>
                    <div class="action-col">
                        <p>SLEVA <br><strong>{$product->propricecom|getDiscountInPer:$product->proprice}</strong></p>
                    </div>
                </div>
            {else}
            <p >{$product->prodescs!=''?$product->prodescs:($product->prodesc ?? '')|stripHtml}</p>
            {/if}
        </div>

        <div class="product__buy">
            <p class="product__price">{$product->proprice*$basketAddQty|formatPrice}{$product->proprice*$basketAddQty|formatPriceEur:$config["EUR_RATE"]|noescape}</p>
            <span n:if="$classAdd!=''" style="font-weight: bold">
                {$basketAddQty}&nbsp;ks
            </span>
            {if $classAdd!='product--basket'&&$isSalable}
                <form action="{plink basketAdd! $product->proid}" method="get" class="ajax product__buy-control">
              <span class="form-count">
                <div class="form-count__control">
                  <div class="form-count__plus">&plus;</div>
                  <div class="form-count__minus">&minus;</div>
                </div>
                <label class="sr-only" for="name">Množství</label>
                <input type="text" name="qty" min="0" max="999" value="1">
              </span>
                    <button type="submit" class="product__buy-btn btn"><i class="fas fa-shopping-basket"></i></button>
                </form>
            {/if}
        </div>

    </div>
</div>