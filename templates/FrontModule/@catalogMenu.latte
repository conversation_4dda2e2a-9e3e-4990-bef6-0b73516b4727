<div id="menu" class="nav">
      {default $masterCatId=21}
      {ifset $menuCatalog[$masterCatId]}
      {foreach $menuCatalog[$masterCatId] as $row}
        {if $iterator->isFirst()}
          <ul>
        {/if}
          {php
            $isActiveSubcategory15 = FALSE;
            $isActiveSubcategory25 = FALSE;
            $isActiveSubcategory35 = FALSE;
            $isActiveSubcategory45 = FALSE;
            $isActiveSubcategory55 = FALSE;
            if (!empty($thisCatId)) {
              $isActiveSubcategory15 = ($catalogPathIds[1] == $thisCatId || $catalogPathIds[2] == $thisCatId || $catalogPathIds[3] == $thisCatId || $catalogPathIds[4] == $thisCatId || $catalogPathIds[5] == $thisCatId);
              $isActiveSubcategory25 = ($catalogPathIds[2] == $thisCatId || $catalogPathIds[3] == $thisCatId || $catalogPathIds[4] == $thisCatId || $catalogPathIds[5] == $thisCatId);
              $isActiveSubcategory35 = ($catalogPathIds[3] == $thisCatId || $catalogPathIds[4] == $thisCatId || $catalogPathIds[5] == $thisCatId);
              $isActiveSubcategory45 = ($catalogPathIds[4] == $thisCatId || $catalogPathIds[5] == $thisCatId);
              $isActiveSubcategory55 = ($catalogPathIds[5] == $thisCatId);
            }
          }
          <li class="nav__item">
            <a href="{plink Catalog:detail, $row->catid, getURLKey($row->catname, $row->catkey)}" class="{if $catalogPathIds[1]==$row->catid && !$isActiveSubcategory25}is-active{/if}">
              {$row->catname}
              {if $catalogPathIds[1]==$row->catid && $isActiveSubcategory15 && isset($row->subItems) && count($row->subItems) > 0}
                <i class="icon icon_arrow-menu-open"></i>
              {else}
                <i class="icon icon_arrow-menu"></i>
              {/if}
            </a>
            {if $catalogPathIds[1]==$row->catid}
              {if isset($row->subItems)}
                {foreach $row->subItems as $irow}
                  {if $iterator->isFirst()}
                    <ul>
                  {/if}
                    <li class="nav__submenu">
                      <a href="{plink Catalog:detail, $irow->catid, getURLKey($irow->catname, $irow->catkey)}" class="{if !empty($irow->subItems)}has-submenu {/if}{if $catalogPathIds[2]==$irow->catid && !$isActiveSubcategory35}is-active{/if}">
                        {if $catalogPathIds[2]==$irow->catid}
                          <i class="icon icon_minus"></i>{$irow->catname}
                        {else}
                          <i class="icon icon_plus"></i>{$irow->catname}
                        {/if}
                      </a>
                      {if $catalogPathIds[2]==$irow->catid}
                        {if isset($irow->subItems)}
                          {foreach $irow->subItems as $iirow}
                            {if $iterator->isFirst()}
                              <ul class="nav__main__submenu">
                            {/if}
                              <li class="nav__main__submenu__item">
                                <a href="{plink Catalog:detail, $iirow->catid, getURLKey($iirow->catname, $iirow->catkey)}" class="{if !empty($iirow->subItems)} has-submenu{/if}{if $catalogPathIds[3]==$iirow->catid && !$isActiveSubcategory45} is-active{/if}">
                                  {if $catalogPathIds[3]==$iirow->catid}
                                    <i class="icon icon_minus"></i>{$iirow->catname}
                                  {else}
                                    <i class="icon icon_plus"></i>{$iirow->catname}
                                  {/if}
                                </a>

                                {if $catalogPathIds[3]==$iirow->catid}
                                  {if isset($iirow->subItems)}
                                    {foreach $iirow->subItems as $iiirow}
                                      {if $iterator->isFirst()}
                                        <ul class="nav__main__submenu">
                                      {/if}
                                        <li class="nav__main__submenu__item">
                                          <a href="{plink Catalog:detail, $iiirow->catid, getURLKey($iiirow->catname, $iiirow->catkey)}" class="{if !empty($iiirow->subItems)} has-submenu{/if}{if $catalogPathIds[4]==$iiirow->catid && !$isActiveSubcategory55} is-active{/if}">
                                            {if $catalogPathIds[4]==$iiirow->catid}
                                              <i class="icon icon_minus"></i>{$iiirow->catname}
                                            {else}
                                              <i class="icon icon_plus"></i>{$iiirow->catname}
                                            {/if}
                                          </a>

                                        </li>
                                      {if $iterator->isLast()}
                                        </ul>
                                      {/if}
                                    {/foreach}
                                  {/if}
                                {/if}



                              </li>
                            {if $iterator->isLast()}
                              </ul>
                            {/if}
                          {/foreach}
                        {/if}
                      {/if}
                    </li>
                  {if $iterator->isLast()}
                    </ul>
                  {/if}
                {/foreach}
              {/if}
            {/if}
          </li>

        {if $iterator->isLast()}
          </ul>
        {/if}
      {/foreach}
      {/ifset}