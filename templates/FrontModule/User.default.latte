{default $pageTitle       = $presenter->translator->translate('<PERSON><PERSON><PERSON>')}
{default $pageRobots      = "nofollow,noindex"}

{block #content}

    <section class="section section--login">

        <article class="container" role="article">

            <div class="content-nav">
                <ul>
                    <li><a href="{plink default}" class="is-active"><PERSON>ů<PERSON></a></li>
                    <li><a href="{plink edit}">Upravit údaje / změnit heslo</a></li>
                    <li><a href="{plink logout}">Odhlásit se</a></li>
                </ul>

            </div>

            <h1>{$pageTitle}</h1>

            {if $userRow->usrmailverified === 0}
                <p>
                    K dokončení registrace je nutné ověřit Váš email. Vyhledejte naši žádost o ověření na emailové adrese, kterou jste zadali při registraci ({$userRow->usrmail}) a postupujte podle pokynů v tomto emailu.<br>
                    <br>
                    Pokud jste nedostali ověřovací email, nejprve zkontrolujte, zda jste zadali email správně. Pokud je email správně, klikněte na <a href="{plink sendVerification}">poslat znovu ověřovací email</a>. Email bude zaslán na adresu <strong>{$userRow->usrmail}</strong>.<br>
                    <br>
                    Zároveň také zkontrolujte, zda tento email nezapadl do nevyžádané pošty (SPAM).<br>
                    <br>
                    Procedura ověření emailu je pro Vaši bezpečnost. Děkujeme za spolupráci.
                </p>

                <h2>Zde můžete zadat ověřovací kód který Vám byl zaslán do emailu.</h2>
                {form setVerifyCodeForm}
                    {input usrmailvcode size=>6} {input submit}
                {/form}
            {/if}

      <h2 class="title title--sm">Otevřené objednávky</h2>
      {if count($openedOrders) > 0}
                {foreach $openedOrders as $row}
                    {if $iterator->isFirst()}
                        <table class="table table--orders" cellpadding="0" cellspacing="0" border="1">
                    {/if}
                    <tr>
                        <td><a href="{plink order $row->ordid}">{$row->ordcode}</a></td>
                        <td>{$row->orddatec|date:'%d.%m.%Y'}</td>
                        <td>{$row->ordprice|formatPrice}</td>
                        <td>{$enum_ordstatus[$row->ordstatus]}</td>
                    </tr>
                    {if $iterator->isLast()}
                        </table>
                    {/if}
                {/foreach}
            {else}
                <p>{_'Žádné otevřené objednávky'}</p>
            {/if}

      <h2 class="title title--sm">Uzavřené objednávky</h2>

      {if count($closedOrders) > 0}
                {foreach $closedOrders as $row}
                    {if $iterator->isFirst()}
                        <table class="table table--orders" cellpadding="0" cellspacing="0" border="1">
                    {/if}
                    <tr>
                        <td><a href="{plink order $row->ordid}">{$row->ordcode}</a></td>
                        <td>{$row->orddatec|date:'%d.%m.%Y'}</td>
                        <td>{$row->ordprice|formatPrice}</td>
                        <td>{$enum_ordstatus[$row->ordstatus]}</td>
                    </tr>
                    {if $iterator->isLast()}
                        </table>
                    {/if}
                {/foreach}
            {else}
                <p>{_'Žádné uzavřené objednávky'}</p>
            {/if}

      <h2 class="title title--sm">Hlídací pes</h2>

      {if count($watchDogs) > 0}
                {foreach $watchDogs as $row}
                    {if $iterator->isFirst()}
                        <table class="table table--orders" cellpadding="0" cellspacing="0" border="1">
                    {/if}
                    <tr>
                        <td><a href="{plink Product:detail, $row->proid, getProKey($row)}">{$row->proname}</a></td>
                        <td><a href="{plink User:watchDog, $row->proid}">Vymazat hlídání</a></td>
                    </tr>
                    {if $iterator->isLast()}
                        </table>
                    {/if}
                {/foreach}
            {else}
                <p>{_'Hlídání není nastaveno'}</p>
            {/if}

        </article>

    </section>

{/block}