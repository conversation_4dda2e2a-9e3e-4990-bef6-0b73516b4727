{varType App\Orm\Product[] $products}
{varType App\Orm\Banner $banner}
{varType App\Orm\Catalog[] $resultCatalogs}
{varType string $orderBy}
{varType bool $onStore}

{default $pageTitle = $presenter->translator->translate($banner->name)}
{default $pageKeywords = ''}
{default $pageDescription = $presenter->translator->translate($banner->name)}


{block content}
    {var $proIds = []}

    <section class="section">

        <article class="container" role="article">

            <div class="row row--start">


                <div class="col col--2 col--grow">

                    <div class="breadcrumbs" role="navigation" aria-label="Drobečková navigace">

                        <a n:href="Homepage:default"><i class="fas fa-home"></i><span class="sr-only">Domů</span></a>


                        <span class="breadcrumbs__separator" aria-hidden="true">&gt;</span>
                        {$banner->name}


                    </div>


                    <h1>{_$banner->name}</h1>

                    {* popis kategorie *}

                        <div class="category-description" style="margin: auto;max-width: 100%">
                           <img src="{$banner->getImagSrc()}">
                        </div>

                    <div class="centerMenu" n:ifset="$resultCatalogs">
                        <div class="row row--start">
                            <div class="col col--6" n:foreach="$resultCatalogs as $oneCatalog">
                                <a n:href="Catalog:detail, $oneCatalog->id, $oneCatalog->getURLKey()">
                                    <strong>{$oneCatalog->catname}</strong>
                                    <img n:if="$oneCatalog->getImagSrc()" src="{$baseUrl}/{$oneCatalog->getImagSrc()}" alt="{$oneCatalog->catname}">
                                </a>
                            </div>
                        </div>
                    </div>


                    <div class="quick-filter">

                        <div class="row">
                            <div class="col col--2">
                                <div class="quick-filter__sort">
                                    <a n:href="this orderBy=>'favorite'"
                                    n:class="$orderBy==='favorite' ? 'is-open', btn">Nejoblíbenější</a>
                                    <a n:href="this orderBy=>'price'"  n:class="$orderBy==='price' ? 'is-open',btn">Nejlevnější</a>
                                    <a n:href="this orderBy=>'priceDOWN'" n:class="$orderBy==='priceDOWN' ? 'is-open',btn">Nejdražší</a>
                                </div>
                            </div>

                            <div class="col col--2">
                                <div class="quick-filter__tag">
                                    <p class="form-checkbox">
                                        <input type="checkbox" name="stock"
                                               id="stock" n:attr="checked: $onStore == 'onStore'"
                                               onchange="document.getElementById('onStoreBtn').click();">
                                        <span class="form-checkbox__box"></span>
                                        <label class="form-label" for="stock">Skladem</label><a
                                                id="onStoreBtn" n:href="this , onStore=>!$onStore"></a>
                                    </p>

                                </div>
                            </div>
                        </div>

                        <button class="btn quick-filter__mobile js-filter"><i class="fas fa-filter"></i> Upřesnit
                            parametry
                        </button>

                    </div>
                    <!-- sort start -->
                    {*if !empty($catalogsData) && is_array($catalogsData)}
                      {foreach $catalogsData as $row}
                        {if $iterator->first}
                        <h3>Doporučené kategorie:</h3>
                        <fieldset>
                        {/if}
                        <h4><a style="color: black" href="{plink Catalog:detail, $row->catid, getURLKey($row->catname, $row->catkey)}">{$row->catpath}</a></h4>
                        {if $iterator->last}
                        </fieldset>
                        {/if}
                      {/foreach}
                    {/if*}





                        <!-- sort end -->

                    {control paginator}


                    <div class="row row--start product-row">

                            {foreach $products as $product}



                                <div class="col col--4">
                                    {include 'productItemOrm.latte', product:$product,coupon:$banner->campaign??->getCoupon()}
                                </div>

                            {/foreach}

                                {if count($products) == 0}
                                    <p>Bohužel, nebylo nalezen žádný záznam. Zkuste, prosíme, změnit zadání.</p>
                                {/if}

                    </div>

                    {control paginator}


                </div>

            </div>

            <hr>

        </article>

    </section>

    </div>
    {*php
        $GLOBALS["ecommProId"] = '["' . implode('","', $proIds) . '"]';
    *}

    <script>

        $('.storeChkFilter').click(function () {
            var val = $(this).prop('checked');
            $('.storeChkFilter').prop('checked', false);
            $(this).prop('checked', val);
        });

    </script>
{/block}
