{varType App\Orm\Catalog|null $catalog}
{varType App\Orm\Catalog[] $rootCatalogs}
{varType App\Orm\Menu[] $menuFooter1}
{varType App\Orm\Menu[] $menuFooter2}
{varType App\Orm\Menu[] $menuFooter3}
{varType App\Orm\Basket $basketOrm}
{default $urlkey=''}
{default $lng=$lang}
<!DOCTYPE html>
<html class="no-js" lang="cs">

<head>

  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">

  <title>{$pageTitle} - {$presenter->config["SERVER_NAME"]}</title>

  {if isset($pageDescription)}
    <meta name="description" content="{$pageDescription|strip|truncate:255, ''}">
  {/if}
  {if isset($pageRobots)}
    <meta name="robots" content="{$pageRobots}">
    <meta name="googlebot" content="{$pageRobots}">
  {/if}
  {if isset($canonicalUrl)}
    <link rel="canonical" href="{$canonicalUrl}">
  {/if}

  <link rel="stylesheet" href="{$baseUrl}/style/{$serverId}/css/styles.css?v={$appVersion}" type="text/css">
  <link rel="stylesheet" href="{$baseUrl}/style/js/libs/jquery-ui/jquery-ui.min.css" type="text/css">

  <link rel="apple-touch-icon" sizes="180x180" href="{$baseUrl}/style/{$serverId}/favicons/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="{$baseUrl}/style/{$serverId}/favicons/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="{$baseUrl}/style/{$serverId}/favicons/favicon-16x16.png">
  <link rel="manifest" href="{$baseUrl}/style/{$serverId}/favicons/site.webmanifest">
  <link rel="mask-icon" href="{$baseUrl}/style/{$serverId}/favicons/safari-pinned-tab.svg" color="#080808">
  <link rel="shortcut icon" href="{$baseUrl}/style/{$serverId}/favicons/favicon.ico">
  <meta name="msapplication-TileColor" content="#d4921e">
  <meta name="msapplication-config" content="{$baseUrl}/style/{$serverId}/favicons/browserconfig.xml">
  <meta name="theme-color" content="#d4921e">

  {if $serverId == 'web1'}
    <meta name="google-site-verification" content="-r0fGxYalQPDBtXn0IaNx5Mhi5BVbymbvqffRerT0kw" />
  {elseif $serverId == 'web2'}
    <meta name="google-site-verification" content="ErdhTOocFU_NPKjKSYBJPPkZVtI9gi1Eiw9Nr-Nitk4" />
  {elseif $serverId == 'web3'}
    <meta name="google-site-verification" content="J2vZoCQ6ZlBP1pgqKeGxtxLaEy7rGpI8GHa_b8okINw" />
  {/if}

<script>
  document.documentElement.className = document.documentElement.className.replace("no-js", "js");
</script>

  {* vyskakovací košík *}
  {snippet basketModalWindow}
    <!-- modální okno - košík -->
    {include 'basketModalWindow.latte'}
  {/snippet}

  <script type="text/javascript">
  var basePath = {$baseUrl} ;
</script>

{if !empty($googleAnalytics["ua"])}
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id={$googleAnalytics["ua"]}&l=eshopDataLayer"></script>
  <script> window.eshopDataLayer = window.eshopDataLayer || []; function gtag(){ eshopDataLayer.push(arguments); } gtag('js', new Date()); gtag('config', {$googleAnalytics["ua"]}, { 'debug_mode': true }); </script>
{/if}

{block #ogHead}
{/block}

  {if $serverId == 'web1'}
  {elseif $serverId == 'web2'}
    <script async src="https://scripts.luigisbox.com/LBX-227096.js"></script>
    <!-- Tanganica pixel -->
    <script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':new Date().getTime(),event:'gtm.js' });var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f); })(window,document,'script','dataLayer','GTM-TRS5RSN');</script>
    <!-- End Tanganica pixel -->
  {elseif $serverId == 'web3'}
    <script async src="https://scripts.luigisbox.com/LBX-512681.js"></script>
    <!-- Tanganica pixel -->
    <script>(function(w,d,s,l,i){ w[l]=w[l]||[];w[l].push({ 'gtm.start':new Date().getTime(),event:'gtm.js' });var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f); })(window,document,'script','dataLayer','GTM-TRS5RSN');</script>
    <!-- End Tanganica pixel -->
  {/if}

  {if !empty($srovnamecz["KeyMereniKonverzi"])}
    <!-- Start Srovname.cz Pixel -->
    <script>
      !function(e,n,t,a,s,o,r){ e[a]||((s=e[a]=function(){ s.process?s.process.apply(s,arguments):s.queue.push(arguments) }).queue=[],(o=n.createElement(t)).async=1,o.src='https://tracking.srovname.cz/srovnamepixel.js',o.dataset.srv9="1",(r=n.getElementsByTagName(t)[0]).parentNode.insertBefore(o,r)) }(window,document,"script","srovname");srovname("init",{$srovnamecz["KeyMereniKonverzi"]});
    </script>
    <!-- End Srovname.cz Pixel -->
  {/if}
</head>
<body>

<script src="{$baseUrl}/js/ecomail.js"></script>

{include 'page-toolbar-top.latte'}

<header class="header">

  <div class="container">

    <div class="header__content">
      <div class="nav-switcher" tabindex="0" aria-label="Menu" role="button" aria-controls="navigation">
        <div class="nav-switcher--line"></div>
      </div>

      <a n:href="Homepage:default" class="header__logo">
        <img src="{$baseUrl}/style/{$serverId}/img/logo.svg" alt="{$presenter->config["SERVER_NAME"]} logo">
      </a>

      <div class="header__search">
        {form searchForm class=>'search'}
          <label class="sr-only" for="top-search">Vyhledávání</label>
        {input fulltext 'id'=>'bfulltext', 'value'=>'', 'class'=>'clear_me search__input', 'placeholder'=>"Hledáte pračku? Napište hledaný výraz...", 'autocomplete'=>"off"}
          <button type="submit" class="btn search__submit">
            <i class="fas fa-search"></i>
            <em>Hledat</em>
          </button>

          {include 'hint.latte'}

        {/form searchForm}

        <p class="trending" n:if="false">
          Oblíbeno zákazníky:
          <a href="#">Pračky</a>
          <a href="#">Myčky</a>
          <a href="#">Sauny</a>
          <a href="#">Štípače</a>
          <a href="#">Kuchyňské roboty</a>
        </p>

      </div>

      <div class="header__end" n:snippet="basketHeader">

        <div class="search-toggle">
          <i class="fas fa-search"></i>
        </div>

        {if $userRow->usrid > 0}
          <a href="{plink User:default}" class="login">
            <i class="fas fa-lock"></i>
            <p>{if !empty($userRow->usriname)}{$userRow->usriname} {$userRow->usrilname}{else}{$userRow->usrmail}{/if}</p>
          </a>

        {else}
          <a href="{plink User:login}" class="login">
            <i class="fas fa-lock"></i>
            <p>Přihlášení</p>
          </a>
        {/if}

        <div class="delivery">
            {if $basketOrm->sumPriceForDeliveryLimit() >= $delFreeLimit || $basketDelFree}
              Na tento nákup máte vybranou <strong>dopravu zdarma</strong>.
            {else}
              Nakupte ještě za <strong>{($delFreeLimit-$basketOrm->sumPriceForDeliveryLimit())|formatPrice} <small>{($delFreeLimit-$basketPriceSumForDelFree)|formatPriceEur:$config["EUR_RATE"]|noescape}</small></strong> {include 'toolTip.latte', tTitle:'POZOR!', tText:'Některé produkty se nemusí do limitu na DOPRAVU ZDARMA započítávat. O této skutečnosti budete informováni v košíku. Děkujeme za pochopení.'} <br>
              a máte vybranou <strong>dopravu zdarma</strong>.
            {/if}
        </div>

        {if $basketOrm->sumPrice() > 0}
          <a href="{plink Basket:default}" class="basket">
            <i class="fas fa-shopping-basket"></i>
            <p>V košíku je <strong>{$basketOrm->sumItems()}&nbsp;ks</strong><br>za <strong>{$basketOrm->sumPrice()|formatPrice} <small>{$basketOrm->sumPrice()|formatPriceEur:$config["EUR_RATE"]|noescape}</small></strong></p>
            <span class="basket__count">{$basketOrm->sumItems()}</span>
          </a>
        {else}
          <a href="{plink Basket:default}" class="basket">
            <i class="fas fa-shopping-basket"></i>
            <p>Košík je <strong>prázdný</strong></p>
          </a>
        {/if}
      </div>
    </div>

    <nav class="nav" role="navigation" >
      {include 'menu.latte', rootCatalogs: $rootCatalogs}

      <div class="submenu">
        <ul>
          {default $catRootId=21}
          {foreach $rootCatalogs as $rootCatalog}
            <li><a n:href="Catalog:detail, $rootCatalog->id, $rootCatalog->getURLKey()" {if $rootCatalog->id==$catRootId}class="is-active"{/if}>{$rootCatalog->catname}</a></li>
          {/foreach}
        </ul>
      </div>

    </nav>

  </div>

</header>



<main role="main">
  {snippet flashmessage}
    <div class="container" n:if="count($flashes)">
    {foreach $flashes as $flash}
      <div class="alert alert--{$flash->type==='err'?'danger':$flash->type}">
        <p>{$flash->message}</p>
      </div>
    {/foreach}
    </div>
  {/snippet}

  {block adminMenu}
  {/block}

{include #content}

</main>

<footer class="footer" role="contentinfo">

  <div class="container">

    <div class="row">
      <div class="col col--4">
        <img class="footer__logo" src="{$baseUrl}/style/{$serverId}/img/logo.svg" alt="Shopcom logo"><br><br>
        <a href="https://www.instagram.com/shopcom.cz?utm_source=qr&igsh=MzR3bTZleHNvN2t2" target="_blank"><img src="{$baseUrl}/img/instagram-logo.png" alt="Shopcom instagram" width="100"></a><br><br>
        <a href="https://www.platimpak.cz/" target="_blank"><img src="{$baseUrl}/img/platim-pak-logo.png" alt="PlatimPak" height="100"></a>
      </div>

      <div class="col col--4">
        {foreach $menuFooter1 as $menu}
          {if $iterator->isFirst()}
            <h2>{$menu->parent->menname}</h2>
            <ul>
          {/if}
          <li><a n:href="Page:detail, $menu->page->pagurlkey">{$menu->menname}</a></li>
          {if $iterator->isLast()}
            </ul>
          {/if}
        {/foreach}
      </div>

      <div class="col col--4">
        {foreach $menuFooter2 as $menu}
          {if $iterator->isFirst()}
            <h2>{$menu->parent->menname}</h2>
            <ul>
          {/if}
          <li><a n:href="Page:detail, $menu->page->pagurlkey">{$menu->menname}</a></li>
          {if $iterator->isLast()}
            </ul>
          {/if}
        {/foreach}
      </div>

      <div class="col col--4">
        {foreach $menuFooter3 as $menu}
          {if $iterator->isFirst()}
            <h2>{$menu->parent->menname}</h2>
            <ul>
          {/if}
          <li><a n:href="Page:detail, $menu->page->pagurlkey">{$menu->menname}</a></li>
          {if $iterator->isLast()}
            </ul>
          {/if}
        {/foreach}
      </div>
    </div>

    <hr>

    <p>Copyright © 2000 – {php echo date("Y")} MABIL MOBIL s.r.o., Ostrava - Nová Bělá, Na Šancích 344/70, PSČ 72400, C 51125 vedená u Krajského soudu v Ostravě, IČO:	27760456, DIČ: **********</p>

    <p><small>Tyto internetové stránky používají soubory cookie. Více informací <a href="{$baseUrl}/text-gdpr">zde</a>.<br>Podle zákona o evidenci tržeb je prodávající povinen vystavit kupujícímu účtenku. Zároveň je povinen zaevidovat přijatou tržbu u správce daně online; v případě technického výpadku pak nejpozději do 48 hodin.</small></p>

  </div>

</footer>


<div class="popup ">

  <span class="popup__close js-popup-close">
    <span class="icon icon--times-solid">
      <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <use xlink:href="{$baseUrl}/style/{$serverId}/img/icons.svg#times-solid" x="0" y="0" width="100%" height="100%"></use>
      </svg>
    </span>
  </span>

  {*
  <div class="popup__content">
    <a href="#">
      <img loading="lazy" src="{$baseUrl}/style/{$serverId}/img/popup.png" alt="" width="464" height="225">
    </a>
  </div>
  *}

</div>
 {block scripts}
<script src="{$baseUrl}/style/js/libs/svg4everybody.min.js"></script>
<script>
  svg4everybody();
</script>

  <script src="{$baseUrl}/style/js/libs/jquery-1.12.4.min.js"></script>
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js" integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU=" crossorigin="anonymous"></script>
  <script src="{$baseUrl}/style/js/libs/slick.min.js"></script>
  <script src="{$baseUrl}/style/js/libs/hoverIntent.js"></script>
  <script src="{$baseUrl}/style/js/libs/superfish.min.js"></script>
  <script src="{$baseUrl}/style/js/libs/nouislider.min.js"></script>

  <script src="{$baseUrl}/style/js/scripts.js?v={$appVersion}"></script>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/cookie-bar/cookiebar-latest.min.js?customize=1&tracking=1&thirdparty=1&always=1&refreshPage=1&showPolicyLink=1"></script>

<script>

  {* inicializace ajaxu v nette *}
  $(function () {
    naja.initialize();
  });

</script>

{/block}
{* Heureka cert *}
{if !empty($heureka["IdOverenoZakaznikyCertifikat"])}
  <script type="text/javascript">
    //<![CDATA[
    var _hwq = _hwq || [];
    _hwq.push(['setKey', {$heureka["IdOverenoZakaznikyCertifikat"]}]);_hwq.push(['setTopPos', '220']);_hwq.push(['showWidget', '21']);(function() {
      var ho = document.createElement('script'); ho.type = 'text/javascript'; ho.async = true;
      ho.src = 'https://ssl.heureka.cz/direct/i/gjs.php?n=wdgt&sak='+{$heureka["IdOverenoZakaznikyCertifikat"]};
      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ho, s);
    })();
    //]]>
  </script>
{/if}

{* retargeting zbozi.cz *}
<script type="text/javascript">
  //retargeting zbozi.cz
  /* <![CDATA[ */
  var seznam_retargeting_id = 27307;
  /* ]]> */
</script>
<script type="text/javascript" src="//c.imedia.cz/js/retargeting.js"></script>

  <!-- Kód Google značky pro remarketing -->
  <!--------------------------------------------------
  Značka pro remarketing nesmí být spojena s údaji umožňujícími identifikaci osob nebo umístěna na stránkách týkajících se citlivých kategorií. Další informace a postup nastavení značky naleznete na stránce: http://google.com/ads/remarketingsetup
  --------------------------------------------------->

{if !empty($GLOBALS["ecommProId"])}
  {php
    if (is_array($GLOBALS["ecommProId"])) {
      $GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
    } else {
      $val = $GLOBALS["ecommProId"];
    }
  }
  <script type="text/javascript">
    var google_tag_params = {
      ecomm_prodid: {$val|noescape},
      ecomm_pagetype: {$GLOBALS["ecommPageType"]},
      ecomm_totalvalue: {$GLOBALS["ecommTotalValue"]},
    };
  </script>
{/if}
<script type="text/javascript">
  /* <![CDATA[ */
  var google_conversion_id = 951659273;
  var google_custom_params = window.google_tag_params;
  var google_remarketing_only = true;
  /* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
</script>



{if isset($catalog) && $catalog->root && $catalog->root->id == 13212}
  <div class="modal modal--info" id="xxx-modal" role="dialog" aria-labelledby="dialog-title-1">

    <div class="modal__body">

    <span class="modal__close js-modal-close">
      <span class="icon icon--times-solid">
        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <use xlink:href="/img/icons.svg#times-solid" x="0" y="0" width="100%" height="100%"></use>
        </svg>
      </span>
    </span>

      <div class="modal__content">

        <h2 id="dialog-title-1"> ZBOŽÍ PODLÉHAJÍCÍ VĚKOVÉMU OMEZENÍ</h2>

        <p>Vstupem do tohoto oddělení bezvýhradně souhlasíte a potvrzujete, že jste dosáhli věku 18 let, případně 21 let tam, kde je to vyžadováno zákonem, dále že sem vstupujete dobrovolně a že neumožníte přístup do tohoto oddělení nezletilým osobám.</p>

        <p><button id="xxx-modal-closed" class="btn btn--md  js-modal-close">Souhlasím</button> <a href="/" class="btn btn--md  btn--secondary">Nesouhlasím</a></p>

      </div>

    </div>

  </div>
  <script src="{$baseUrl}/js/js.cookie.js"></script>

  <script type="text/javascript">
    var alerted = Cookies.get('alert_13212');
    if (alerted == null) {
      $("#xxx-modal").show();
    }

    $( "#xxx-modal-closed" ).click(function() {
      Cookies.set('alert_13212', 'yes', { expires: 1/24 });
      $("#xxx-modal").hide();
    });
  </script>
{/if}

<noscript>
  <div style="display:inline;">
    <img height="1" width="1" style="border-style:none;" alt="" src="//googleads.g.doubleclick.net/pagead/viewthroughconversion/951659273/?guid=ON&amp;script=0"/>
  </div>
</noscript>

{block footerScripts}

{/block}

</body>

</html>
