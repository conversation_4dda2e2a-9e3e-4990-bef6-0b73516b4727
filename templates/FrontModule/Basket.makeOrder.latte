{varType App\Orm\Basket $basketOrm}
{varType array $enum_insVarId}
{default $pageTitle = "Doručovací údaje"}
{default $pageRobots = "nofollow,noindex"}

{block content}

    {include 'basketProgress.latte'}
    
    

    <section class="section section--cart">

        <article class="container container--md" role="article">

            <h1 class="cart-headline">{$pageTitle}</h1>

            {form makeOrderForm}
              {include 'formError.latte', form: $form}

                <div class="cart-form">

                    <h2>Status zákazníka</h2>

                    <div class="cart-form__top">
                        <p class="form-checkbox">
                            <input type="radio" n:name="ordcustype:osoba" id="personal" class="js-check-company" checked>
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" for="personal">Soukromá osoba</label>
                        </p>

                        <p class="form-checkbox">
                            <input type="radio"  n:name="ordcustype:firma" id="diffcompany"  class="js-check-company js-check-company-open">
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" for="diffcompany">Firma</label>
                        </p>

                        <p class="form-checkbox">
                            <input type="radio"  n:name="ordcustype:organ" id="difforganization"  class="js-check-company js-check-company-open">
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" for="difforganization">Státní organizace <span>(školy, obce, nemocnice, státní i polostátní firmy, příspěvkové organizace, apod.)</span></label>
                        </p>

                        <div class="cart-form__hidden {if $form["ordcustype"]->getValue()!='firma'&&$form["ordcustype"]->getValue()!='organ'}is-hidden{/if} js-check-data-company">
                            <div class="form-row">
                                <p>
                                    <label n:name="ordic">IČ</label>
                                    <input type="text" n:name="ordic" id="ico_off">
                                </p>

                                <p>
                                    <label n:name="orddic">DIČ</label>
                                    <input type="text" n:name="orddic">
                                </p>
                            </div>

                            <div class="form-row">
                                <p>
                                    <label n:name="ordifirname">Název firmy</label>
                                    <input type="text" n:name="ordifirname">
                                </p>

                                <p>
                                    <label n:name="ordcodecus">Kód objednávky zákazníka</label>
                                    <input type="text" n:name="ordcodecus">
                                </p>
                            </div>

                            <p class="form-checkbox">
                                <input type="checkbox" n:name="ordusrvat">
                                <span class="form-checkbox__box"></span>
                                <label class="form-label"  n:name="ordusrvat">Jsem plátce DPH</label>
                            </p>

                        </div>

                    </div>

                    <h2>Kontaktní údaje</h2>

                    <div class="form-row">
                        <p>
                            {label ordiname class=>"is-required" /}
                            {input ordiname required=>"required"}
                        </p>

                        <p>
                            {label ordilname class=>"is-required" /}
                            {input ordilname required=>"required"}
                        </p>

                    </div>

                    <div class="form-row">

                        <div class="form-row__item">
                            {label ordtel class=>"is-required" /}
                            <div class="form-row__phone">
                              <div class="custom-select">
                              {input telprefix required=>"required"}
                              </div>
                              {input ordtel, data-adultocz => 'customer_phone', required=>"required"}
                            </div>
                        </div>

                        <p>
                            {label ordmail class=>"is-required" /}
                            {input ordmail, data-adultocz => 'customer_email', required=>"required"}
                        </p>
                    </div>


                    {if $withoutAddress}
                    <div id="addAddressTable" class="form-row">
                        <p class="form-checkbox">
                            <input type="checkbox" n:name="address">
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" n:name="address">Chci doplnit adresu</label>
                        </p>
                    </div>
                    {/if}

                    <div id="addressTable">
                    <div class="form-row">
                        <p>
                            {label ordistreet class=>"is-required" /}
                            {input ordistreet required=>"required"}
                        </p>

                        <p>
                            {label ordistreetno class=>"is-required" /}
                            {input ordistreetno required=>"required"}
                        </p>
                    </div>

                    <div class="form-row">
                        <p>
                            {label ordicity class=>"is-required" /}
                            {input ordicity required=>"required"}
                        </p>

                        <p>
                            {label ordipostcode class=>"is-required" /}
                            {input ordipostcode required=>"required"}
                        </p>
                    </div>

                    <div class="form-row">
                        <p>
                            Země
                            {$enum_delcouid[$couId]}
                        </p>
                    </div>
                    </div>

                    <div class="form-row">
                      <p>
                          {label ordnote /}
                          {input ordnote}
                      </p>
                    </div>

                    {if !$withoutAddress}
                    <div class="cart-form__bottom">
                    {if isset($form["shipto"])}
                        <p class="form-checkbox">
                            <input type="checkbox" n:name="shipto" id="diffaddress" class="js-check">
                            <span class="form-checkbox__box"></span>
                            <label class="form-label" for="diffaddress">Chci doručit na jinou adresu</label>
                        </p>


                        <div class="cart-form__hidden is-hidden js-check-data"{if $form["shipto"]->getValue()} style="display: block;"{/if}>
                            <div class="form-row">
                                <p>
                                    {label ordstname class=>"is-required" /}
                                    {input ordstname required=>"required"}
                                </p>

                                <p>
                                    {label ordstlname class=>"is-required" /}
                                    {input ordstlname required=>"required"}
                                </p>
                            </div>

                            <div class="form-row">

                              <p>
                                  {label ordststreet class=>"is-required" /}
                                  {input ordststreet required=>"required"}
                              </p>

                              <p>
                                  {label ordststreetno class=>"is-required" /}
                                  {input ordststreetno required=>"required"}
                              </p>
                            </div>

                            <div class="form-row">
                              <p>
                                  {label ordstcity class=>"is-required" /}
                                  {input ordstcity required=>"required"}
                              </p>

                              <p>
                                  {label ordstpostcode class=>"is-required" /}
                                  {input ordstpostcode required=>"required"}
                              </p>
                            </div>

                            <div class="form-row">

                                <p>
                                    Země
                                    {$enum_delcouid[$couId]}
                                </p>

                            </div>
                        </div>
                      {/if}
                    </div>
                    {/if}

                    {if isset($form['orditype'])}
                    <div class="form-row">
                      <p>
                        {label ordibirthdate /}
                        {input ordibirthdate}<small>{_'Vyplňte ve tvaru den.měsíc.rok. Musíte být starší 18-ti let.'}</small>
                      </p>

                      <p>
                        {label orditype /}
                        {input orditype}
                      </p>
                    </div>
                    {/if}
                    
                     {*JEN PRO eJuicy VENDOR*}
                    
                    {var $adultcheck = 41}
                    {foreach $basketOrm->basketItems as $item}
                     
											{var $name = $item->product->name}
											
                    	{ifset $productAdultFlags[$item->product->id]}
							            {if $productAdultFlags[$item->product->id]}
							                {var $adultcheck = 420}
							            {/if}
							        {/ifset}

    									
    								{/foreach}
    								<!--aq{$adultcheck}-->
                    {if $adultcheck==420}
                    <div class="form-row">
                    		<div class="form-row__item">
												<p>
												<input type="hidden" name="customer_age" value="18" data-adultocz="customer_age" />
												
												
												
												{if $presenter->config["SERVER_NAMESHORT"]=="shopcom.cz"}
												<!--shopcom-->
												<div class="adulto-cz" data-sitekey="97RPNK4B"></div>
												{else}
												<!--scshop-->
												<div class="adulto-cz" data-sitekey="TBFSXK6L"></div>
												{/if}
												<script async src="https://api.js.m2a.cz/api.js"></script>
												{*<script async src="https://sandbox.adulto.cz/api.js"></script>*}

												</p>
												</div>
										</div>
                    {/if}
                    {if !$identity->isLoggedIn()}
                    <div class="form-row antispam">
                      <p>
                        {label antispam /}
                        {input antispam}<small>{_'test proti robotum'}</small>
                      </p>
                    </div>
                    {/if}

                </div>

                <div class="cart-products">

                      <ul aria-hidden="true">
                        <li class="cart-product__top-name">Produkt</li>
                        <li class="cart-products__top-amount">Počet kusů</li>
                        <li class="cart-products__top-stock">Dostupnost</li>
                        <li class="cart-products__top-price">Cena za kus</li>
                        <li class="cart-products__top-price-total">Celková cena</li>
                      </ul>

                      <ol>
                        {foreach $basketOrm->basketItems as $item}
	                        {*
	                            $giftdisc = 0;
                            if (isset($basket->ordgifts[$id]["discount"]) && $basket->ordgifts[$id]["discount"]) {
                              $giftdisc = $productRows[$id]->progiftdisc;
                            }
                          *}

                          <li>
                            <dl>
                              <dt class="sr-only">Produkt</dt>
                              <dd class="cart-products__overview">
                                  <img src="{$baseUrl}/{$item->product->getImage()->getPathList()}" alt="{$item->product->name}">
                                  <a href="{plink Product:detail, $item->product->id, $item->product->getUrlKey()}"><strong>{$item->product->name}</strong></a>
                                  {if $item->giftType===App\Orm\BasketItem::GIFT_DISCOUNT}
                                  <br>
                                  <strong class="color-green">+ sleva {$item->product->getDiscount() * $item->qty|formatPrice}</strong>
                                  {elseif $item->giftType===App\Orm\BasketItem::GIFT_REJECTED}
                                    <br><span class="basket__gift">dárek jste u této položky odmítl/a</span>
                                  {else}
                                        {foreach $item->getGifts() as $gift}
                                            {if $iterator->first}
                                                <br><span class="basket__gift">+ dárek:
                                          {/if}
                                                              {$gift->name}
                                                          {if !$iterator->last}, {else}</span>{/if}
                                    {/foreach}
                                    {/if}
                              </dd>

                              <dt class="sr-only">Počet kusů</dt>
                              <dd class="cart-products__count">
                                {$item->qty}
                              </dd>

                              <dt class="sr-only">Dostupnost</dt>
                              <dd class="cart-products__stock"> {$item->product->getAccessText(enum_proaccess:$enum_proaccess)}</dd>

                              <dt class="sr-only">Celková cena</dt>
                              <dd class="cart-products__price">
                                  <strong>{$item->getProductPrice(true)|formatPrice}</strong>
                                <br>
                                {*<small>{$productRows[$id]|formatPriceNoVat} < / small >*}
                              </dd>

                              <dt class="sr-only">Celková cena</dt>
                              <dd class="cart-products__price-total">
                                 <strong>{$item->getProductPrice()|formatPrice}</strong>
                                <br>
                                {*<small>999 999 Kč bez DPH</small>*}
                              </dd>

                            </dl>

                            <div class="cart-products__additional">
                              <p class="cart-products__additional-text color-info">
                                <strong>

                                    {if isset($item->serviceGuarantee)}
                                      <br>+ {App\Orm\Service::NAME_TYPE[App\Orm\Service::TYPE_GUARANTEE]} {$enum_insVarId[App\Orm\Service::TYPE_GUARANTEE][$item->serviceGuarantee->variant]}
                                      {$item->serviceGuarantee->priceServiceSell|formatPrice}/kus
                                    {/if}
                                    {if isset($item->serviceInsurance)}
                                        <br>+ {App\Orm\Service::NAME_TYPE[App\Orm\Service::TYPE_INSURANCE]} {$enum_insVarId[App\Orm\Service::TYPE_INSURANCE][$item->serviceInsurance->variant]}
                                        {$item->serviceInsurance->priceServiceSell|formatPrice}/kus
                                    {/if}

                                  {if $item->orderService}
                                    <br>+ {$item->orderService->name} {$item->orderService->price|formatPrice} <br>
                                  {/if}
                                </strong>
                              </p>
                            </div>
                          </li>

                        {/foreach}
                        <li>
                            <dl>
                              <dt class="sr-only">Doprava</dt>
                              <dd class="cart-products__overview">
                                <strong>Doprava: {$basketOrm->delivery->delname} {if !empty($delNameAdd)}<small> {$delNameAdd}</small>{/if}<br>Platba: {$basketOrm->payment->delname}</strong>
                              </dd>

                              <dt class="sr-only"></dt>
                              <dd class="cart-products__count">

                              </dd>

                              <dt class="sr-only"></dt>
                              <dd class="cart-products__stock"></dd>

                              <dt class="sr-only"></dt>
                              <dd class="cart-products__price">
                              </dd>

                              <dt class="sr-only">Celková cena</dt>
                              <dd class="cart-products__price-total">
                                {var  $delPrice = $basketOrm->deliveryPrice() +  $basketOrm->paymentPrice()}
                                <strong>
                                    {if $basketOrm->delivery->delspecdel===1}
                                    Cena bude upřesněna
                                    {elseif $delPrice > 0}
                                    {$delPrice|formatPrice} {$delPrice|formatPriceEur:$config["EUR_RATE"]|noescape}
                                    {else}ZDARMA
                                    {/if}</strong>
                              </dd>

                            </dl>

                          </li>
                      </ol>

                    </div>
                <div class="coupon-info" n:if="$basketOrm->coupon">
                    <h2 style="">{$basketOrm->coupon->textInfo()} <span style="font-size: 1rem;">{$basketOrm->coupon->textInfoOn()}</span></h2>
                    <dl>
                        {if $basketOrm->sumPrice()-$basketOrm->sumPrice(sale:false)==0}
                            <dt>Sleva se nevztahuje na žadný produkt v košíku.</dt>
                        {else}
                            <dt>Sleva s DPH:</dt>
                            <dd class="cart-sumprice__vatinclude">{$basketOrm->sumPrice()-$basketOrm->sumPrice(sale:false)|formatPrice}</dd>
                        {/if}
                    </dl>
                </div>

                <div class="cart-sumprice">

                    <h2>Celková cena za zboží</h2>

                    <dl>
                        {*
                        <dt>Cena bez DPH:</dt>
                        <dd class="cart-sumprice__vatless">{$basket->priceSumTotal|formatPrice}</dd>
                        *}
                        <dt>Celková cena s DPH:</dt>
                        <dd class="cart-sumprice__vatinclude">{$basketOrm->sumTotalPrice()|formatPrice}</dd>
                    </dl>

                </div>

                {ifset $form['maillist']}
                       <p class="form-checkbox">
                           <input type="checkbox" n:name="maillist">
                           <span class="form-checkbox__box"></span>
                           <label class="form-label" n:name="maillist">Souhlasím se zasíláním slevových kupónů a newsletterů a vyjadřuji tímto souhlas s <a href="/text-gdpr" target="_blank">podmínkami zpracování osobních údajů</a>.</label>
                       </p>
                    {/ifset}
                    <p class="form-checkbox">
                       <input type="checkbox" n:name="ordheurekagdpr">
                       <span class="form-checkbox__box"></span>
                       <label class="form-label" n:name="ordheurekagdpr">Nesouhlasím se zasláním dotazníku spokojenosti v rámci programu Ověřeno zákazníky.</label>
                    </p>

                    <p>Pokud jste vybrali <strong>platbu na splátky</strong> budete po odeslání objednávky přesměrování na platební bránu/stránky úvěrové společnosti.</p>

                <p><strong>Odesláním objednávky a stisknutím tlačítka "Objednat (objednávka zavazující k platbě)" potvrzuji, že jsem se seznámil s
                  <a href="{$baseUrl}/text-obchodni-podminky" target="_blank">obchodními podmínkami</a> a <a href="{$baseUrl}/text-reklamacni-rad" target="_blank">reklamačním řádem</a> internetového obchodu {$presenter->config["SERVER_NAMESHORT"]} a
                  <a href="{$baseUrl}/text-gdpr" target="_blank">zásadami ochrany osobních údajů.</a>
                  </strong>
                </p>

                <nav class="cart-pagination" role="navigation" aria-label="Nákupní košík">
                  <p><a href="{plink Basket:ordDelMode}" class="btn btn--txt"><i class="fas fa-caret-left"></i>Zpět na výběr dopravy</a></>
                  <p>
                    <button n:name="submit" class="btn" >Objednat (objednávka zavazující k platbě)<i class="fas fa-caret-right"></i></button>
                  </p>
                </nav>

            {/form}
        </article>

    </section>

{/block}

{block footerScripts}
<script>
    {if $userRow->usrid === 0}
    $('#ord_antispam').val({$config["ANTISPAM_NO"]|noescape}).closest('.antispam').hide();
    {/if}
</script>
{/block}