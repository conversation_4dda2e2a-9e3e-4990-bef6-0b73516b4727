{varType App\Orm\Product $product}
{varType ?App\Orm\Coupon $coupon}
{* TODO add tag CACHE *}

{default $classAdd=""}
{default $basketAddQty=1}

<div class="product {$classAdd}">

    <div class="product__top">

        <div class="product__name">
            <a n:href="Product:detail, $product->id, $product->getUrlKey()" title="{$product->name}">{$product->name}</a>
        </div>

        {include 'productTagListORM.latte', product:$product}

    </div>

    <a  n:href="Product:detail, $product->id, $product->getUrlKey()"  class="product__image">

        <img src="{$baseUrl}{$product->getImage()->getPathDetail()}" width="280" height="260" alt="{$product->name}">

        {*
        <div class="product__badge-list">
          <img src="{$baseUrl}/style/{$serverId}/img/from-tv.png" loading="lazy" alt="">
          <img src="{$baseUrl}/style/{$serverId}/img/3v1.png" loading="lazy" alt="">
          <img src="{$baseUrl}/style/{$serverId}/img/product-2019.png" loading="lazy" alt="">
        </div>
        *}

    </a>

    <div class="product__bottom">

        <div class="stock">{$product->getAccessText($presenter->web,$enum_proaccess)}</div>

        <div class="product__desc">


            {if isset($coupon)}
                <div class="action">
                    <div class="action-col">
                        <p>Cena s kupónem {$coupon->code} <br><span class="action-price "><strong>{$product->getPrice()-$coupon->calculateSale($product)|formatPrice}{$coupon->calculateSale($product)|formatPriceEur:$config["EUR_RATE"]|noescape}</strong></span></p>
                    </div>
                    <div class="action-col">
                        <p>SLEVA <br><strong>{$coupon->campaign->val}{=$coupon->campaign::VALTEXT[$coupon->campaign->type]}</strong></p>
                    </div>
                </div>
            {elseif  FALSE} {* slevy vypnuty *}
                <div class="action">
                    <div class="action-col">
                        <p>CENA DŘÍVE <br><span class="action-price">{$product->getPrice()|formatPrice}{$product->getPrice()|formatPriceEur:$config["EUR_RATE"]|noescape}</span></p>
                    </div>
                    <div class="action-col">
                        <p>SLEVA <br><strong>{$product->getPrice(App\Orm\Product::PRICE_COM)|getDiscountInPer:$product->getPrice(App\Orm\Product::PRICE_E)}</strong></p>
                    </div>
                </div>
            {else}
            <p >{$product->descriptionShort!=''?$product->descriptionShort:($product->description ?? '')|stripHtml}</p>
            {/if}
        </div>

        <div class="product__buy">
            <p n:class="isset($coupon)? product__priceOnBannerSale,product__price">{$product->getPrice(App\Orm\Product::PRICE_E)*$basketAddQty|formatPrice}{$product->getPrice(App\Orm\Product::PRICE_E)*$basketAddQty|formatPriceEur:$config["EUR_RATE"]|noescape}</p>
            <span n:if="$classAdd!=''" style="font-weight: bold">
                {$basketAddQty}&nbsp;ks
            </span>
            {if $classAdd!='product--basket'&&$product->canBuy()}
                <form action="{plink basketAdd! $product->id}" method="get" class="ajax product__buy-control">
              <span class="form-count">
                <div class="form-count__control">
                  <div class="form-count__plus">&plus;</div>
                  <div class="form-count__minus">&minus;</div>
                </div>
                <label class="sr-only" for="name">Množství</label>
                <input type="text" name="qty" min="0" max="999" value="1">
              </span>
                    <button type="submit" class="product__buy-btn btn"><i class="fas fa-shopping-basket"></i></button>
                </form>
            {/if}
        </div>

    </div>
</div>