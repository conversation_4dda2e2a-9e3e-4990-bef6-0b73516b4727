{default pageTitle => ""}

{layout null}

{block #content}

<head><link rel="stylesheet" href="{$baseUrl}/css/{$serverId}/styles.css?v4" type="text/css"></head>

<form action="" method="get">
  <input type="text" name="s" size="60" value="{$s}">
  <input type="submit" name="submit">
</form>

{if count($catRows) > 0}
  <h3>Katalog</h3>
  {foreach $ids["sccom_catalogs"] as $item}
    {if $iterator->first}
    <table class="table table-condensed table-hover table-bordered">
      <tr>
        <th>Katalogová cesta</th>
        <th>Popis</th>
        <th>score</th>
        <th></th>
        <th></th>
      </tr>
    {/if}
    {if isset($catRows[$item["_id"]])}
      <tr>
        <td>{$catRows[$item["_id"]]->catpath}</td>
        <td>{strip_tags($catRows[$item["_id"]]->catdesc)|truncate:150}</td>
        <td>{$item["_score"]}</td>
        <td><pre>{print_r($item["_source"], true)}</pre></td>
        <td><a href="{plink Catalog:detail, $catRows[$item["_id"]]->catid, ""}">detail</td>
      </tr>
    {/if}
    {if $iterator->last}
    </table>
    {/if}
  {/foreach}
{/if}

{if count($proRows) > 0}
  <h3>Produkty</h3>
  {foreach $ids["sccom_products"] as $item}
    {if $iterator->first}
    <table class="table table-condensed table-hover table-bordered">
      <tr>
        <th>Kód</th>
        <th>Název</th>
        <th>Cena shopcom.cz</th>
        <th>Dostupnost</th>
        <th>score</th>
        <th></th>
        <th></th>
      </tr>
    {/if}
      {if isset($proRows[$item["_id"]])}
      <tr>
        <td>{$proRows[$item["_id"]]->procode}</td>
        <td>{$proRows[$item["_id"]]->proname}</td>
        <td>{$proRows[$item["_id"]]->propricea_web2}</td>
        <td>{$proRows[$item["_id"]]->proaccess_web2}</td>
        <td>{$item["_score"]}</td>
        <td><pre>{print_r($item["_source"], true)}</pre></td>
        <td><a href="{plink Product:detail, $proRows[$item["_id"]]->proid, ""}">detail</td>
      </tr>
      {/if}
    {if $iterator->last}
    </table>
    {/if}
  {/foreach}
{/if}