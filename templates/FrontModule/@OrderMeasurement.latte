{* Google Analytics eComerce *}
{php
$dphSum = $order->ordpriceinclvat - $order->ordpricenovat;
}
<script>
eshopDataLayer = window.dataLayer || [];
eshopDataLayer.push({ ecommerce: null });  // Clear the previous ecommerce object.
eshopDataLayer.push({
  "event":"purchase",
  "enhanced_conversion_data":{
    "email": {$order->ordmail},
    "phone_number": {$order->ordtel},
    "first_name": {$order->ordiname},
    "last_name": {$order->ordilname},
    "home_address":{
      "street": {$order->ordistreet.$order->ordistreetno},
      "city": {$order->ordicity},
      "postal_code": {$order->ordipostcode},
    }
  },
  "ecommerce":{
    "currencyCode":'CZK',
    "purchase":{
      "actionField":{
        "id": {$order->ordcode},
        "revenue": {$order->ordpricevat},
        "shipping": {$order->orddelprice}
      },
      "products": [
        {foreach $order->items as $gRow}
        {
          "id": {$gRow->oriproid},
          "name": {$gRow->oriname},
          "price": {$gRow->oriprice},
          "quantity": {$gRow->oriqty}
        }{if !$iterator->isLast()},{/if}
        {/foreach}
      ]
    }
  }
});

dataLayer.push({ ecommerce: null });  // Clear the previous ecommerce object.
dataLayer.push({
  "event":"purchase",
  "enhanced_conversion_data":{
    "email": {$order->ordmail},
    "phone_number": {$order->ordtel},
    "first_name": {$order->ordiname},
    "last_name": {$order->ordilname},
    "home_address":{
      "street": {$order->ordistreet.$order->ordistreetno},
      "city": {$order->ordicity},
      "postal_code": {$order->ordipostcode},
    }
  },
  "ecommerce":{
    "currencyCode":'CZK',
    "purchase":{
      "actionField":{
        "id": {$order->ordcode},
        "revenue": {$order->ordpricevat},
        "shipping": {$order->orddelprice}
      },
      "products": [
        {foreach $order->items as $gRow}
        {
          "id": {$gRow->oriproid},
          "name": {$gRow->oriname},
          "price": {$gRow->oriprice},
          "quantity": {$gRow->oriqty}
        }{if !$iterator->isLast()},{/if}
        {/foreach}
      ]
    }
  }
});
</script>

{* Heureka konverze PPC *}
<!-- Heureka.cz THANK YOU PAGE script -->
<script>
    (function(t, r, a, c, k, i, n, g) {
        t['ROIDataObject'] = k;
        t[k] = t[k] || function() {
            (t[k].q = t[k].q || []).push(arguments)
        },
            t[k].c = i;
        n = r.createElement(a),
            g = r.getElementsByTagName(a)[0];
        n.async = 1;
        n.src = c;
        g.parentNode.insertBefore(n, g)
    })(window, document, 'script', '//www.heureka.cz/ocm/sdk.js?version=2&page=thank_you', 'heureka', 'cz');

    heureka('authenticate', '0a6230387345182349d6dbf6e71d28fa1934');

    heureka('set_order_id', {$order->ordcode});
    {php $sumPrice = 0}
    {foreach $order->items as $row}
    {php $sumPrice += round($row->oriprice) * $row->oriqty}
    heureka('add_product', {$row->procode}, {$row->oriname}, {round($row->oriprice)}, {$row->oriqty});
    {/foreach}
    heureka('set_total_vat', {$sumPrice});
    heureka('set_currency', 'CZK');
    heureka('send', 'Order');
</script>
<!-- End Heureka.cz THANK YOU PAGE script -->

{* srovnáme.cz Tracking pixel *}
{if !empty($presenter->config["SROVNAMECZ"]["KeyMereniKonverzi"])}
<script>
    srovname('event', 'purchase', {
        'value': {$order->ordpricevat},
        'currency': 'CZK',
        'transaction_id': {$order->ordid},
        'items': [
            {foreach $order->items as $row}
            {
                'id': {$row->procode},
                'name': {$row->oriname},
                'quantity': {$row->oriqty},
                'price': {$row->oriprice}
            },
            {/foreach}
        ]
    });
</script>
{/if}

<!-- Google Code for Objednavka sccom Conversion Page -->
<script type="text/javascript">
    /* <![CDATA[ */
    var google_conversion_id = 951659273;
    var google_conversion_language = "en";
    var google_conversion_format = "3";
    var google_conversion_color = "ffffff";
    var google_conversion_label = "xNF-CIrpn2wQidbkxQM";
    var google_conversion_value = {$order->ordpricevat};
    var google_conversion_currency = "CZK";
    var google_remarketing_only = false;
    /* ]]> */
</script>
<script type="text/javascript" src="//www.googleadservices.com/pagead/conversion.js">
</script>
<noscript>
  <div style="display:inline;">
    <img height="1" width="1" style="border-style:none;" alt="" src="//www.googleadservices.com/pagead/conversion/951659273/?value={$order->ordpricevat}&amp;currency_code=CZK&amp;label=xNF-CIrpn2wQidbkxQM&amp;guid=ON&amp;script=0"/>
  </div>
</noscript>