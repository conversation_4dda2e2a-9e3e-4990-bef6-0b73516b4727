{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>
{foreach $rows as $row}
<SHOPITEM>
<PRODUCT>{if !empty($row->pronames)}{$row->pronames}{else}{$row->proname}{/if}</PRODUCT>
<DESCRIPTION>{$row->prodescs}</DESCRIPTION>
<URL>{plink //:Front:Product:detail, $row->proid, getProKey($row)}</URL>
<ITEM_TYPE>new</ITEM_TYPE>
<DELIVERY_DATE>1</DELIVERY_DATE>
<IMGURL>{$baseUrl}/{getPicName($row, 'detail')}</IMGURL>
<PRICE_VAT>{$row->proprice}</PRICE_VAT>
</SHOPITEM>
{/foreach}
</SHOP>