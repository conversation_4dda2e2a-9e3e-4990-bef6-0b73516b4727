{varType App\Orm\Catalog[] $catalogs}
{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<CATEGORIES>
  {foreach $catalogs as $catalog}
    {php
      $catPath = str_replace('|', ' | ', $catalog->catpath);
    }
    <CATEGORY>
        <TITLE>{$catalog->catname}</TITLE>
        <URL>{plink //:Front:Catalog:detail, $catalog->id, $catalog->getURLKey()}</URL>
        <DESCRIPTION>{$catalog->catdesc}</DESCRIPTION>
        <IMAGE n:if="$catalog->getImagSrc()">{$baseUrl}{$catalog->getImagSrc()}</IMAGE>
        <PATH>{$catPath}</PATH>
        <PRODUCTS>{$catalog->countProduct}</PRODUCTS>
    </CATEGORY>
  {/foreach}
</CATEGORIES>