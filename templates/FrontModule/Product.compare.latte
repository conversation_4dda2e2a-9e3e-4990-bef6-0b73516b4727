{default $pageTitle       = $presenter->translator->translate('Porovnání zboží')}

{block #content}
<div class="product-compare">

  <h1><span>{$pageTitle}</span></h1>

  {block #crumb}
  <div id="crumb" class="breadcrumbs">
    <p><a href="{$baseUrl}">Úvod</a>
       <span>/</span>
       {$pageTitle}
    </p>
  </div>
  {/block}

  {if count($products) > 0}
  <table>
  <tr>
    <th></th>
  {foreach $products as $pro}
    <th>
      <strong>{$pro->proname}</strong><br>
      <a href="{plink Product:detail, $pro->proid, getProKey($pro)}">
      <img src="{$baseUrl}/{getPicName($pro, 'product/list')}" alt="{$pro->proname}">
      </a><br />
      <strong>Cena: {$pro->proprice|formatPrice}</strong><br /><br />
      <a href="{plink Product:detail, $pro->proid, getProKey($pro)}">Detail zboží</a> | <a href="{plink Product:compareDelete, $pro->proid}">Odstranit z porovnávání</a>
    </th>
  {/foreach}
  </tr>
  {foreach $paramsAll as $row}
  <tr>
    <th>{$row->prpname}</th>
    {foreach $products as $pro}
    <td>{ifset $pro["params"][$row->prpname]["prpvalue"]}{$pro["params"][$row->prpname]["prpvalue"]}{/ifset}</td>
    {/foreach}
  </tr>
  {/foreach}

  </table>
  {else}
  <p>Vyberte nejprve nějaké zboží k porovnání.</p>
  {/if}
</div>
{/block}
