{varType App\Orm\Product[] $productsOrm}
{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP>
{foreach $productsOrm as $product}
<SHOPITEM>
<ITEM_ID>{$product->code}</ITEM_ID>
<MANUFACTURER>{$product->manufacturer->name}</MANUFACTURER>
{if strlen($product->code2) == 13}<EAN>{$product->code2}</EAN>{/if}
<PRODUCTNAME>{$product->name}</PRODUCTNAME>
<PRODUCT>{$product->name}{if !empty($product->nameadd)} {$product->pronameadd}{/if}</PRODUCT>
<DESCRIPTION>{$product->descriptionShort}</DESCRIPTION>
<URL>{plink //:Front:Product:detail, $product->id, $product->getUrlKey()}</URL>
<DELIVERY_DATE>{$product->getAccess()}</DELIVERY_DATE>
<IMGURL>{$baseUrl}/{$product->getImage()->getPathDetail()}</IMGURL>
<PRICE_VAT>{$product->price}</PRICE_VAT>
</SHOPITEM>
{/foreach}
</SHOP>