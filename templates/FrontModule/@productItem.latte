
      {php $isSalable = isSalable($product); }

      <li class="product">
        <div class="product__wrapper">
          <div class="product__image"><a href="{plink Product:detail, $product->proid, getProKey($product)}"><img src="{$baseUrl}/{getPicName($product, 'product/list')}" alt="{$product->proname}"></a></div>
          <div class="product__badges">
            {if isDelFree($product, $delFreeLimitByVendor)}<span class="badge badge--2">Doprava zdarma</span>{/if}
            {if haveGift($product, $catalogGifts)}<span class="badge badge--8">D<PERSON><PERSON></span>{/if}
            {if $product->protypid}<span class="badge badge--4">Ak<PERSON><PERSON><PERSON> cena</span>{/if}
            {if $product->protypid2}<span class="badge badge--3">Novinka</span>{/if}
            {if $product->protypid3}<span class="badge badge--5">Tip</span>{/if}
            {if $product->protypid4}<span class="badge badge--6">Nejprodávanější</span>{/if}
            {if $product->protypid5}<span class="badge badge--7">Výprodej</span>{/if}
            {if $product->protypid6}<span class="badge badge--6">Proplácí pojišťovna</span>{/if}
            {if isset($discGifts) && isDiscount($product,$discGifts)}<span class="badge badge--4">Sleva</span><br>{/if}
          </div>
          <h3 class="product__header"><a href="{plink Product:detail, $product->proid, getProKey($product)}">{$product->proname|myTruncate:75}</a></h3>
          {if ($serverId == 'web2' || $serverId == 'web3')}<span class="product__stock">{if $isSalable}{$product|getProAccessText:$enum_proaccess}{else}Nedostupné{/if}</span>{/if}
          <span class="product__price">
            {$product->proprice|formatPrice}{$product->proprice|formatPriceEur:$config["EUR_RATE"]|noescape}
            {if $product->proqty > 0}{/if}
          </span>
          {if $isSalable}
            <form action="{plink basketAdd! $product->proid}" method="get" class="ajax product__form">
            <span class="control--count product__input"><input type="number" name="qty" min="0" max="999" value="1"></span>
              <input type="submit" class="product__buy product__buy--single" value="&nbsp;">
            </form>
          {else}
            <a href="{plink Product:detail, $product->proid, getProKey($product)}" class="product__buy">DETAIL</a>
          {/if}
        </div>
