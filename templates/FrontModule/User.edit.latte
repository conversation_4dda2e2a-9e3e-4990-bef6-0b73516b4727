{default $pageTitle       = $presenter->translator->translate('Upravit osobní údaje')}
{default $pageRobots      = "nofollow,noindex"}

{block #content}

  <section class="section section--login">

    <article class="container" role="article">
      <div class="row row--start">

        <div class="content-nav">
          <ul>
            <li><a href="{plink default}">M<PERSON><PERSON></a></li>
            <li><a href="{plink edit}" class="is-active">Upravit údaje / změnit heslo</a></li>
            <li><a href="{plink logout}">Odhlásit se</a></li>
          </ul>

        </div>

        <div class="col col--2 col--start col--grow">

          <h1>Upravit údaje / změnit heslo</h1>

          {form userEditForm}
            {include 'formError.latte', form: $form}

            <p class="form-set__row">
              {label usrmail class=>"is-required" /}
              {input usrmail required=>'required', autocomplete=>"email"}
            </p>

            <p class="form-set__row">
              {label usrtel /}
              {input usrtel}
            </p>

            <hr>
            <h2>Fakturační adresa</h2>

            <p class="form-set__row">
              {label usriname /}
              {input usriname}
            </p>

            <p class="form-set__row">
              {label usrilname /}
              {input usrilname}
            </p>

            <p class="form-set__row">
              {label usrifirname /}
              {input usrifirname}
            </p>

            <p class="form-set__row">
              {label usristreet /}
              {input usristreet}
            </p>

            <p class="form-set__row">
              {label usristreetno /}
              {input usristreetno}
            </p>

            <p class="form-set__row">
              {label usricity /}
              {input usricity}
            </p>

            <p class="form-set__row">
              {label usripostcode /}
              {input usripostcode}
            </p>

            <hr>
            <h2>Adresa dodání</h2>

            <p class="form-set__row">
              {label usrstname /}
              {input usrstname}
            </p>

            <p class="form-set__row">
              {label usrstlname /}
              {input usrstlname}
            </p>

            <p class="form-set__row">
              {label usrstfirname /}
              {input usrstfirname}
            </p>

            <p class="form-set__row">
              {label usrststreet /}
              {input usrststreet}
            </p>

            <p class="form-set__row">
              {label usrststreetno /}
              {input usrststreetno}
            </p>

            <p class="form-set__row">
              {label usrstcity /}
              {input usrstcity}
            </p>

            <p class="form-set__row">
              {label usrstpostcode /}
              {input usrstpostcode}
            </p>

            <hr>

            <h2>Změna hesla</h2>
            <p class="form-set__row">
              {label usrpassw_old /}
              {input usrpassw_old}
            </p>
            <p class="form-set__row">
              {label usrpassw /}
              {input usrpassw}
            </p>
            <p class="form-set__row">
              {label usrpassw2 /}
              {input usrpassw2}
            </p>

            <hr>

            <p class="form-checkbox">
              {input usrmaillist:input}
              <span class="form-checkbox__box"></span>
              {label usrmaillist:label, class:'form-label' /}
            </p>

            {if isset($form['usrmaillist2'])}
              <p class="form-checkbox">
                {input usrmaillist2:input}
              <span class="form-checkbox__box"></span>
              {label usrmaillist2:label, class:'form-label' /}
              </p>
            {/if}

            {input save  class=>"btn btn--md"}
          {/form}

        </div>

      </div>

    </article>

  </section>

{/block}