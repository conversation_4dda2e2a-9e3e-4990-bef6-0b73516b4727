{ifset $basketAdd}


    <div class="modal is-open" id="modal-basket" role="dialog" aria-labelledby="dialog-title-5">

        <div class="modal__body">

    <span class="modal__close js-modal-close">
      <span class="icon icon--times-solid">
        <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <use xlink:href="{$baseUrl}/style/{$serverId}/img/icons.svg#times-solid" x="0" y="0" width="100%" height="100%"></use>
        </svg>
      </span>
    </span>

            <div class="modal__content">

                <h2 class="center" id="dialog-title-5"><i class="fas fa-check-circle"></i> Zboží vloženo do košíku</h2>

                {include 'productItem.latte', product:$basketAdd, classAdd:'product--basket'}

      <p class="modal__control">
                        <button class="btn btn--txt js-modal-close"><i class="fas fa-caret-left"></i>Zpět do obchodu</button>
                        <a href="{plink Basket:default}" class="btn">Zobrazit košík<i class="fas fa-shopping-basket"></i></a>
      </p>

      {if is_array($basketAccessories) && count($basketAccessories) > 0}
                      <hr>

                      <div class="modal__stripe">
                          <h3 class="center">Doporučujeme přikoupit</h3>
                      </div>

                      <div class="row row--start product-slick-modal">

                          {foreach $basketAccessories as $row}
                              <div class="col col--4">
                                  {include 'productItem.latte', product:$row}
                              </div>
                          {/foreach}

                      </div>
                {/if}
            </div>

        </div>

    </div>

{/ifset}
