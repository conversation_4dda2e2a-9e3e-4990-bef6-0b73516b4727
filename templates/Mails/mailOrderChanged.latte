
{* Objednáno u dodavatele *}
{if $mailStatus == 11}
<p><PERSON><PERSON><PERSON><PERSON>ý zákazníku,<br>
informuje<PERSON> Vás, že objednávka obsahuje zboží, kter<PERSON> je skladem u dodavatele, viz dostupnost jednotlivých produktů.<br>
<PERSON><PERSON><PERSON><PERSON> jsme právě objednali u našeho dodavatele.<br>
Dodací lhůta zboží trvá zpravidla 5-7 pracovních dnů od data objednání objednávky.<br>
J<PERSON><PERSON> z<PERSON> ob<PERSON>, je<PERSON><PERSON><PERSON> tentýž den objednávku realizujeme. Budeme Vás informovat prostřednictvím e-mailu. Děkujeme za pochopení a přejeme hezký zbytek dne. :)
</p>
{/if}

{* Čekáme na platbu *}
{if $mailStatus == 2}
<p>V<PERSON><PERSON><PERSON><PERSON> zákazníku,<br>
{if $payMode->delcode=='creditcard' && $delMode->delcode!="OSOBNE"}
vámi zvolená platba - on-line platba kartou, nebyla stále realizována. Prosíme o dodatečné provedení platby, níže odkaz na opětovný pokus.<br>
{plink //:Front:Order:status, $orderRow->ordid.substr(md5($orderRow->ordid.$orderRow->orddatec->getTimestamp()), 0, 8)}.<br>
<br>
V případě nejasností se na nás můžete obrátit.
{elseif $payMode->delcode=='paybefore'}
vámi objednané zboží je připraveno k expedici. Jelikož jste si zvolil/la platbu převodem, čekáme nyní na připsání finančních prostředků,
následně zboží předáme zvolenému přepravci.<br />
Údaje pro platbu předem najdete na tomto odkaze:<br />
{plink //:Front:Order:status, $orderRow->ordid.substr(md5($orderRow->ordid.$orderRow->orddatec->getTimestamp()), 0, 8)}
{/if}
<br>
Na <a href="{plink //:Front:Order:status, $orderRow->ordid.substr(md5($orderRow->ordid.$orderRow->orddatec->getTimestamp()), 0, 8)}">této stránce</a> můžete také průběžně sledovat stav
úhrady a postup vyřízení vaší objednávky.
{/if}

{* Zaplaceno *}
{if $mailStatus == 6}
<p>Vážený zákazníku,<br>
informujeme Vás, že jsme právě obdrželi Vaši platbu na náš bankovní účet. O dalším postupu objednávky Vás budeme následně informovat.
</p>
{/if}

{* Storno *}
{if $mailStatus == 5}
<p>Vážený zákazníku,<br>
  byli jsme nuceni provést STORNO objednávky, není-li Vám důvod znám, napište na e-mailovou adresu: {$presenter->config["SERVER_MAIL"]}. Byla-li objednávka již zaplacena na bankovní účet našeho e-shopu, bude Vám částka samozřejmě navrácena v nejkratším možném termínu. Děkujeme za pochopení.
</p>
{/if}

{* Vyřízeno - odesláno kurýrní službou *}
{if $mailStatus == 3}
<p>Vážený zákazníku,<br>
objednávka byla vyřízena. Zboží budeme dnes expedovat na adresu uvedenou v objednávce a to prostřednictvím Vámi zvolené kurýrní služby. Zboží můžete očekávat v následujících dnech. V případě jakýchkoli dotazů nás neváhejte kontaktovat.<br>
</p>
{if isset($delMode)}
<p>
{_'Zboží bylo odesláno přepravní službou'} <strong>{if !empty($orderRow->ordparurl) && isset($enum_delCustomURL[$orderRow->ordparurl])}{$enum_delCustomURL[$orderRow->ordparurl]}{else}{$delMode->delname}{/if}</strong>.<br />
{if $payMode->delcode=='dobirka'}
  {_'Cena dobírky'}: Částka: <strong>{$orderRow->ordpricevat|formatPrice}</strong>.<br />
  {if $delMode->delcode == 'ZASILKOVNA'}
    <strong style="color: red">POZOR!</strong> Pokud jste vybrali <strong style="color: red">Z-BOX</strong>, je potřeba <strong style="color: red">dobírku uhradit předem před vyzvednutím</strong> v  mobilní aplikaci Zásilkovna nebo v online sledování zásilek na webu Zásilkovny, více <a href="https://www.zasilkovna.cz/zbox" target="_blank"><strong>ZDE</strong></a>.<br>
  {/if}
{/if}
{if !empty($orderRow->ordparcode)} {_'Vaše číslo balíku je'}: <strong>{$orderRow->ordparcode}</strong>.<br />{/if}
{if isset($parcelURL)}<strong>{_'Balík můžete sledovat na této adrese:'} <a href="{$parcelURL}">{$parcelURL}</a></strong>.{/if}
</p>
{/if}
{if isset($invAdded) && $invAdded}
<p>V příloze zasíláme fakturu - danový doklad.</p>
{/if}
<p>Děkujeme za Váš nákup a srdečně se těšíme na další...</p>
{/if}

{* Přepravujeme na odběrné místo *}
{if $mailStatus == 10}
<p>Vážený zákazníku,<br>
informujeme, že jste si objednal zboží, které je skladem u dodavatele, nutno zajistit převoz z jejich centrálního skladu, většinou je doba dodání do 3 pracovních dnů.<br>
Dnes objednáme, takže v následujících dnech bude objednávka vyřízena, o čemž Vás budeme také informovat.<br>
<br>
Děkujeme za pochopení.
</p>
{/if}

{* Připraveno k odběru *}
{if $mailStatus == 9}
<p>Vážený zákazníku,<br>
objednávka je připravena k osobnímu odběru. Zboží si můžete vyzvednout na adrese:<br>
{$delMode->deldesc|breakLines}
{if !empty($delMode->delurlmap)}<br><a href="{$delMode->delurlmap}" target="_blank">Orientační mapa</a><br>{/if}
<br>
{if isset($invAdded) && $invAdded}
<br>V příloze zasíláme fakturu - danový doklad.<br>
{/if}
Děkujeme za Váš nákup a srdečně se těšíme na další...
</p>
{/if}

{* Připraveno k odběru *}
{if $mailStatus == 12}
<p>Vážený zákazníku,<br>
objednávka je připravena k odběru. Zboží si můžete vyzvednout na adrese provozovny, a to ve dnech Po-Pá od 8:00-15:30 na adrese provozovny.

Děkujeme za Váš nákup a srdečně se těšíme na další...
</p>
{/if}

{* Uzavřená *}
{if $mailStatus == 4}
<p>Vážený zákazníku,<br>
objednávka byla uzavřena. V příloze tohoto emailu zasíláme fakturu.

Děkujeme za Váš nákup a srdečně se těšíme na další...
</p>
{/if}

{include 'mailFooter.latte'}