
<h3><PERSON><PERSON><PERSON> aktualizace cen - upozornění</h3>
{foreach $wrongPrices as $row}
{if $iterator->isFirst()}
<h3>Špatné ceny</h3>
<table>
  <tr>
    <th><PERSON><PERSON><PERSON><PERSON></th>
    <th>Aktuální prodejní cena</th>
    <th>Výchozí cena Nosreti</th>
    <th>Nákupní cena Nosreti</th>
    <th>Rozdíl</th>
    <th>Rozdíl %</th>
    <th></th>
  </tr>
{/if}
  <tr>
    <td><a href="{plink //:Admin:Product:edit, $row->proid}">{$row->proname}</a></td>
    <td>{$row->propricea_web2|formatPrice}</td>
    <td>{$row->propricec_web2|formatPrice}</td>
    <td>{$row->propriced_web2|formatPrice}</td>
    <td>{$row->diff|formatPrice}</td>
    <td>{$row->diffpercent}%</td>
    <td><a href="{plink //:Admin:Product:edit, $row->proid}">upravit</a></td>
  </tr>
{if $iterator->isLast()}
</table>
{/if}
{/foreach}

{foreach $fixedUnsetAction as $row}
  {if $iterator->isFirst()}
    <h3>Fixovaná cena a akce skončila</h3>
    <table>
    <tr>
      <th>Název položky</th>
      <th>Aktuální prodejní cena</th>
      <th>Výchozí cena Nosreti</th>
      <th>Nákupní cena Nosreti</th>
      <th>Rozdíl</th>
      <th>Rozdíl %</th>
      <th>Fixace ceny</th>
      <th></th>
    </tr>
  {/if}
  <tr>
    <td><a href="{plink //:Admin:Product:edit, $row->proid}">{$row->proname}</a></td>
    <td>{$row->propricea_web2|formatPrice}</td>
    <td>{$row->propricec_web2|formatPrice}</td>
    <td>{$row->propriced_web2|formatPrice}</td>
    <td>{$row->diff|formatPrice}</td>
    <td>{$row->diffpercent} %</td>
    <td>{if $row->propricefix==1}Ano{else}Ne{/if}</td>
    <td><a href="{plink //:Admin:Product:edit, $row->proid}">upravit</a></td>
  </tr>
  {if $iterator->isLast()}
    </table>
  {/if}
{/foreach}

{include 'mailFooter.latte'}
