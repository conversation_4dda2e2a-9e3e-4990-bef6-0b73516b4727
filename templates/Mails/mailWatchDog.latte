
<p><strong><PERSON><PERSON><PERSON><PERSON> opět skladem na {$presenter->config["SERVER_NAME"]}</strong>.</p>

<p>Nastavil(a) jste si hlídání naskladnění na těcht<PERSON>, k<PERSON><PERSON> jsou nyní skladem.</p>

{foreach $products as $row}
  {if $iterator->isFirst()}
    <p>
  {/if}
  <a href="{plink //:Front:Product:detail, $row->proid, getProKey($row)}">{$row->proname}</a>, cena {$row->proprice|FormatPrice}Kč<br>
  {if $iterator->isLast()}
    </p>
  {/if}
{/foreach}

{include 'mailFooter.latte'}