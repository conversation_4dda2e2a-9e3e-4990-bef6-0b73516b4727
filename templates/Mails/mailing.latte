{php
if ($mailing->mamtarget == 'web2') {
  $color = '#1fb6c5';
} else {
  $color = '#c0392b';
}
}
{layout NULL}
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>{$mailing->mamsubject} - {$config["SERVER_NAMESHORT"]}</title>
</head>
<body style="line-height:1.3;background:white;font-family: Arial, Helvetica, sans-serif;font-size: 14px;color: #000;" link="#000000" vlink="#000000" alink="#000000">
<div style="width:630px;margin:0 auto">
  <!-- tabulka - logo web2 -->
  <table width="630" border="0" cellspacing="0" cellpadding="10">
    <tr>
      <td height="84" align="center" valign="top"><a href="{$serverUrl}/c/0/{$mamid}/{$usrid}"><img src="{$baseUrl}/img/{$mailing->mamtarget}/logo.png" width="243" height="80" alt="{$config["SERVER_NAMESHORT"]}" style="border:none" ></a></td>
    </tr>
  </table>
  <!-- tabulka - logo  -->
  {if $isMail}<p align="center">Nezobrazuje-li se vám e-mail správně, klikněte prosíme <a style="text-decoration:underline" href="{$serverUrl}/c/3/{$mailing->mamid}/{$usrid}/{$loKey}">na tento odkaz</a>.</p>{/if}

  {if !empty($mailing->mambody)}{$mailing->mambody|noescape}{/if}

  <!-- tabulka obalovaci zacatek -->
  <table width="630" border="0"   cellspacing="0" cellpadding="0">
    <tr>
      <td style="border:1px solid {$color}" align="center" valign="top">

        {foreach $products as $product}
          <!-- tabulka jeden vyrobek - pozadi sede -->
          <table  width="100%" border="0" cellspacing="0" cellpadding="10">
            <tr>
              <td  style="border:1px solid {$color}" width="100%" align="left" valign="top">{include #product product => $product, size => 'big', 'type' => '1'}</td>
            </tr>
          </table>
          <!-- tabulka jeden vyrobek - pozadi sede -->
        {/foreach}

  </table>
  {if $mailing->mamcouponvalue > 0}
  <table width="100%" border="0"  cellspacing="0" cellpadding="10">
    <tr>
      <td  align="center" valign="middle">
      <a href="{$serverUrl}/c/5/{$mailing->mamid}/{$usrid}/{$loKey}">Získat slevový kupón v hodnotě {$mailing->mamcouponvalue|formatPrice}</a>
      </td>
    </tr>
  </table>
  {/if}


  <!-- tabulka obalovaci konec -->
  <!-- paticka -->
  <table width="100%" border="0"  cellspacing="0" cellpadding="10">
    <tr>
      <td  align="center" valign="middle">
        <font size="-1"> Provozovatel: {$config["INVOICE_VENDOR_R1"]} | www: <a style="text-decoration:underline" href="{$serverUrl}/c/0/{$mailing->mamid}/{$usrid}">{$config["SERVER_NAMESHORT"]}</a> | e-mail: <a style="text-decoration:underline" href="mailto:{$config["SERVER_MAIL"]}">{$config["SERVER_MAIL"]}</a><br>
        <br>
        Sídlo: {$config["INVOICE_VENDOR_R2"]}, {$config["INVOICE_VENDOR_R3"]} {$config["INVOICE_VENDOR_R4"]} <br>
        {$config["INVOICE_VENDOR_R5"]}<br>
        {$config["INVOICE_VENDOR_R6"]}<br>
        <br>
        Tento email jste obdržel/a proto, že jste se v minulosti přihlásil/a k odběru novinek a akčních nabídek na našem eshopu. Pokud už o zasílání těchto emailů nestojíte, můžete se odhlásit zde:
          <a style="text-decoration:underline" href="{$serverUrl}/c/2/{$mailing->mamid}/{$usrid}/{$loKey}">odhlásit z odběru</a>.</font></td>
    </tr>
  </table>
  <!-- paticka -->
</div>
{* zaloguju zda precetl email *}
{if $isMail}<img src="{$serverUrl}/c/4/{$mamid}/{$usrid}" width="1px" height="1px" style="border:none"/>{/if}
</body>
</html>

{define product}
              <?
                $picName = ($product->propicname != "" ? trim($product->propicname).'.jpg' : $product->procode.'.jpg');
              }
              <a style="display:block;width:100%;height:100%;color:black;text-decoration:none " href="{$serverUrl}/c/1/{$mailing->mamid}/{$usrid}/{$product->proid}">
                {if $type==1}
                <img style="float:left;border:none;margin: 10px" src="{$serverUrl}/pic/{$serverId}/product/ml-{$size}/{$picName}">
                <p><strong>{if $size=='big'}<font size="3">{/if}{$product->proname}{if $size=='big'}</font>{/if}</strong><br>
                <br>
                {$product->prodescs|striptags|truncate:200} <br>
                {elseif $type==3 && $size == 'big'}
                <p><strong>{if $size=='big'}<font size="3">{/if}{$product->proname}{if $size=='big'}</font>{/if}</strong><br>
                <br>
                <img style="float:left; border:none" src="{$serverUrl}/pic/{$serverId}/product/ml-{$size}/{$picName}">
                {$product->prodescs|striptags|truncate:220} <br>
                {else}
                <p><strong>{if $size=='big'}<font size="3">{/if}{$product->proname}{if $size=='big'}</font>{/if}</strong><br>
                {/if}
                <br>
                <strong><font color="#84000" size="4">{$product->proprice|formatPrice}</font></strong>
                {if $size=='small'}<img style="float:right; border:none" src="{$serverUrl}/pic/{$serverId}/product/ml-{$size}/{$picName}">{/if}

                <br>
                <br>
                <img src="{$baseUrl}/img/{$serverId}/{if $size=='big'}ml-koupit.jpg{else}ml-koupit1.jpg{/if}" alt="Koupit" style="border:none"> </p>
              </a>
{/define}