
<img src="{$baseUrl}/img/{$serverId}/logo.png" height="73">
<h3>Seznam včera naskladněných položek na {$presenter->config["SERVER_NAME"]}</h3>
{foreach $rows as $key => $row}
{if $iterator->isFirst()}
<table>
  <tr>
    <th>Název položky</th>
    <th>Vaše cena *</th>
    <th><PERSON><PERSON><PERSON> skladem</th>
    <th></th>
  </tr>
{/if}
  <tr>
    <td><a href="{plink //Product:detail, $row->proid, getProKey($row)}">{$row->proname}</a></td>
    <td>{$row->proprice|formatPrice}</td>
    <td>{$row->qtyfree}</td>
    <td><a href="{plink //Basket:add $row->proid}">koupit</a></td>
  </tr>
{if $iterator->isLast()}
</table>
<p>* Uvedené ceny uvidíte na eshopu až po přihlášení na svůj zákaznický účet.</p>
{/if}
{/foreach}

{include 'mailFooter.latte'}
