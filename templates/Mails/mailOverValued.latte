
<p><strong><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></strong></p>

{foreach $rows as $row}
  {if $iterator->isFirst()}
    <p>Seznam přeceněn<PERSON><PERSON> polo<PERSON></p>
    <p>
  {/if}
  {$row->procode}, <a href="{plink //:Admin:Product:edit, $row->proid}">{$row->proname}</a>, cena {$row->proprice|FormatPrice}Kč<br>
  {if $iterator->isLast()}
    </p>
  {/if}
{/foreach}

{include 'mailFooter.latte'}