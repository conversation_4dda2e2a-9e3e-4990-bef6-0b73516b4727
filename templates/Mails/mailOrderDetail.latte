
<h3>{$title} {_'na'} {$presenter->config["SERVER_NAME"]}</h3>

<strong>{_'<PERSON><PERSON><PERSON> objedn<PERSON>vky'}:</strong><br />
<table>
<tr>
  <td><strong>Katalogové číslo</strong></td>
  <td><strong>Název</strong></td>
  <td><strong>Kusy</strong></td>
  <td><strong>Dostupnost</strong></td>
  <td><strong>Cena s DPH</strong></td>
</tr>
{php
$sum = 0;
}
{foreach $ordItemRows as $row}

{php
  $sum += ($row->oriqty*$row->oriprice);
}

<tr>
  <td>{$row->oriprocode}</td>
  <td>{$row->oriname}</td>
  <td>{$row->oriqty} {_'ks'}</td>
  {if $row->oritypid != 1}
  <td>
    {if $row->oriprice > 0}
        {$products[$row->oriproid]->getAccessText(enum_proaccess:$enum_proaccess)}
    {/if}
  </td>
  {else}
  <td></td>
  {/if}
  <td>{if $row->oriprice > 0}{$row->oriprice|formatPrice} {$row->oriprice|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}{else}ZDARMA{/if}</td>
</tr>

{/foreach}
</table>

<br />

<table>
<tr>
  <td><strong>{_'Celková cena s DPH'}: </strong></td>
  <td>{$sum|formatPrice} {$sum|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}</td>
</tr>
</table>

<p>
<strong>{_'Zvolená doprava'}:</strong> {$delMode->delname}
{if $delMode->delcode=='ULOZENKA' && isset($ulozenkapoint)} {$ulozenkapoint->uloname}<br>
{$ulozenkapoint->ulostreet}<br>
{$ulozenkapoint->ulocity}<br>
tel: {$ulozenkapoint->ulophone}, email: {$ulozenkapoint->uloemail}<br>
{$ulozenkapoint->uloopeninghours}<br>
Navigace: <br>
GPS: {$ulozenkapoint->ulogpsn} {$ulozenkapoint->ulogpse}<br>
{$ulozenkapoint->ulonavigation}<br>
{elseif $delMode->delcode=='ZASILKOVNA' && isset($zasilkovnapoint)} {$zasilkovnapoint->zasname}<br>
{$zasilkovnapoint->zasstreet}<br>
{$zasilkovnapoint->zascity}<br>
tel: {$zasilkovnapoint->zasphone}, email: {$zasilkovnapoint->zasemail}<br>
{$zasilkovnapoint->zasopeninghours}<br>
Navigace: <br>
GPS: {$zasilkovnapoint->zasgpsn} {$zasilkovnapoint->zasgpse}<br>
{$zasilkovnapoint->zasnavigation}<br>
{elseif $delMode->delid==43 && isset($geispoint)} Odběrné místo: {$geispoint->gponame}<br>
{$geispoint->gponame}<br>
{$geispoint->gpostreet}<br>
{$geispoint->gpocity}<br>
tel: {$geispoint->gpophone}, email: {$geispoint->gpoemail}<br>
{$geispoint->gpoopeninghours}<br>
GPS: {$geispoint->gpogpsn} {$geispoint->gpogpse}<br>
{elseif $delMode->delid==1 && isset($intimepoint)} Poštomat: {$intimepoint->intcity}, {$intimepoint->intstreet} ({$intimepoint->intname})<br>
{$intimepoint->intname}<br>
{$intimepoint->intstreet}<br>
{$intimepoint->intcity}<br>
GPS: {$intimepoint->intgpsn} {$intimepoint->intgpse}<br>
{elseif $delMode->delcode=='OSOBNE'}<br>
{$delMode->deldesc|breakLines}
{if !empty($delMode->delurlmap)}<br>Mapa: {$delMode->delurlmap}{/if}
{elseif $delMode->delcode=='CESKA_POSTA_BALIKOMAT' && isset($enum_balikomat[$orderRow->orddelspecbalikomat])}<br>
{$enum_balikomat[$orderRow->orddelspecbalikomat]}
{/if}
<br />
<strong>{_'Platba'}:</strong> {$payMode->delname}
{if $payMode->delcode == 'paybefore'}
{* udaje pro platbu predem *}
<br />
<strong>Údaje pro platbu předem:</strong><br />
Naše čísla účtů:<br>
{if $delMode->delcouid === 1}
{$presenter->config["SERVER_ACCNAME"]} {$presenter->config["SERVER_ACCNO"]}<br />
{if !empty($presenter->config["SERVER_ACCNAME2"])} {$presenter->config["SERVER_ACCNAME2"]} {$presenter->config["SERVER_ACCNO2"]}<br />{/if}
{else}
{$presenter->config["SERVER_ACCNAME"]}<br />
IBAN: {$presenter->config["SERVER_ACCIBAN"]}<br />
SWIFT: {$presenter->config["SERVER_ACCSWIFT"]}<br />
{/if}
Variabilní symbol: {$orderRow->ordcode}<br />
Částka: {$orderRow->ordpricevat|formatPrice} {$orderRow->ordpricevat|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}
{/if}
<br /><br />
<strong>{_'Poznámka'}:</strong><br />
{$orderRow->ordnote|breakLines}<br />
<br />
<strong>{_'Fakturační a současně doručovací adresa'}:</strong><br />
{_'Jméno'}: {$orderRow->ordiname}<br />
{_'Přijmení'}: {$orderRow->ordilname}<br />
{if $lang=='cs'}{_'Firma'}: {$orderRow->ordifirname}<br />{/if}
{_'Ulice'}: {$orderRow->ordistreet}<br />
{_'Číslo popisné'}: {$orderRow->ordistreetno}<br />
{_'Město, obec'}: {$orderRow->ordicity}<br />
{_'PSČ'}: {$orderRow->ordipostcode}<br />
{_'Telefon'}: {$orderRow->ordtel}<br />
{_'Email'}: {$orderRow->ordmail}<br />
IČ: {$orderRow->ordic}, DIČ: {$orderRow->orddic}<br />
{if !empty($orderRow->ordstname)}
<br />
<strong>Dodací adresa:</strong><br />
Jméno, přijmení: {$orderRow->ordstname} {$orderRow->ordstlname}<br />
Firma: {$orderRow->ordstfirname}<br />
Ulice: {$orderRow->ordststreet} {$orderRow->ordststreetno}<br />
Město, obec: {$orderRow->ordstcity}<br />
PSČ: {$orderRow->ordstpostcode}<br />
{/if}
</p>
{include 'mailFooter.latte'}