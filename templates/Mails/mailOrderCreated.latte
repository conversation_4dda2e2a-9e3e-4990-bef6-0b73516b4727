{varType App\Orm\Product[] $products}
{varType App\Orm\Product[] $ordItemsNotOnStock}

<h3>{_'Vaše objednávka č.'} {$orderRow->ordcode} {_'na'} {$presenter->config["SERVER_NAME"]} byla uložena v našem eshopu.</h3>
<strong>Děkujeme, že využíváte našich služeb. </strong><br />

<p>
  Při osobním odběru Vás budeme e-mailem informovat, jakmile bude objednávka připravena k vyzvednutí.
  Necháváte-li si zboží zaslat na adresu, tak také obdržíte e-mail v den expedice, jehož součástí bude i faktura v el. podobě (příloha).
  Děkujeme za pochopení. <br>
  <br>
  Stav Vaší objednávky můžete prů<PERSON><PERSON><PERSON><PERSON><PERSON> take sledovat na této <a href="{plink //:Front:Order:status, $orderRow->ordid.substr(md5($orderRow->ordid.$orderRow->orddatec->getTimestamp()), 0, 8)}" target="_blank">stránce</a>.
</p>

{if $orderRow->ordcustype === 'organ' && $payMode === 'paybefore'}
<p>
  Faktuta na základě vaší objednávky je přiložena k tomuto emailu jako příloha se splatností 30 dnů.
  Zboží zašleme na vámi uvedenou adresu a to bezprostředně.
  Upozorňujeme, že v rámci této nadstandardní služby jsme nuceni si objednávku ověřit a to z důvodu možnosti podvodného jednání!
</p>
{/if}

<strong>{_'Položky objednávky'}:</strong><br />
<table>
<tr>
  <td><strong>Katalogové číslo</strong></td>
  <td><strong>Název</strong></td>
  <td><strong>Kusy</strong></td>
  <td><strong>Dostupnost</strong></td>
  <td><strong>Cena s DPH/ks</strong></td>
</tr>
{php
$sum = 0;
$vendors = array();
}
{foreach $ordItemRows as $row}
  {var $sum = $sum+$row->oriqty*$row->oriprice}
        {if ($row->oritypid == 0) }
            {php


                $provenid = $products[$row->oriproid]->vendorCode;
                if (!empty($provenid)) {
                  $vendors[$provenid] = 1;
                } else {
                  $vendors["none"] = 1;
                }
                if ($provenid != "nosreti") $vendors["not_nosreti"] = 1;
                if ($provenid != "homelux") $vendors["not_homelux"] = 1;
                if ($provenid != "mujperfekt") $vendors["not_mujperfekt"] = 1;

            }
        {/if}

<tr>
  <td>{$row->oriprocode}</td>
  <td>
    {$row->oriname}
    {if !empty($products[$row->oriproid]) && $products[$row->oriproid]->deliveryFree==App\Orm\Product::DELIVERYFREE_BLOCK}
      <br><small style="color: red">POZOR, nezapočítává se do dopravy zdarma!</small>
    {/if}
    {if $row->origifttype==App\Orm\BasketItem::GIFT_REJECTED}
      <br><small>Dárek byl odmítnut</small>
    {/if}
  </td>
  <td>{$row->oriqty} {_'ks'}</td>
  {if $row->oritypid != 1}
  <td>
    {if $row->oriprice > 0}
      {$products[$row->oriproid]->getAccessText(enum_proaccess:$enum_proaccess)}
    {/if}
  </td>
  {else}
  <td></td>
  {/if}
  <td>
    {if $row->oriprice > 0}
      {$row->oriprice|formatPrice} {$row->oriprice|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}
      {if $row->origiftdisc > 0}<br><small>(původní cena {($row->oriprice + $row->origiftdisc)|formatPrice})</small>{/if}
    {else}ZDARMA{/if}</td>
</tr>

{/foreach}
</table>

<table>
<tr>
  <td><strong>{_'Celková cena s DPH'}: </strong></td>
  <td>{$sum|formatPrice} {$sum|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}</td>
</tr>
</table>

<br />
<strong style="color: red;">
  MÁME INSTAGRAM, SLEDUJ NÁS <a href="https://www.instagram.com/shopcom.cz?utm_source=qr&igsh=MzR3bTZleHNvN2t2">ZDE</a>
</strong>
<br />

{if count($ordItemsNotOnStock) > 0}
    <strong style="color: red;">
      Berte prosím na vědomí, že některé položky nebyly v okamžiku objednání skladem v požadovaném množství, což může způsobit prodloužení lhůty dodání.
    </strong>
    <br /><br />
{/if}

{if isset($giftsCnt) && $giftsCnt > 0}
    <strong style="color: red;">
      Vzhledem k tomu, že Vaše objednávka obsahuje DÁREK, jsme byli nuceni v souladu s legislativou ČR odečíst 1 Kč z ceny kusu produktu a tu následně přičíst k dárku, který se k danému produktu vztahuje.
      Bez obav, pro Vás se nic nemění, výsledná cena zůstane stejná! Kontrolní orgány si totiž nepřejí, aby byly dárky nabízeny za 0 Kč.
    </strong>
    <br />
{/if}

{if !empty($vendors["nosreti"]) && !empty($vendors["not_nosreti"])}
  {* pokud obj. obsahuje nosreti a jeste jine zbozi tak info o doprave zvlast *}
    <strong style="color: red;">
      Objednal/a jste si produkt z kategorie CHLADÍCÍ ZAŘÍZENÍ, který bude doručen spediční společností Geis.
      Zbývající produkty mimo tuto kategorii včetně dárků, budou odeslány ZVLÁŠŤ a to spediční společností PPL ZDARMA. Děkujeme za pochopení.
    </strong><br />
{/if}

{if !empty($vendors["homelux"]) || !empty($vendors["nosreti"]) || !empty($vendors["mujperfekt"])}
    {* pokud obj. obsahuje nosreti a homelux *}
    <strong style="color: red;">
      Prosíme, zboží při přebírání řádně zkontrolovat! Bude-li obal jevit známky jakéhokoli poškození, znehodnocení či jiné, pak trvejte na zápisu - převzato s výhradou!
      Kurýr je povinen onen zápis vystavit! Předejdete tím případným komplikacím při případné reklamaci.
    </strong><br />
   {if (!empty($vendors["nosreti"]) && !empty($vendors["not_nosreti"])) || (!empty($vendors["homelux"]) && !empty($vendors["not_homelux"])) || (!empty($vendors["mujperfekt"]) && !empty($vendors["not_mujperfekt"]))}
    <strong style="color: red;">
      Zbývající produkty včetně DÁRKŮ, budou odeslány ZVLÁŠŤ, po doručení nadrozměrné záslky, a to spediční společností Česká pošta - ZDARMA. Děkujeme za pochopení.
    </strong><br />
    {/if}
{/if}

{if !empty($messages) && is_array($messages)}
{foreach $messages as $message}
  <p style="color: red;">{$message}</p>
{/foreach}
{/if}

{if isset($basketOverloaded) && $basketOverloaded}
  <strong style="color: red;">
    Byl překročen max. váhový limit 50Kg pro stadnardní spedici kurýrní služby. Po uzavření objednávky Vám z naší strany indivudálně neceníme nadrozměrnou dopravu nebo můžete zvolit osobní odběr.
  </strong>
{/if}

{if $delMode->delcode=='ZASILKOVNA'}
  <strong style="color: red">POZOR!</strong> Pokud jste vybrali <strong style="color: red">Z-BOX</strong>, je potřeba <strong style="color: red">dobírku uhradit předem před vyzvednutím</strong> v  mobilní aplikaci Zásilkovna nebo v online sledování zásilek na webu Zásilkovny, více <a href="https://www.zasilkovna.cz/zbox" target="_blank"><strong>ZDE</strong></a>.<br>
  <br>
  <strong style="color: red;">
    Doporučujeme, zásilku při převzetí a uhrazení fyzicky překontrolovat/rozbalit na výdejním místě zasilkovna.cz (netýká se služby Z-BOX), zda nedošlo k poškození či zcizení při přepravě.
    Došlo-li by k převzetí a následném opuštění výdejního místa bez výhrad, má se zato, že zboží bylo dodáno nepoškozeno. Dojde-li při kontrole na pobočce ke zjištění, že je zásilka poškozena,
    je nutné o této skutečnosti informovat obsluhu výdejního místa. Obsluha by měla zásilku vzít zpět a bylo-li uhrazeno na výdejním místě, pak také vrátit peníze! Nastane-li situace,
    kdy by obsluha nebyla ochotna vrátit peníze, pak neprodleně volat infolinku zasilkovna.cz: +420 216 216 516 a tuto skutečnost tam nahlásit! Následně sdělit i prodejci na email {$presenter->config["SERVER_MAILRECL"]},
    aby mohlo být zahájeno reklamační řízení se zasilkovna.cz a s vámi dohodnut další postup např. opětovné zaslání zásilky. Pokud by nastal problém při vyzvednutí zásilky ze Z-BOXU, je zapotřebí,
    aby příjemce neprodleně kontaktoval náš zákaznický servis na telefonním čísle +420 216 216 516 nebo e-<NAME_EMAIL>.
    V případě e-mailu je vhodné, aby přiložil fotografie dané zásilky a popsal konkrétní problém.
  </strong><br>
  <br>
{/if}

{if $isAc}
  <p style="color: red;">
    Koupě produktu je podmíněna zajištěním <strong>profesionální montáže</strong> a to buď našim smluvním partnerem, viz
    certifikát v příloze, tel.: 603818482, nebo si zákazník zajistí montáž sám, ale <strong>musí doložit certifikát dané firmy</strong>, která bude montáž provádět.
  </p>
{/if}

<p>
<strong>{_'Zvolená doprava'}:</strong> {$delMode->delname}
{if $delMode->delcode=='ULOZENKA' && isset($ulozenkapoint)} {$ulozenkapoint->uloname}<br>
{$ulozenkapoint->ulostreet}<br>
{$ulozenkapoint->ulocity}<br>
tel: {$ulozenkapoint->ulophone}, email: {$ulozenkapoint->uloemail}<br>
{$ulozenkapoint->uloopeninghours|noescape}<br>
Navigace: <br>
GPS: {$ulozenkapoint->ulogpsn} {$ulozenkapoint->ulogpse}<br>
{$ulozenkapoint->ulonavigation|noescape}<br>
{elseif $delMode->delcode=='ZASILKOVNA' && isset($zasilkovnapoint)} {$zasilkovnapoint->zasname}<br>
{$zasilkovnapoint->zasstreet}<br>
{$zasilkovnapoint->zascity}<br>
tel: {$zasilkovnapoint->zasphone}, email: {$zasilkovnapoint->zasemail}<br>
{$zasilkovnapoint->zasopeninghours|noescape}<br>
Navigace: <br>
GPS: {$zasilkovnapoint->zasgpsn} {$zasilkovnapoint->zasgpse}<br>
{$zasilkovnapoint->zasnavigation|noescape}<br>
{elseif $delMode->delid==43 && isset($geispoint)} Odběrné místo: {$geispoint->gponame}<br>
{$geispoint->gponame}<br>
{$geispoint->gpostreet}<br>
{$geispoint->gpocity}<br>
tel: {$geispoint->gpophone}, email: {$geispoint->gpoemail}<br>
{$geispoint->gpoopeninghours}<br>
GPS: {$geispoint->gpogpsn} {$geispoint->gpogpse}<br>
{elseif $delMode->delid==1 && isset($intimepoint)} Poštomat: {$intimepoint->intcity}, {$intimepoint->intstreet} ({$intimepoint->intname})<br>
{$intimepoint->intname}<br>
{$intimepoint->intstreet}<br>
{$intimepoint->intcity}<br>
GPS: {$intimepoint->intgpsn} {$intimepoint->intgpse}<br>
{elseif isset($balikovnapoint) && $delMode->delcode=='CESKA_POSTA_BALIKOVNA'}<br>
Adresa Balíkovny: {$balikovnapoint->balpostcode}, {$balikovnapoint->balname}, {$balikovnapoint->balstreet}
{elseif isset($postapoint) && $delMode->delcode=='CESKA_POSTA_NA_POSTU'}<br>
Adresa pošty: {$postapoint->cpaddress}
{elseif $delMode->delcode=='OSOBNE'}<br>
{$delMode->deldesc|breakLines}
  {if !empty($delMode->delurlmap)}<br>Mapa: <a href="{$delMode->delurlmap}">ZDE</a>.{/if}
  {if !empty($delMode->delsrcphoto)}<br><img src="{$delMode->delsrcphoto}" alt="$delMode->delname" />{/if}
{elseif $delMode->delcode=='CESKA_POSTA_BALIKOMAT' && isset($enum_balikomat[$orderRow->orddelspecbalikomat])}<br>
{$enum_balikomat[$orderRow->orddelspecbalikomat]}
{/if}
<br />
<strong>{_'Platba'}:</strong> {$payMode->delname}
{if $payMode->delcode == 'paybefore' && $orderRow->ordtype != 3}
{* udaje pro platbu predem *}
<br />
<strong>Údaje pro platbu předem:</strong><br />
Naše čísla účtů:<br>
{if $delMode->delcouid === 1}
{$presenter->config["SERVER_ACCNAME"]} {$presenter->config["SERVER_ACCNO"]}<br />
{if !empty($presenter->config["SERVER_ACCNAME2"])} {$presenter->config["SERVER_ACCNAME2"]} {$presenter->config["SERVER_ACCNO2"]}<br />{/if}
Variabilní symbol: {$orderRow->ordcode}<br />
Částka: {$orderRow->ordpricevat|formatPrice} {$orderRow->ordpricevat|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}<br />
<img src="{plink //:Front:Order:qr, substr(md5($orderRow->ordid.$orderRow->orddatec->getTimestamp()), 0, 6).$orderRow->ordid}" />
{else}
{$presenter->config["SERVER_ACCNAME"]}<br />
IBAN: {$presenter->config["SERVER_ACCIBAN"]}<br />
SWIFT: {$presenter->config["SERVER_ACCSWIFT"]}<br />
Variabilní symbol: {$orderRow->ordcode}<br />
Částka: {$orderRow->ordpricevat|formatPrice} {$orderRow->ordpricevat|formatPriceEur:$presenter->config["EUR_RATE"]|noescape}
{/if}


{/if}
<br /><br />
<strong>{_'Poznámka'}:</strong><br />
{$orderRow->ordnote|breakLines}<br />
<br />
<strong>{_'Fakturační a současně doručovací adresa'}:</strong><br />
{_'Jméno'}: {$orderRow->ordiname}<br />
{_'Přijmení'}: {$orderRow->ordilname}<br />
{if $lang=='cs'}{_'Firma'}: {$orderRow->ordifirname}<br />{/if}
{_'Ulice'}: {$orderRow->ordistreet}<br />
{_'Číslo popisné'}: {$orderRow->ordistreetno}<br />
{_'Město, obec'}: {$orderRow->ordicity}<br />
{_'PSČ'}: {$orderRow->ordipostcode}<br />
{_'Telefon'}: {$orderRow->ordtel}<br />
{_'Email'}: {$orderRow->ordmail}<br />
IČ: {$orderRow->ordic}, DIČ: {$orderRow->orddic}<br />
{if !empty($orderRow->ordstname)}
<br />
<strong>Dodací adresa:</strong><br />
Jméno, přijmení: {$orderRow->ordstname} {$orderRow->ordstlname}<br />
Firma: {$orderRow->ordstfirname}<br />
Ulice: {$orderRow->ordststreet} {$orderRow->ordststreetno}<br />
Město, obec: {$orderRow->ordstcity}<br />
PSČ: {$orderRow->ordstpostcode}<br />
{/if}
{foreach $ordItemPdf as $row}
  {if $iterator->isFirst()}
<br />
<strong>K objednaným položkám nabízíme ke stažení tyto materiály:</strong><br />
  {/if}
{$row->oriname}: <a href="{$baseUrl}/files/{$row->atafilename}" target="_blank">{$row->ataname}</a> ({$row->atatype|upper}, {$row->atasize|bytes})<br />
  {if $iterator->isLast()}
  {/if}
{/foreach}
</p>

<p>V příloze zasíláme naše obchodní podmínky, reklamační řád, formulář pro podání reklamace/odstoupení od kupní smlouvy. Více informací <a href="{$baseUrl}/text-postup-pri-reklamaci">zde</a>. </p>

{include 'mailFooter.latte'}