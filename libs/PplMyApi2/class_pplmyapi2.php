<?php
/**
* PPL <PERSON>vce (myapi2)
*
* @copyright <PERSON>, https://zvarik.cz/
*
* @license	Jen pro potřeby vlastníka, z<PERSON>az dalšího prodeje nebo sdílení.
*			Only for the needs of the owner, further resell or share is prohibited
*
* @version 2023-10-04
*/
class pplmyapi2
{
	private $endpoint_url;
	private $endpoint_url_production = 'https://api.dhl.com/ecs/ppl/myapi2';
	private $endpoint_url_dev = 'https://api-dev.dhl.com/ecs/ppl/myapi2';
	
	
	/**
	* GET
	*/
	function get($path, $postData = null, &$headerData = [])
	{
		$path = trim($path, '/');
		
		$ch = curl_init();
		if (strpos($path, '://') !== false) {
			$url = $path; // zadána celá URL
		} else {
			$url = $this->endpoint_url.'/'.$path;
		}
		
		
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_HEADER, true);
		
		
		$headers = array();
		$headers[] = "Accept: application/json";
		$headers[] = "Authorization: Bearer ".$this->token;
		
		if (isset($postData)) {
			// hlavičky se akceptují jen u POST, jinak to hodí chybu
			$headers[] = "Accept: application/json";
			$headers[] = "Content-Type: application/json";
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
			curl_setopt($ch, CURLOPT_POST, 1);
		}
		else {
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
		}
		
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		
		$res = curl_exec($ch);
		
		$header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
		$header = substr($res, 0, $header_size);
		$body = substr($res, $header_size);
		
		preg_match_all('~^\s*([^\s]+)\s*: \s*(.+?)\s*$~m', $header, $ms);
		
		$headerData = array_combine($ms[1], $ms[2]);
		
		$status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		
		$headerData['Status'] = $headerData['status'] = $status;
		
		curl_close($ch);
		
		if (in_array(substr($body, 0, 1), ['{', '['])) {
			$body = json_decode($body);
		}
		
		if ($status == 201)
		{
			// byl vytvořen nový resource
			
			if (!empty($headerData['Location']))
			{
				return (string) $headerData['Location'];
			}
		}
		
		return $body;
	}
	
	/**
	* Access Token
	*/
	private function getAccessToken($client_id, $client_secret)
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $this->endpoint_url.'/login/getAccessToken');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials&scope=myapi2&client_id='.$client_id.'&client_secret='.$client_secret);
		curl_setopt($ch, CURLOPT_POST, 1);
		
		$headers = array(); 
		$headers[] = "Accept: application/json";
		// programátoři v PPL se rozhodli, že to pošlou přes $_POST
		// $headers[] = "Authorization: Basic " . base64_encode($client_id . ":" . $client_secret);
		$headers[] = "Content-Type: application/x-www-form-urlencoded";
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		
		$res = curl_exec($ch);
		curl_close($ch);
		return json_decode($res)->access_token;
	}
	
	
	
	/**
	* __construct
	*
	* @param (string) $client_id
	* @param (string) $client_secret
	*/
	function __construct($client_id, $client_secret, $isTestMode = false)
	{
		$this->endpoint_url = ($isTestMode ? $this->endpoint_url_dev : $this->endpoint_url_production);
		$this->token = $this->getAccessToken($client_id, $client_secret);
	}
	
	
	/**
	* Odeslání objednávky
	* Vrátí URL adresu, která se musí dále kontrolovat, zda byl vytvořen štítek
	*/
	function orderBatch($data)
	{
		$res = $this->get('order/batch', $data);
		
		// pokud nedošlo k chybě, tak máme URL, ze kterého dostanu batchId a které vrací stav vytváření
		if (is_string($res)) {
			return $res;
		}
		
		// došlo k nějaké chybě
		throw new pplmyapi2Exception(__FUNCTION__, 500, $res);
	}
	
	
	/**
	* Odeslání zásilky
	* Vrátí URL adresu, která se musí dále kontrolovat, zda byl vytvořen štítek
	*/
	function shipmentBatch($data)
	{
		$res = $this->get('shipment/batch', $data);
		
		// pokud nedošlo k chybě, tak máme URL, ze kterého dostanu batchId a které vrací stav vytváření
		if (is_string($res)) {
			return $res;
		}
		
		// došlo k nějaké chybě
		throw new pplmyapi2Exception(__FUNCTION__, 500, $res);
	}
	
	
	/**
	* Storno zásilky
	*/
	function cancelShipment($number)
	{
		// vrací HEADER - HTTP/1.1 202 Accepted
		$this->get('shipment/'.$number.'/cancel', [], $headerData);
		
		if ($headerData['Status'] == 202) {
			return true;
		}
		else {
			throw pplmyapi2Exception(__FUNCTION__, $headerData['Status'], 'Nevrátilo status 202 Accepted');
		}
	}
	
	/**
	* Změna zásilky - kontaktní údaje
	*/
	function changeShipmentContact($number, $data)
	{
		// vrací HEADER - HTTP/1.1 202 Accepted
		$this->get('shipment/'.$number.'/redirect', $data, $headerData);
		
		if ($headerData['Status'] == 202) {
			return true;
		} else {
			throw pplmyapi2Exception(__FUNCTION__, $headerData['Status'], 'Nevrátilo status 202 Accepted');
		}
	}
	
	
	/**
	* Výdejní místa
	*
	* POZOR! V seznamu mají 38.000 poboček, což znamená 38 requestů
	*/
	function getAccessPoints($countryCode, $pageNo = 0)
	{
		// maximum na stránku je 1000
    $limit = 1000;
    $offset = $pageNo * $limit;
		return $this->get('accessPoint?CountryCode='.$countryCode.'&Limit=1000&Offset=' . $offset, null, $headerData);
	}
}


/**
* CeskaPostaException
*/
class pplmyapi2Exception extends Exception
{
	private $extra = [];
	
	/**
	* @param (string) $message - Předmět chyby 
	* @param (integer) $code - Kód
	* @param (mixed) $extra - JSON / Array / String - Extra popis chyby
	*/
	public function __construct($functionName, $code = 500, $extra = null)
	{
		if (is_string($extra)) {
			$extraTemp = @json_decode($extra, true);
			if (!empty($extraTemp)) {
				if (count($extraTemp) == 1) $extraTemp = current($extraTemp);
				$extra = $extraTemp;
			}
		}
        $this->extra = $extra;
        parent::__construct("Error:" . @json_encode((array)$extra->Errors), $code);
	}
	
	/**
	* Další info o chybě
	*
	* @return (array)
	*/
	public function getExtra()
	{
		return (array) $this->extra;
	}
	
	/**
	* RAW výpis chyby
	*/
	public function __toString()
	{
		return $this->getMessage().' ['.$this->getCode().']: '.var_export($this->getExtra(), true);
	}
}

