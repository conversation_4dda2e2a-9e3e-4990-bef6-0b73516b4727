{"name": "monolog/monolog", "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "keywords": ["log", "logging", "psr-3"], "homepage": "http://github.com/Seldaek/monolog", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.5", "graylog2/gelf-php": "~1.0", "sentry/sentry": "^0.13", "ruflin/elastica": ">=0.90 <3.0", "doctrine/couchdb": "~1.0@dev", "aws/aws-sdk-php": "^2.4.9 || ^3.0", "php-amqplib/php-amqplib": "~2.4", "swiftmailer/swiftmailer": "^5.3|^6.0", "php-console/php-console": "^3.1.3", "phpunit/phpunit-mock-objects": "2.3.0", "jakub-onderka/php-parallel-lint": "0.9"}, "_": "phpunit/phpunit-mock-objects required in 2.3.0 due to https://github.com/sebastianbergmann/phpunit-mock-objects/issues/223 - needs hhvm 3.8+ on travis", "suggest": {"graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "sentry/sentry": "Allow sending log messages to a Sentry server", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "rollbar/rollbar": "Allow sending log messages to Rollbar", "php-console/php-console": "Allow sending log messages to Google Chrome"}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "autoload-dev": {"psr-4": {"Monolog\\": "tests/Monolog"}}, "provide": {"psr/log-implementation": "1.0.0"}, "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "scripts": {"test": ["parallel-lint . --exclude vendor --exclude src/Monolog/Handler/FormattableHandlerInterface.php  --exclude src/Monolog/Handler/FormattableHandlerTrait.php --exclude src/Monolog/Handler/ProcessableHandlerInterface.php --exclude src/Monolog/Handler/ProcessableHandlerTrait.php", "phpunit"]}}