<?php
/**
* ČESKÁ POŠTA (B2B API)
*
* <AUTHOR> | zvarik.cz
*
* @link https://zvarik.cz/cs/ceska-posta-api-b2b-brana
* @link https://www.ceskaposta.cz/napi/b2b
* @link https://www.postaonline.cz/dokumentaceapi/b2b/redoc/B2B3-ZSKService/B2B-ZSKService-1.4.0.yaml
*
* @version 2022-04-23
*/
class CeskaPosta
{
	const URL_PRODUCTION = 'https://b2b.postaonline.cz:444/restservices/ZSKService/v1';
	const URL_TEST = 'https://b2b-test.postaonline.cz:444/restservices/ZSKService/v1';
	
	private $idContract;
	private $apiToken;
	private $secretKey;
	private $isTesting;
	
	
	/**
	* __construct
	*
	* Testovací účty najdete zde: https://www.ceskaposta.cz/napi/b2b#accounts 
	*
	* @param (int) $id_contract - Např. ********* - <PERSON><PERSON><PERSON> je uvedeno ve smlouvě k B2B API
	* @param (string) $apiToken
	* @param (string) $secretKey
	* @param (bool) $isTesting = false - Zda jde o testovací provoz
	*/
	function __construct($idContract, $apiToken, $secretKey, $isTesting = false)
	{
		$this->idContract = $idContract;
		$this->apiToken = $apiToken;
		$this->secretKey = $secretKey;
		$this->isTesting = (bool) $isTesting;
	}
	
	
	/**
	* Get - pošle request
	*
	* @param (string) $method - GET/POST/PUT...
	* @param (string) $urlPath - Část URL
	* @param (array) $params = null - Tělo requestu; pokud půjde o GET, tak se to přidá k URL
	*
	* @param (array) Response 
	*/
	function get($method, $urlPath, $params = null)
	{
		$method = strtoupper($method);
		
		$url = $this->isTesting ? static::URL_TEST : static::URL_PRODUCTION;
		
		$url.= '/'.trim($urlPath, '/ ');
		
		if ($method == 'GET' && !empty($params)) {
			$url.= '?'.http_build_query($params);
		}
		
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
		curl_setopt($ch, CURLOPT_TIMEOUT, 60);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_VERBOSE, false);
    curl_setopt($ch, CURLOPT_HEADER, 0);
		
		
		$requestBody = '';
		
		if ($method != 'GET' && !empty($params))
		{
			if (is_array($params)) $params = json_encode($params);
			
			$requestBody = $params;
			
			curl_setopt($ch, CURLOPT_POSTFIELDS, $requestBody);
		}
		
		
		//--- AUTORIZACE
		$contentHash = hash('sha256', $requestBody);
		$timestamp = time();
		$nonce = $this->generateNonceV4();
		
		$signature = hash_hmac('sha256', "$contentHash;$timestamp;$nonce", $this->secretKey, true);
		
		$signature = base64_encode($signature);
		
		
		$headers = array();
		$headers[] = 'Content-Type: application/json;charset=UTF-8';
		$headers[] = 'Api-Token: '.$this->apiToken;
		$headers[] = 'Authorization-Timestamp: '.$timestamp;
		$headers[] = 'Authorization-Content-SHA256: '.$contentHash;
		$headers[] = 'Authorization: CP-HMAC-SHA256 nonce="'.$nonce.'" signature="'.$signature.'"';
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		
		$res = curl_exec($ch);
		
		$statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		if (substr($statusCode, 0, 1) != 2) {
			throw new CeskaPostaException('HTTP Status Error', $statusCode, $res);
		}
		
		if (empty($res) && ($method == 'GET' || $method == 'POST')) {
			throw new CeskaPostaException('Empty response', 500, ['error' => curl_error($ch) ]);
		}
		
		$json = @json_decode($res, true);
		if (!empty($res) && empty($json)) {
			throw new CeskaPostaException('Response JSON Error', 500, ['response_content' => $res, 'json_error' => json_last_error() ]);
		}
		
		return $json;
	}
	
	
	/**
	* Nonce
	*/
	private function generateNonceV4()
	{
		return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
			mt_rand(0, 0xffff), mt_rand(0, 0xffff),
			mt_rand(0, 0xffff),
			mt_rand(0, 0x0fff) | 0x4000,
			mt_rand(0, 0x3fff) | 0x8000,
			mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
		);
	}
	
	
	/**
	* Zformátuje telefon do čistého formátu
	*
	* @param (string) $s Phone Number
	* @param (string = CZ) $phonePrefix Country ISO code /or Prefix like +420 (will be added to $s only if needed)
	*
	* @result (string) +420732123456
	*/
	static function formatPhoneNumber($s, $phonePrefix = 'CZ')
	{
		$s = preg_replace("~(^00|(?<!^)\+|[^0-9\+])~", "", $s);
		
		if (strpos($s, '+') === FALSE)
		{
			$prefixesByIso = array('AF' => '+93','AL' => '+355','DZ' => '+213','AD' => '+376','AO' => '+244','AG' => '******','AR' => '+54','AM' => '+374','AU' => '+61','AT' => '+43','AZ' => '+994','BS' => '******','BH' => '+973','BD' => '+880','BB' => '******','BY' => '+375','BE' => '+32','BZ' => '+501','BJ' => '+229','BT' => '+975','BO' => '+591','BA' => '+387','BW' => '+267','BR' => '+55','BN' => '+673','BG' => '+359','BF' => '+226','BI' => '+257','KH' => '+855','CM' => '+237','CA' => '+1','CV' => '+238','CF' => '+236','TD' => '+235','CL' => '+56','CN' => '+86','CO' => '+57','KM' => '+269','CD' => '+243','CG' => '+242','CR' => '+506','CI' => '+225','HR' => '+385','CU' => '+53','CY' => '+357','CZ' => '+420','DK' => '+45','DJ' => '+253','DM' => '******','EC' => '+593','EG' => '+20','SV' => '+503','GQ' => '+240','ER' => '+291','EE' => '+372','ET' => '+251','FJ' => '+679','FI' => '+358','FR' => '+33','GA' => '+241','GM' => '+220','GE' => '+995','DE' => '+49','GH' => '+233','GR' => '+30','GD' => '+1-473','GT' => '+502','GN' => '+224','GW' => '+245','GY' => '+592','HT' => '+509','HN' => '+504','HU' => '+36','IS' => '+354','IN' => '+91','ID' => '+62','IR' => '+98','IQ' => '+964','IE' => '+353','IL' => '+972','IT' => '+39','JM' => '+1-876','JP' => '+81','JO' => '+962','KZ' => '+7','KE' => '+254','KI' => '+686','KP' => '+850','KR' => '+82','KW' => '+965','KG' => '+996','LA' => '+856','LV' => '+371','LB' => '+961','LS' => '+266','LR' => '+231','LY' => '+218','LI' => '+423','LT' => '+370','LU' => '+352','MK' => '+389','MG' => '+261','MW' => '+265','MY' => '+60','MV' => '+960','ML' => '+223','MT' => '+356','MH' => '+692','MR' => '+222','MU' => '+230','MX' => '+52','FM' => '+691','MD' => '+373','MC' => '+377','MN' => '+976','ME' => '+382','MA' => '+212','MZ' => '+258','MM' => '+95','NA' => '+264','NR' => '+674','NP' => '+977','NL' => '+31','NZ' => '+64','NI' => '+505','NE' => '+227','NG' => '+234','NO' => '+47','OM' => '+968','PK' => '+92','PW' => '+680','PA' => '+507','PG' => '+675','PY' => '+595','PE' => '+51','PH' => '+63','PL' => '+48','PT' => '+351','QA' => '+974','RO' => '+40','RU' => '+7','RW' => '+250','KN' => '+1-869','LC' => '+1-758','VC' => '+1-784','WS' => '+685','SM' => '+378','ST' => '+239','SA' => '+966','SN' => '+221','RS' => '+381','SC' => '+248','SL' => '+232','SG' => '+65','SK' => '+421','SI' => '+386','SB' => '+677','SO' => '+252','ZA' => '+27','ES' => '+34','LK' => '+94','SD' => '+249','SR' => '+597','SZ' => '+268','SE' => '+46','CH' => '+41','SY' => '+963','TJ' => '+992','TZ' => '+255','TH' => '+66','TL' => '+670','TG' => '+228','TO' => '+676','TT' => '+1-868','TN' => '+216','TR' => '+90','TM' => '+993','TV' => '+688','UG' => '+256','UA' => '+380','AE' => '+971','GB' => '+44','US' => '+1','UY' => '+598','UZ' => '+998','VU' => '+678','VA' => '+379','VE' => '+58','VN' => '+84','YE' => '+967','ZM' => '+260','ZW' => '+263','GE' => '+995','TW' => '+886','AZ' => '+374-97','CY' => '+90-392','MD' => '+373-533','SO' => '+252','GE' => '+995','CX' => '+61','CC' => '+61','NF' => '+672','NC' => '+687','PF' => '+689','YT' => '+262','GP' => '+590','GP' => '+590','PM' => '+508','WF' => '+681','CK' => '+682','NU' => '+683','TK' => '+690','GG' => '+44','IM' => '+44','JE' => '+44','AI' => '******','BM' => '******','IO' => '+246','VG' => '******','KY' => '******','FK' => '+500','GI' => '+350','MS' => '******','SH' => '+290','TC' => '******','MP' => '******','AS' => '******','GU' => '******','VI' => '******','HK' => '+852','MO' => '+853','FO' => '+298','GL' => '+299','GF' => '+594','GP' => '+590','MQ' => '+596','RE' => '+262','AX' => '+358-18','AW' => '+297','AN' => '+599','SJ' => '+47','AC' => '+247','TA' => '+290','CS' => '+381','PS' => '+970','EH' => '+212',
			);
			
			if (isset($phonePrefix) && isset($prefixesByIso[strtoupper($phonePrefix)])) {
				$phonePrefix = $prefixesByIso[strtoupper($phonePrefix)];
			}
			
			$noSignsPrefix = preg_replace("~[^0-9]~", "", $phonePrefix);
			if ($noSignsPrefix == substr($s, 0, strlen($noSignsPrefix))) {
				$s = '+' . $s; // only plus was missing
			}
			else {
				$s = $phonePrefix . $s;
			}
		}
		return $s;
	}
	
	
	/**
	* Stats - vrátí statistické informace o podáních za období
	*
	* @param (string/int) $dateFrom - date/timestamp
	* @param (string/int) $dateTo = time() - date/timestamp
	*
	* @return (array) $response
	*/
	function Stats($dateFrom, $dateTo = null)
	{
		if (!is_numeric($dateFrom)) $dateFrom = strtotime($dateFrom);
		if (empty($dateTo)) $dateTo = time();
		if (!is_numeric($dateTo)) $dateTo = strtotime($dateTo);
		
		if (!$dateFrom || !$dateTo) throw new CeskaPostaException('CeskaPosta: Wrong date', 500);
		
		return $this->get('GET', 'sendParcels/stats', [
			'idContract' => $this->idContract,
			'dateFrom' => date("Y-m-d", $dateFrom),
			'dateTo' => date("Y-m-d", $dateTo),
		]);
	}
	
	
	/**
	* sendParcels - asynchronní import zásilek; vrátí ID transakce dávky a pak je třeba zjistit její stav přes sendParcelsGet()
	* Je možné najednou vkládat více zásilek
	*
	* @param (array) $parcelHeader
	* @param (array) $parcelDataList
	*
	* @return (array) vždy bude obsahovat 'idTransaction' jinak hodí exception
	*/
	function sendParcels($parcelHeader, $parcelDataList)
	{
		$res = $this->get('POST', 'sendParcels', [
			'parcelHeader' => $parcelHeader,
			'parcelDataList' => $parcelDataList,
		]);
		
		if (empty($res['idTransaction'])) {
			throw new CeskaPostaException('Chybí ID transakce', 500, $res);
		}
		
		return $res;
	}
	
	
	/**
	* sendParcelsGet - Vrací stav dávky podle $idTransaction
	*
	* @param (string) $idTransaction - ID transakce získané z sendParcels()
	* @return (array) $response
	*/
	function sendParcelsGet($idTransaction)
	{
		return $this->get('GET', 'sendParcels/idTransaction/'.$idTransaction);
	}
	
	
	/**
	* parcelPrinting - Tisk štítků
	*
	* @param (string) $customerId - Technologické číslo podavatele
	* @param (array) $codes - Sledovací čísla
	* @param (integer) $idForm = 100 - typ formuláře
	* @param (integer) $position = 0 - pozice na A4ce
	* @param (integer) $shiftHorizontal = 0 - v mm
	* @param (integer) $shiftVertical = 0 - v mm
	* @param (integer) $idContract = výchozí - IDCČK zákazníka, pokud se liší od konfigurace
	* @return (array) $response
	*/
	function parcelPrinting($customerId, $codes, $idForm = 100, $position = 0, $shiftHorizontal = 0, $shiftVertical = 0, $idContract = '')
	{
		return $this->get('POST', 'parcelPrinting', [
			'printingHeader' => [
				'customerID' => $customerId, // technologické číslo
				'contractNumber' => strval($idContract ?: $this->idContract),
				'idForm' => $idForm,
				'shiftHorizontal' => $shiftHorizontal,
				'shiftVertical' => $shiftVertical,
				'position' => $position
			],
			'printingData' => (array) $codes,
		]);
	}
	
	
	/**
	* parcelService
	* Synchronní import zásilek; rovnou vrátí podací číslo a nemusí se čekat na zpracování dávky jako u sendParcels()
	* Volání této funkce je omezeno na 1 zásilku o maximálně 5 balících.
	*/
	function parcelService($parcelServiceHeader, $parcelServiceData, $multipartParcelData)
	{
		return $this->get('POST', 'parcelService', [
			// 'idContract' => $this->idContract,
			'parcelServiceHeader' => $parcelServiceHeader,
			'parcelServiceData' => $parcelServiceData,
			'multipartParcelData' => $multipartParcelData
		]);
	}
	
	
	/**
	* parcelStatus - Zjistí stav 1-10 zásilek
	*
	* @param (string/array) $parcel_ids - podací čísla zásilek - maximálně 10 čísel najednou
	* @param (string) $language - jazyk
	*/
	function parcelStatus($parcel_ids, $language = 'cs')
	{
		if (!is_array($parcel_ids)) $parcel_ids = array($parcel_ids);
		
		return $this->get('POST', 'parcelStatus', [
			'parcelIds' => $parcel_ids,
			'language' => $language,
		]);
	}
	
	
	/**
	* parcelDataHistory - Získá historii zásilky
	*
	* @param (string) $parcel_id - podací číslo JEDINÉ zásilky
	* @param (string) $idContract = null ... ID CČK složky podavatele, výchozí se použije z konfigurace
	*/
	function parcelDataHistory($parcel_id, $idContract = null)
	{
		if (empty($idContract)) $idContract = $this->idContract;
		
		if (strlen($idContract) <= 2) throw new CeskaPostaException('Druhý parametr má být $idContract!');
		
		return $this->get('GET', 'parcelDataHistory/idContract/'.$idContract.'/parcelID/'.$parcel_id);
	}
	
	
	/**
	* POST: /letterWithCN22 (Asynchronní)
	* Ukládá data podaných zásilek pro OLZ = Obyčejná listovní zásilka
	*
	* @link https://www.ceskaposta.cz/napi/b2b#letterWithCN22IN
	*
	* @param (array) $requestBody
	*
	* @return (array) vždy bude obsahovat 'idTransaction' jinak hodí exception
	*/
	function letterWithCN22($requestBody)
	{
		$res = $this->get('POST', 'letterWithCN22', $requestBody);
		
		if (empty($res['idTransaction'])) {
			throw new CeskaPostaException('Chybí ID transakce', 500, $res);
		}
		
		return $res;
	}
	
	
	/**
	* GET: /letterWithCN22/idTransaction/{idTransaction}
	* Operace slouží ke zjištění výsledku zpracování dat předaných prostřednictvím operace letterWithCN22
	*
	* @param (string) $idTransaction - ID transakce získané z letterWithCN22()
	* @return (array) $response
	*/
	function letterWithCN22Get($idTransaction)
	{
		return $this->get('GET', 'letterWithCN22/idTransaction/'.$idTransaction);
	}
	
	
	/**
	* Vypíše seznam podacích míst uložená pro daného zákazníka
	*
	* @param (string) $idContract = null ... ID CČK složky podavatele, výchozí se použije z konfigurace
	*/
	function getLocation($idContract = null)
	{
		if (empty($idContract)) $idContract = $this->idContract;
		
		return $this->get('GET', 'location/idContract/'.$idContract);
	}
	
	
	/**
	* Smaže podací místo
	*
	* @param (integer) $locationNumber ... číslo podacího místa
	* @param (string) $idContract = null ... ID CČK složky podavatele, výchozí se použije z konfigurace
	*/
	function deleteLocation($locationNumber, $idContract = null)
	{
		if (empty($idContract)) $idContract = $this->idContract;
		
		$this->get('DELETE', 'location/idContract/'.$idContract.'?locationNumber='.$locationNumber);
		
		// nevrací result, ale pokud je Status Code 200, tak to nehodilo Exception a smazalo se to
		
		return true;
	}
	
	
	/**
	* Uloží podací místo
	*
	* @param (array) $requestBody
	*/
	function postLocation($requestBody)
	{
		if (empty($idContract)) $idContract = $this->idContract;
		
		return $this->get('POST', 'location/idContract/'.$idContract, $requestBody);
	}
	
	
	/**
	* parcelStatuses - Asynchronní funkce (vrátí ID transakce a pak se musí volat parcelStatusesGet() pro výsledek)
	* OBSAHUJE JEN ZÁKLADNÍ DATA O ZÁSILKÁCH
	*
	* @param (string/int) $dateTimeFrom - date/timestamp
	* @param (string/int) $dateTimeTo = time() - date/timestamp
	* @param (string) $customerId = null - NEPOVINNÉ - Technologické číslo podavatele
	* @param (string) $idContract = null
	*
	* @return (array) $response
	*/
	public function parcelStatuses($dateTimeFrom, $dateTimeTo = null, $customerId = null, $idContract = null)
	{
		if (!is_numeric($dateTimeFrom)) $dateTimeFrom = strtotime($dateTimeFrom);
		if (empty($dateTimeTo)) $dateTimeTo = time();
		if (!is_numeric($dateTimeTo)) $dateTimeTo = strtotime($dateTimeTo);
		
		$params = [
			'DateFrom' => date("c", $dateTimeFrom),
			'DateTo' => date("c", $dateTimeTo),
			'idContract' => strval($idContract ?: $this->idContract),
		];
		if (!empty($customerId)) $params['customerId'] = strval($customerId);
		
		$res = $this->get('POST', 'report/parcelStatuses/current/period?'.http_build_query($params));
		
		if (empty($res['idTransaction'])) {
			throw new CeskaPostaException('Chybí ID transakce', 500, $res);
		}
		return $res;
	}
	
	
	/**
	* parcelStatusesGet - Vrací stav dávky podle $idTransaction
	*
	* @param (string) $idTransaction - ID transakce získané z parcelStatuses()
	* @return (array) $response
	*/
	public function parcelStatusesGet($idTransaction)
	{
		return $this->get('GET', 'result/parcelStatuses/current/period/'.$idTransaction);
	}
	
	
	/**
	* submittedConsignments - Asynchronní funkce (vrátí ID transakce a pak se musí volat submittedConsignmentsGet() pro výsledek)
	* OBSAHUJE VÍCE DAT VČETNĚ VARIABILNÍHO SYMBOLU ATP.
	*
	* @param (string/int) $dateTimeFrom - date/timestamp
	* @param (string/int) $dateTimeTo = time() - date/timestamp
	* @param (string) $customerId = null - NEPOVINNÉ - Technologické číslo podavatele
	* @param (string) $idContract = null
	* @param (string) $prefix - nějaký filtr přes prefix zásilek (Např. Date=2023-01-10)
	*
	* @return (array) $response
	*/
	public function submittedConsignments($dateTimeFrom, $dateTimeTo = null, $customerId = null, $idContract = null, $prefix = null)
	{
		if (!is_numeric($dateTimeFrom)) $dateTimeFrom = strtotime($dateTimeFrom);
		if (empty($dateTimeTo)) $dateTimeTo = time();
		if (!is_numeric($dateTimeTo)) $dateTimeTo = strtotime($dateTimeTo);
		
		$params = [
			'DateFrom' => date("c", $dateTimeFrom),
			'DateTo' => date("c", $dateTimeTo),
			'idContract' => strval($idContract ?: $this->idContract),
		];
		if (!empty($customerId)) $params['customerId'] = strval($customerId);
		if (!empty($prefix)) $params['prefix'] = strval($prefix);
		
		$res = $this->get('POST', 'report/submittedConsignments?'.http_build_query($params));
		
		if (empty($res['idTransaction'])) {
			throw new CeskaPostaException('Chybí ID transakce', 500, $res);
		}
		return $res;
	}
	
	
	/**
	* submittedConsignmentsGet - Vrací stav dávky podle $idTransaction
	*
	* @param (string) $idTransaction - ID transakce získané z parcelStatuses()
	* @return (array) $response
	*/
	public function submittedConsignmentsGet($idTransaction)
	{
		return $this->get('GET', 'result/submittedConsignments/'.$idTransaction);
	}
}


#######################################################

/**
* CeskaPostaException
*/
class CeskaPostaException extends Exception
{
	private $extra = [];
	
	/**
	* @param (string) $message - Předmět chyby 
	* @param (integer) $code - Kód
	* @param (mixed) $extra - JSON / Array / String - Extra popis chyby
	*/
	public function __construct($message, $code = 500, $extra = null)
	{
		if (is_string($extra)) {
			$extraTemp = @json_decode($extra, true);
			if (!empty($extraTemp)) {
				if (count($extraTemp) == 1) $extraTemp = current($extraTemp);
				$extra = $extraTemp;
			}
		}
        $this->extra = $extra;
        parent::__construct($message.'~'.@json_encode($extra, JSON_PRETTY_PRINT), $code);
	}
	
	/**
	* Další info o chybě
	*
	* @return (array)
	*/
	public function getExtra()
	{
		return (array) $this->extra;
	}
	
	/**
	* RAW výpis chyby
	*/
	public function __toString()
	{
		return $this->getMessage().' ['.$this->getCode().']: '.var_export($this->getExtra(), true);
	}
}
