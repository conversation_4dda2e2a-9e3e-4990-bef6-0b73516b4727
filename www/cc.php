<?php
/**
 * Created by PhpStorm.
 * User: koblihcz
 * Date: 14.09.2017
 * Time: 14:34
 */

function rrmdir($dir) {
  if (is_dir($dir)) {
    $objects = scandir($dir);
    foreach ($objects as $object) {
      if ($object != "." && $object != "..") {
        if (filetype($dir . "/" . $object) == "dir") {
          rrmdir($dir . "/" . $object);
        } else {
          unlink($dir . "/" . $object);
        }
      }
    }
    reset($objects);
    rmdir($dir);
  }
}

if (empty($_GET["k"])) parse_str(implode('&', array_slice($argv, 1)), $_GET);

if ($_GET["k"] == 'lZwJIL') {
  rrmdir(__DIR__ . "/../temp_web2/cache/");
  @unlink(__DIR__ . "/../temp_web2/btfj.dat");
  rrmdir(__DIR__ . "/../temp_web3/cache/");
  @unlink(__DIR__ . "/../temp_web3/btfj.dat");
  echo "Cache vymazana ...";
}
