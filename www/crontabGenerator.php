<pre><?php
if ($file = fopen(__DIR__.'/../doc/crontab.txt', "r")) {
    while(!feof($file)) {
        $line = fgets($file);


        $e=explode('|',$line);
        $coment=$e[1];
        $coment=trim($coment);
        $e=explode(' ',$e[0],6);
        $minute=$e[0];
        $h=$e[1];
        $d='*';
       // var_dump($e);
        $urle=explode("'",$e[5]);
        $url=$urle[1];
     //   echo PHP_EOL.$coment;
      //  echo PHP_EOL.$url.PHP_EOL.PHP_EOL;
        echo "INSERT INTO `crontab` (`id`, `domena_id`, `min`, `hod`, `den`, `tyden`, `mesic`, `src`, `poznamka`, `vytvoreno`, `zapnuto`, `preset_type`) VALUES (NULL, '5', '$minute', '$h', '$d', '*', '*', 'GET \'$url\'', '$coment', '2022-05-05 19:15:31', '1', '7');".PHP_EOL;



    }
    fclose($file);
}
?>

</pre>

