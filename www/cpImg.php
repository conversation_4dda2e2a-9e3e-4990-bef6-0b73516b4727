<?php
copyImage("detail");
copyImage("big");
copyImage("list");
function copyImage($from){
    $data=file_get_contents($from.".txt");
    $url="https://www.shopcom.cz/pic/web2/product/$from/";
    $i=0;
    $all=explode('#',$data);
    $toEnd=count($all);
    foreach ($all as $file){
        $toEnd--;
        if(!file_exists(__DIR__."/pic/web2/product/$from/".$file)) {
            $i++;
            if (file_put_contents(__DIR__ . "/pic/web2/product/$from/" . $file, file_get_contents($url . $file))) {
                echo $i."---z..".$toEnd." -- ".$file.PHP_EOL;
            } else {
                echo "--------ERROR----------".PHP_EOL;
                die($file);
            }
            if($i>100000) {
                //     break;
            }
        }
    }

}