<pre><?php
umask(0);
$directory = __DIR__."/pic/web2/product/detail/";
$filecount = 0;
$files = glob($directory . "*");
if ($files){
    foreach ($files as $file){
        $fileInfo=new SplFileInfo($file);
        if(!$fileInfo->isFile()){
            continue;
        }
        var_dump($fileInfo->getFilename());
        var_dump($fileInfo->getPath());
        $filename=$fileInfo->getFilename();
        $dir=$fileInfo->getPath();
        $newDir=$dir .'/'. substr($filename, 0, 2);
        if(!file_exists($newDir)){
            if (!mkdir($newDir,0777)) {
                die('Error make dir '.$newDir);
            }
        }
        if(!rename($dir.'/'.$filename,$newDir.'/'.$filename)){
            die("ERROR ".$dir.'/'.$filename.'->'.$newDir.'/'.$filename);
        }



    }
}

/*
    $directory = __DIR__."/pic/web2/product/detail/";
    $filecount = 0;
    $files = glob($directory . "*");
    if ($files){

       foreach ($files as $file){

           $fileInfo=new SplFileInfo($file);
           if(!$fileInfo->isFile()){
               continue;
           }
           echo $fileInfo->getFilename()."#";
       }
    }
*/

?>Success
    </pre>

