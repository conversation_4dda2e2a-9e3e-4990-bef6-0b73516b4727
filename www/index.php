<?php
declare(strict_types=1);

$badBots = [
    '<PERSON><PERSON><PERSON><PERSON>', 'SemrushBot', 'Yande<PERSON>', 'B<PERSON><PERSON><PERSON>ot', 'AhrefsBot',
    'DotBot', '<PERSON>abot', 'aiHitBot', 'spbot', 'MJ12bot', 
    'DeuSu', 'ia_archiver', 'MetaURI', 'FlipboardProxy', 'DataForSeoBot',
    'awario.com', 'Imagesift', 'By<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Amazonbot',
    'Python', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Gpt', 'externalagent',
    'sindresorhus', 'babbar','perplexity','Brave Chrome','SearchBot'
];

$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
foreach ($badBots as $bot) {
    if (stripos($userAgent, $bot) !== false) {
        //http_response_code(403);
        header("Location: https://www.cnn.com");
        exit();
    }
}




function ipInRange($ip, $cidr) {
    list($subnet, $mask) = explode('/', $cidr);
    return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === (ip2long($subnet) & ~((1 << (32 - $mask)) - 1));
}

$blockedCIDRs = [
    '**********/24', '***********/24', '***********/16', '***********/16',
    '**********/16', '*********/16', '**********/16', '***********/16',
    '*********/15', '*********/14', '*********/15', '*********/14',
    '**********/16', '**********/16', '**********/16', '**********/16',
    '**********/16', '**********/16', '**********/16', '**********/16',
    '**********/16', '**********/16', '**********/16', '**********/16',
    '**********/15', '**********/15'
];

$clientIP = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'];
foreach ($blockedCIDRs as $cidr) {
    if (ipInRange($clientIP, $cidr)) {
        http_response_code(403);
        exit();
    }
}



require __DIR__ . '/../vendor/autoload.php';
require_once __DIR__.'/../app/setter.php';

$configurator = App\Bootstrap::boot();
$container = $configurator->createContainer();
$application = $container->getByType(Nette\Application\Application::class);
$application->run();
