<?php
// absolute filesystem path to the web root
define('WWW_DIR', dirname(__FILE__));
// absolute filesystem path to the libraries
define('LIBS_DIR', WWW_DIR . '/../libs');
// absolute filesystem path to the application root
define('APP_DIR', WWW_DIR . '/../app');

// dibi
require LIBS_DIR . '/dibi/dibi.php';
require APP_DIR . '/models/ProductsModel.php';
require APP_DIR . '/models/CatalogsModel.php';
require APP_DIR . '/models/CatPlacesModel.php';
require APP_DIR . '/models/ProParamsModel.php';

$options = [
    'driver'   => 'mysql',
    'host'     => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'sccom',
];
dibi::connect($options);

$rows = dibi::fetchAll("SELECT * FROM import_products");
$pros  = $this->em->getProducts();
$cats  = $this->em->getCatalogs();
$caps = $this->em->getCatPlaces();
$prps = $this->em->getProParams();
dibi::query("TRUNCATE TABLE catplaces");
dibi::query("TRUNCATE TABLE proparams");
$catsArr = array();
foreach ($rows as $key => $value) {
  //zjistim catid
  $catid = 0;
  if (isset($catsArr[$rows->category])) {
    $catid = $catsArr[$rows->category];  
  } else {
    $arr = explode('==>>', $rows->category);
    foreach ($arr as $catname) {
      $catid = dibi::fetch("Select catid from catalogs WHERE catname=%s", $catname);      
      if ($catid) {
        
      } else {
        $catVals = array(
          'catmasid' => $catid,
          'catname' => $catname,
        );
        $catid = $cats->insert($catVals);
      }
    }
  }
  if ($catid == 0) die("catid == 0");    
  //vlozim do zbozi
  $descArr = explode('.', $rows->desc);
  $descs = $descArr[0];
  $vals = array(
    'proid' => $rows->id,
    'procode2' => $rows->ean,
    'proname' => $rows->name,
    'proprice' => $rows->price,
    'prodesc' => $rows->desc,
    'prosdesc' => strip_tags($descs), 
    'proaccess' => $rows->stock, 
  );
  $proid = (int)dibi::fetchSingle("SELECT proid from products WHERE proid=%i", $rows->id);
  if ($proid > 0) {
    unset($vals["proid"]);
    $pros->update($rows->id, $vals);  
  } else {
    $proid = $pros->insert($vals);    
  }
  //zaradim do katalogu
  $caps->insert(array('capproid'=>$proid, 'capcatid'=>$catid));
  
  //vytvorim parametry
  $paramsArr = explode('#:#:#', $rows->variant);
  
  foreach ($paramsArr as $item) {
    $itemArr = explode('=>', $item);
    $prps->insert(array(
      'prpproid' => $proid,
      'prpname' => $itemArr[0],
      'prpvalue' => $itemArr[1],
    ));
  }
}