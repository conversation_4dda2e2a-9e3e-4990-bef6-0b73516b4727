<?php
use <PERSON>\Debugger;
use Nextras\Migrations\Bridges;
use Nextras\Migrations\Controllers;
use Nextras\Migrations\Drivers;
use Nextras\Migrations\Extensions;

require __DIR__ . '/../vendor/autoload.php';

if(!isset($_GET["pass"])||$_GET["pass"]!="tohleSeNesmiNikomuRikat!"){
    die("fuck off!");
}

# Necháme konfigurátor, aby nám sestavil DI kontejner
$container = App\Bootstrap::boot()
    ->createContainer();

$conn =  $container->getByType('Dibi\Connection');
$dbal = new Nextras\Migrations\Bridges\Dibi\Dibi3Adapter($conn);
$driver = new Nextras\Migrations\Drivers\MySqlDriver($dbal);
if (PHP_SAPI === 'cli') {
    $controller = new Nextras\Migrations\Controllers\ConsoleController($driver);
} else {
    $controller = new Nextras\Migrations\Controllers\HttpController($driver);
}

$baseDir = __DIR__;
$controller->addGroup('sql',  __DIR__ ."/../migrations/sql");
$controller->addExtension('sql', new Extensions\SqlHandler($driver));

$controller->run();
