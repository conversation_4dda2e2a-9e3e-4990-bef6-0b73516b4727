# mod_rewrite
<IfModule mod_rewrite.c>
	RewriteEngine On
	# RewriteBase /



  RewriteCond %{HTTP_HOST} ^([^\.]+\.[^\.]+)$ [NC]
  RewriteRule ^(.*)$ https://www.%1/$1 [R=301,L]

  #RewriteCond %{ENV:HTTPS} !^.*on
  #RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1 [R301,L]

  # obrazky a ostatni soubory na https
  #RewriteCond %{ENV:HTTPS} !^.*on
  #RewriteRule (.*)\.(?:(jpe?g|gif|bmp|png|tiff|css|js|pdf))$ https://%{HTTP_HOST}/$1.$2 [R=301,L]

    # use HTTPS
  	RewriteCond %{HTTPS} !on
  	RewriteRule .? https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

	# front controller

	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule !\.(pdf|js|ico|gif|jpg|png|css|rar|zip|tar\.gz)$ index.php [L]
</IfModule>
