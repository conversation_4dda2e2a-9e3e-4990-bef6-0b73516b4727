'use strict';

// nastavení
var settings = {
  browsersync: {
    url: 'http://shopcom.test/',
    browser: 'chrome',
    watch: ['*.html', '*.htm', '*.php']
  },
  css: {
    source: 'www/style/scss/web2.scss',
    source2: 'www/style/scss/web3.scss',
    target: 'www/style/web2/css',
    target2: 'www/style/web3/css',
    filename: 'styles.css',
    watch: ['www/style/scss**/*.scss', 'www/style/scss**/*.css', '!www/style/scss/styles.css'],
    components: ['www/style/scss/base/**/*.scss', '!www/style/scss/base/print.scss', '!www/style/scss/base/variables.scss', '!www/style/scss/base/fonts.scss', 'www/style/scss/components/**/*.scss']
  },
  js: {
    source: ['www/style/js/libs/simple-lightbox.min.js', 'www/style/js/components/**/*.js', 'www/style/js/main.js'],
    target: 'www/style/js/',
    filename: 'scripts.js',
    watch: ['www/style/js/**/*.js', '!www/style/js/scripts.js'],
    components: ['www/style/js/components/**/*.js', 'www/style/js/main.js']
  },
  img: {
    source: 'www/style/web2/img**/*.{gif,jpg,jpeg,png}',
    target: 'img'
  },
  icons: {
    source: 'www/style/web2/img/icons/**/*.svg',
    target: 'www/style/web2/img/',
    filename: 'icons.svg',
    style: '../www/style/web2/cssicons/icons.scss',
    preview: '../www/style/web2/cssicons/icons.html',
    prettycode: true
  }
};

// gulp
var gulp = require('gulp');
  // spojení souborů
  var concat = require('gulp-concat');
  // Cheerio - manipulace v HTML/XML souborech
  var cheerio = require('cheerio')
  // plumber - odchycení chybových hlášek
  var plumber = require('gulp-plumber');
  // přejmenování souborů
  var rename = require("gulp-rename");
  // sourcemaps - generování map zdrojů
  var sourcemaps = require('gulp-sourcemaps');
  // through2 - Node wrapper
  var through2 = require('through2');
  // Vinyl - konvertor streamu
  var Vinyl = require('vinyl');
// BrowserSync - live realod, server, ovládání prohlížeče
var browserSync = require('browser-sync');
// SASS - generování CSS z preprocesoru
var sass = require('gulp-sass')(require('sass'));
// postCSS - postprocessing CSS (minifikace, autoprefixer...)
var postcss = require('gulp-postcss');
  var autoprefixer = require('autoprefixer');
  var cssnano = require('cssnano');
  var flexbugs = require('postcss-flexbugs-fixes');
  var pxtorem = require('postcss-pxtorem');
// CSScomb - uhlazení SASS souborů (řazení vlastností, odsazení...)
var csscomb = require('gulp-csscomb');
// lintování CSS
var stylelint = require('gulp-stylelint');
// minifikace JavaScriptu
var uglify = require('gulp-uglify');
// lintování JavaScriptu
var jshint = require('gulp-jshint');
// Prettier - uhlazení JS souborů
var prettier = require('gulp-prettier');
// Imagemin - optimalizace obrázků
var imagemin = require('gulp-imagemin');
// generování SVG spritů a ikon
var svgstore = require('gulp-svgstore');
// minimalizace SVG
var svgmin = require('gulp-svgmin');

// postCSS pluginy a nastavení
var postcssPlugins = [
  flexbugs(),
  pxtorem(),
  autoprefixer(),
  cssnano()
];

// výpis chybových hlášek
var onError = function (err) {
  console.log(err);
  this.emit('end');
};

// SASS kompilace
function wtSass() {
  return gulp.src(settings.css.source)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(sourcemaps.init())
    .pipe(sass())
    .pipe(rename(settings.css.filename))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(settings.css.target))
    .pipe(browserSync.stream());
};

// SASS kompilace
function wtSass2() {
    return gulp.src(settings.css.source2)
        .pipe(plumber({ errorHandler: onError }))
        .pipe(sourcemaps.init())
        .pipe(sass())
        .pipe(rename(settings.css.filename))
        .pipe(sourcemaps.write('.'))
        .pipe(gulp.dest(settings.css.target2))
        .pipe(browserSync.stream());
};

// CSS kompilace (produkce)
function wtCss() {
  return gulp.src(settings.css.source)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(sourcemaps.init())
    .pipe(sass({ style: 'expanded' }))
    .pipe(postcss(postcssPlugins))
    .pipe(rename(settings.css.filename))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(settings.css.target));
};

// CSS kompilace (produkce)
function wtCss2() {
    return gulp.src(settings.css.source2)
        .pipe(plumber({ errorHandler: onError }))
        .pipe(sourcemaps.init())
        .pipe(sass({ style: 'expanded' }))
        .pipe(postcss(postcssPlugins))
        .pipe(rename(settings.css.filename))
        .pipe(sourcemaps.write('.'))
        .pipe(gulp.dest(settings.css.target2));
};

// CSScomb - úpravy SASS souborů (řazení vlastností, odsazení...)
function wtCssComb() {
  return gulp.src(settings.css.components, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(csscomb())
    .pipe(gulp.dest('./'));
};

// CSS - lintování (Stylelint)
function wtStyleLint() {
  return gulp.src(settings.css.components, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(stylelint({
      reporters: [
        {
          formatter: 'string',
          console: true
        }
      ]
    }));
};

// JavaScript - spojení souborů
function wtConcatJs() {
  return gulp.src(settings.js.source, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(sourcemaps.init())
    .pipe(concat(settings.js.target + settings.js.filename))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest('./'));
};

// JavaScript - spojení a minifikace (produkce)
function wtJs() {
  return gulp.src(settings.js.target + settings.js.filename, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(sourcemaps.init())
    .pipe(uglify())
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest('./'));
};

// JavaScript - lintování
function wtJsLint() {
  return gulp.src(settings.js.components)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(jshint())
    .pipe(jshint.reporter('default'))
    .pipe(jshint.reporter('fail'));
};

// Prettier - uhlazení JS souborů
function wtPrettier() {
  return gulp.src(settings.js.components, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(prettier({ singleQuote: true }))
    .pipe(gulp.dest('./'));
};

// optimalizace obrázků
function wtImages() {
  return gulp.src(settings.img.source)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(imagemin({
      interlaced: true,
      pngquant: true,
      progressive: true
    }))
    .pipe(gulp.dest(settings.img.target));
};

// generování SVG sprite ikon
function wtSvgIcons() {
  return gulp.src(settings.icons.source)
    .pipe(plumber({ errorHandler: onError }))
    .pipe(svgstore())
    .pipe(through2.obj(function (file, encoding, cb) {
      var $ = cheerio.load(file.contents.toString(), { xmlMode: true });

      // odstraní fill atributy u souborů, které nemají v názvu color
      $('symbol').not('[id*="color"]').find('*').removeAttr('fill');
      // odstraní style tagy
      $('[style]').removeAttr('style');

      // vytáhneme si název, výšku a šířku
      var data = $('svg > symbol').map(function() {
        var $this = $(this);
        var name = $this.attr('id');
        var viewBox = $this.attr('viewBox').split(' ');

        return {
          name: name,
          width: viewBox[2],
          height: viewBox[3],
        };
      }).get();

      // převedeme na SASS formát
      var dataToStyles = "";
      for (var i = 0; i < data.length; i++) {
        dataToStyles = dataToStyles + '\n.icon--' + data[i].name + ' {' + '\n';
          dataToStyles = dataToStyles + '  width: ' + data[i].width + 'px;\n\n';
          dataToStyles = dataToStyles + '  &:before {' + '\n';
          dataToStyles = dataToStyles + '    padding-top: (' + data[i].height + ' / ' + data[i].width + ') * 100%;' + '\n';
          dataToStyles = dataToStyles + '  }' + '\n';
        dataToStyles = dataToStyles + '}' + '\n';
      }

      // uložíme do soubou
      var fileSASS = new Vinyl({
        path: settings.icons.style,
        contents: new Buffer.from(dataToStyles)
      });

      // vygenerujeme náhledový HTML soubor
      var dataToPreview = "";
      dataToPreview = dataToPreview + '<!DOCTYPE html><html lang="cs"><head><meta charset="utf-8"><title>SVG preview</title><link rel="stylesheet" href="/www/style/web2/css/styles.css"></head><body>' + '\n'
      for (var i = 0; i < data.length; i++) {
        dataToPreview = dataToPreview + '<div style="padding:5px;margin:5px;display:inline-block;border:1px dotted gray;">' + '\n'
        dataToPreview = dataToPreview + '<span class="icon icon--' + data[i].name + '">' + '\n'
        dataToPreview = dataToPreview + '  <svg class="icon__svg" xmlns:xlink="http://www.w3.org/1999/xlink">' + '\n'
        dataToPreview = dataToPreview + '    <use xlink:href="/www/style/web2/img/icons.svg#' + data[i].name + '" x="0" y="0" width="100%" height="100%"></use>' + '\n'
        dataToPreview = dataToPreview + '  </svg>' + '\n'
        dataToPreview = dataToPreview + '</span>' + '\n'
        dataToPreview = dataToPreview + '</div>' + '\n'
      }
      dataToPreview = dataToPreview + '</body>' + '\n'

      // uložíme do soubou
      var fileHTML = new Vinyl({
        path: settings.icons.preview,
        contents: new Buffer.from(dataToPreview)
      });

      file.contents = new Buffer.from($.xml());
      this.push(fileSASS);
      this.push(fileHTML);
      this.push(file);
      cb();

    }))
    .pipe(gulp.dest(settings.icons.target));
};

// optimalizace SVG sprite
function wtSvgOptimize() {
  return gulp.src(settings.icons.target + settings.icons.filename, { base: './' })
    .pipe(plumber({ errorHandler: onError }))
    .pipe(svgmin({
        plugins: [
          { removeUselessDefs: false },
          { removeXMLProcInst: false },
          { removeDoctype: false },
          { removeTitle: false },
          { cleanupIDs: false },
          { removeViewBox: false }
        ],
        js2svg: { pretty: settings.icons.prettycode }
    }))
    .pipe(gulp.dest('./'));
};

// sledování změn souborů
function wtWatch(cb) {

  // nastavení BrowserSync:
  browserSync.init({
    proxy: settings.browsersync.url,
    browser: settings.browsersync.browser
  });

  gulp.watch( settings.icons.source, wtSvgIcons ).on('change', browserSync.reload );
  gulp.watch( settings.css.watch, wtSass );
  gulp.watch( settings.css.watch, wtSass2 );
  gulp.watch( settings.js.watch, wtConcatJs ).on('change', browserSync.reload );
  gulp.watch( settings.browsersync.watch ).on('change', browserSync.reload );

  cb();
};

// aliasy tasků
  // úpravy před nahráním do produkce
  exports.deploy = gulp.parallel( gulp.series( wtCssComb, wtCss, wtStyleLint ), gulp.series( wtPrettier, wtConcatJs, wtJs, wtJsLint ), wtImages, wtSvgOptimize );

  // generování CSS
  exports.makecss = gulp.series( wtCssComb, wtCss,wtCss2 );

  // generování JavaScriptu
  exports.makejs = gulp.series( wtPrettier, wtConcatJs, wtJs );

  // generování ikon + optimalizace
  exports.icons = gulp.series( wtSvgIcons, wtSvgOptimize );

  // defaultni task
  exports.default = gulp.parallel( wtWatch );
