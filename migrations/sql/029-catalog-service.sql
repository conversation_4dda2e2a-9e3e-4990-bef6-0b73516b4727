CREATE TABLE `service` (`id` INT NOT NULL AUTO_INCREMENT ,`type` TINYINT(1) NOT NULL DEFAULT '1', `name` VARCHAR(65) NOT NULL , `token` VARCHAR(32) NOT NULL , PRIMARY KEY (`id`)) ENGINE = InnoDB;
CREATE TABLE `service_price` (`id` INT NOT NULL AUTO_INCREMENT , `price_from` DOUBLE NOT NULL COMMENT 'cena zboží od' , `price_to` DOUBLE NOT NULL COMMENT 'cena zboží do' , `price_service_buy` DOUBLE NOT NULL COMMENT ' nákupní cena služby ' , `price_service_sell` DOUBLE NOT NULL COMMENT 'prodejní cena služby ' , `variant` TINYINT(4) NOT NULL COMMENT 'varinata' , `service_id` INT NOT NULL , PRIMARY KEY (`id`), INDEX (`service_id`)) ENGINE = InnoDB;
ALTER TABLE `service_price` ADD FOREIGN KEY (`service_id`) REFERENCES `service`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `catalogs_web2` ADD `service_guarantee` INT NULL AFTER `catmarginfix15_web3`, ADD `service_insurance` INT NULL AFTER `service_guarantee`, ADD INDEX (`service_guarantee`), ADD INDEX (`service_insurance`);
ALTER TABLE `catalogs_web2` ADD FOREIGN KEY (`service_guarantee`) REFERENCES `service`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `catalogs_web2` ADD FOREIGN KEY (`service_insurance`) REFERENCES `service`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;