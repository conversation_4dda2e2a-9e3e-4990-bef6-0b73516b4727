ALTER TABLE `import_products_web2` ADD `vendor_id` INT NULL;
ALTER TABLE `import_products_web2` ADD INDEX(`vendor_id`);
ALTER TABLE `import_products_web2` ADD FOREIGN KEY (`vendor_id`) REFERENCES `vendors`(`venid`) ON DELETE SET NULL ON UPDATE CASCADE;
UPDATE import_products_web2
    left JOIN vendors ON (import_products_web2.provenid  = vendors.coll_on_products)
    SET import_products_web2.vendor_id = vendors.venid;