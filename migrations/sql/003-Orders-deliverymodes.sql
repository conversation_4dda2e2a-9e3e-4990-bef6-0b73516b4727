ALTER TABLE `orders_web2` ADD INDEX(`orddelid`);
ALTER TABLE `orders_web3` ADD INDEX(`orddelid`);
ALTER TABLE `orders_web2` CHANGE `orddelid` `orddelid` INT(10) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `orders_web3` CHANGE `orddelid` `orddelid` INT(10) UNSIGNED NULL DEFAULT NULL;
ALTER TABLE `orders_web2` ADD FOREIGN KEY (`orddelid`) REFERENCES `deliverymodes`(`delid`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `orders_web3` ADD FOREIGN KEY (`orddelid`) REFERENCES `deliverymodes`(`delid`) ON DELETE SET NULL ON UPDATE CASCADE;