-- <PERSON><PERSON><PERSON><PERSON> c<PERSON>, u<PERSON> nen<PERSON> třeba
alter table products_web2 drop column propricea_web2_bck;
alter table products_web2 drop column propricea_web3_bck;

-- pr<PERSON><PERSON><PERSON><PERSON><PERSON> skladová cena
alter table products_web2 add prostorepriceavg double null;
alter table proqtystores add pqspriceavg double null;

UPDATE products_web2
SET prostorepriceavg = (SELECT round(SUM(stiqtyfree*stiprice)/SUM(stiqtyfree)) as stiprice FROM stoitems WHERE stiqtyfree > 0 AND stiproid=proid)
where (proqty_web2+proqty_web3) > 0 and prostorepriceavg is null;

UPDATE proqtystores
SET pqspriceavg = (SELECT round(SUM(stiqtyfree*stiprice)/SUM(stiqtyfree)) as stiprice FROM stoitems WHERE stiqtyfree > 0 AND stiproid=pqsproid)
where pqsqty > 0 and pqspriceavg is null;