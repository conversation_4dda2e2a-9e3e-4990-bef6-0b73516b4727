ALTER TABLE `products_web2` CHANGE `proservices_web2` `proservices_web2` INT(11) NULL DEFAULT NULL, CHANGE `proservices_web3` `proservices_web3` INT(11) NULL DEFAULT NULL;
ALTER TABLE `products_web2` ADD INDEX(`proservices_web2`);
ALTER TABLE `products_web2` ADD INDEX(`proservices_web3`);

ALTER TABLE `products_web2` ADD FOREIGN KEY (`proservices_web2`) REFERENCES `ordservices_web2`(`orsid`) ON DELETE SET NULL ON UPDATE CASCADE; ALTER TABLE `products_web2` ADD FOREIGN KEY (`proservices_web3`) REFERENCES `ordservices_web2`(`orsid`) ON DELETE SET NULL ON UPDATE CASCADE;