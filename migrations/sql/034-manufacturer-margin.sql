ALTER TABLE `margin` CHANGE `vendor_id` `vendor_id` INT(11) NULL DEFAULT NULL;
ALTER TABLE `margin` ADD `manufacturer_id` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `vendor_id`;
ALTER TABLE `margin` ADD FOREIGN KEY (`manufacturer_id`) REFERENCES `manufacturers_web2`(`manid`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `margin` ADD `price_from` DOUBLE NULL DEFAULT NULL AFTER `margin`, ADD `price_to` DOUBLE NULL DEFAULT NULL AFTER `price_from`;
