create table ecomail_feed
(
    id          int auto_increment,
    campaign_id int not null,
    product_id  int not null,
    constraint ecomail_feed_pk
        primary key (id)
)
    collate = utf8_czech_ci;

alter table ecomail_feed
    add constraint ecomail_feed_ecomail_campaign_id_fk
        foreign key (campaign_id) references ecomail_campaign (id)
            on update cascade on delete cascade;

alter table ecomail_campaign drop column items;