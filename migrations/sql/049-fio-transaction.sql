alter table fio_transaction
    change trans_id ordid varchar(50) null;

alter table fio_transaction
    drop column account;

alter table fio_transaction
    drop column amount;

alter table fio_transaction
    drop column currency;

alter table fio_transaction
    drop column description;

alter table fio_transaction
    add resulttext VARCHAR(255) null after datec;

alter table fio_transaction
    drop column language;

alter table fio_transaction
    drop column dms_ok;

alter table fio_transaction
    drop column samaksats;

alter table fio_transaction
    drop column reverse;

alter table fio_transaction
    change status srcode int not null;

alter table fio_transaction
    change errcode prcode int null;

alter table fio_transaction
    change ordid order_id int null;

alter table fio_transaction
    change datec date_created datetime null after resulttext;

alter table fio_transaction
    change dateu date_updated datetime null;

alter table fio_transaction
    change client_ip_addr ip varchar(50) null;


alter table fio_transaction
    modify srcode int null;

ALTER TABLE `fio_transaction` CHANGE `order_id` `order_id` INT(10) UNSIGNED NULL;
ALTER TABLE `fio_transaction` ADD FOREIGN KEY (`order_id`) REFERENCES `orders_web2`(`ordid`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `fio_transaction` CHANGE `order_id` `order_web2` INT(10) UNSIGNED NULL DEFAULT NULL;


ALTER TABLE `fio_transaction` ADD `order_web3` INT(10) UNSIGNED NULL AFTER `order_web2`, ADD INDEX (`order_web3`);

ALTER TABLE `fio_transaction` ADD FOREIGN KEY (`order_web3`) REFERENCES `orders_web3`(`ordid`) ON DELETE CASCADE ON UPDATE CASCADE;