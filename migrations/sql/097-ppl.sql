create table pplpoints
(
    pplid           int unsigned auto_increment
        primary key,
    pplid2          varchar(255)              null,
    pplname         varchar(255)         null,
    pplstreet       varchar(255)         null,
    pplcity         varchar(255)         null,
    pplpostcode     varchar(255)         null,
    pplcountry      varchar(255)         null,
    pplemail        varchar(255)         null,
    pplphone        varchar(255)         null,
    pplopeninghours text                 null,
    pplstatus       tinyint(1) default 0 not null,
    pplurl          varchar(255)         null,
    pplurlphoto     varchar(255)         null,
    pplurlself      varchar(255)         null,
    pplgpsn         varchar(255)         null,
    pplgpse         varchar(255)         null,
    pplnavigation   text                 null,
    ppldatec        datetime             null,
    ppldateu        datetime             null
)
    collate = utf8_unicode_ci;

create index id2_i
    on pplpoints (pplid2);


alter table orders_web2 add orddelspecppl varchar(100) null;
alter table orders_web3 add orddelspecppl varchar(100) null;

