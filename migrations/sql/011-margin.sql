ALTER TABLE `vendors` CHANGE `venid` `venid` INT UNSIGNED NOT NULL AUTO_INCREMENT;

CREATE TABLE `margin`(
                                   `id` INT NOT NULL AUTO_INCREMENT,
                                   `catalog_id`  INT(8) UNSIGNED NOT NULL,
                                   `vendor_id` INT NOT NULL,
                                   `web_id` INT NOT NULL,
                                   `margin` DOUBLE NOT NULL,
                                   `type` INT NOT NULL,
                                   `use_vendor_price` BOOLEAN NOT NULL DEFAULT FALSE,
                                   `fix_price` BOOLEAN NOT NULL DEFAULT FALSE,
                                   `date_create` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   `date_update` DATETIME ON UPDATE CURRENT_TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

                                   PRIMARY KEY(`id`)
) ENGINE = InnoDB;

CREATE TABLE `web`(
                                `id` INT NOT NULL AUTO_INCREMENT,
                                `name` VARCHAR(30) NOT NULL,
                                PRIMARY KEY(`id`)
) ENGINE = InnoDB;

INSERT INTO `web` (`id`, `name`) VALUES (1, 'hracky'), (2, 'sccom.cz'), (3, 'shopcom.cz');

ALTER TABLE `vendors` CHANGE `venid` `venid` INT(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `margin` ADD INDEX(`catalog_id`);
ALTER TABLE `margin` ADD INDEX(`vendor_id`);
ALTER TABLE `margin` ADD INDEX(`web_id`);

ALTER TABLE `margin` ADD FOREIGN KEY (`catalog_id`) REFERENCES `catalogs_web2`(`catid`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `margin` ADD FOREIGN KEY (`web_id`) REFERENCES `web`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `margin` ADD FOREIGN KEY (`vendor_id`) REFERENCES `vendors`(`venid`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `vendors` ADD `coll_on_catalog` INT NULL AFTER `venname`;