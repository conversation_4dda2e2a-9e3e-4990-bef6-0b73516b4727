DROP TABLE IF EXISTS `recycle_fee`;

create table recycle_fee
(
    id    int auto_increment
        primary key,
    code  varchar(100) null,
    name  varchar(255) null,
    value double       null
)
    collate = utf8_czech_ci;

alter table catalogs_web2
    add recycle_fee int null;

alter table catalogs_web2
    add constraint catalogs_web2_recycle_fee_fk
        foreign key (recycle_fee) references recycle_fee (id)
            on update cascade on delete set null;