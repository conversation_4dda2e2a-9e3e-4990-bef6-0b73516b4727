ALTER TABLE `products_web2` ADD `vendor_id` INT NULL AFTER `provenid`;
ALTER TABLE `products_web2` ADD INDEX(`vendor_id`);
ALTER TABLE `products_web2` ADD FOREIGN KEY (`vendor_id`) REFERENCES `vendors`(`venid`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `vendors` CHANGE `coll_on_products` `coll_on_products` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
UPDATE products_web2
    left JOIN vendors ON (products_web2.provenid  = vendors.coll_on_products)
    SET products_web2.vendor_id = vendors.venid;