ALTER TABLE `menus_web2` CHANGE `mensrcid` `mensrcid` INT(11) UNSIGNED NULL DEFAULT '0' COMMENT 'ID zaznamu';
ALTER TABLE `menus_web3` CHANGE `mensrcid` `mensrcid` INT(11) UNSIGNED NULL DEFAULT '0' COMMENT 'ID zaznamu';
ALTER TABLE `pages_web2` CHANGE `pagid` `pagid` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK';
ALTER TABLE `pages_web2` CHANGE `pagid` `pagid` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK';
ALTER TABLE `menus_web2` ADD INDEX(`mensrcid`);
ALTER TABLE `menus_web3` ADD INDEX(`mensrcid`);

ALTER TABLE `menus_web2` ADD FOREIGN KEY (`mensrcid`) REFERENCES `pages_web2`(`pagid`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `menus_web3` ADD FOREIGN KEY (`mensrcid`) REFERENCES `pages_web3`(`pagid`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `menus_web2` CHANGE `menmasid` `menmasid` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL COMMENT 'ID nadrizene urovne, 0 - korenova uroven';
ALTER TABLE `menus_web3` CHANGE `menmasid` `menmasid` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL COMMENT 'ID nadrizene urovne, 0 - korenova uroven';
UPDATE menus_web2 SET `menmasid`=null WHERE `menmasid`=0;
UPDATE menus_web3 SET `menmasid`=null WHERE `menmasid`=0;

ALTER TABLE `menus_web2` ADD FOREIGN KEY (`menmasid`) REFERENCES `menus_web2`(`menid`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `menus_web3` ADD FOREIGN KEY (`menmasid`) REFERENCES `menus_web2`(`menid`) ON DELETE SET NULL ON UPDATE CASCADE;