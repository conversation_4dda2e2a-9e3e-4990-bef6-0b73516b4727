CREATE TABLE `vendor_update_log` (`id` INT NOT NULL AUTO_INCREMENT , `vendor_id` INT NOT NULL , `start` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP , `end` DATETIME NULL DEFAULT NULL , PRIMARY KEY (`id`), INDEX (`vendor_id`)) ENGINE = InnoDB;
ALTER TABLE `vendor_update_log` ADD FOREIGN KEY (`vendor_id`) REFERENCES `vendors`(`venid`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `vendor_update_log` ADD `status` INT(1) NOT NULL DEFAULT '1' AFTER `end`;