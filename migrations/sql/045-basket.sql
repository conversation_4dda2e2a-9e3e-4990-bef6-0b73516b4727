CREATE TABLE `basket`(
                                   `id` INT NOT NULL AUTO_INCREMENT,
                                   `coupon_id` INT NULL DEFAULT NULL,
                                   `hash` VARCHAR(36) CHARACTER SET ASCII COLLATE ascii_bin NOT NULL,
                                   `ip` VARBINARY(16) NOT NULL,
                                   `user_id` MEDIUMINT(8) NULL DEFAULT NULL,
                                   `delivery_id` INT(10) UNSIGNED NULL DEFAULT NULL,
                                   `payment_id` INT(10) UNSIGNED NULL DEFAULT NULL,
                                   PRIMARY KEY(`id`),
                                   INDEX(`hash`),
                                   INDEX(`delivery_id`),
                                   INDEX(`payment_id`)
) ENGINE = InnoDB;

ALTER TABLE
    `basket` ADD FOREIGN KEY(`coupon_id`) REFERENCES `coupon`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE
    `basket` ADD FOREIGN KEY(`delivery_id`) REFERENCES `deliverymodes`(`delid`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE
    `basket` ADD FOREIGN KEY(`payment_id`) REFERENCES `deliverymodes`(`delid`) ON DELETE SET NULL ON UPDATE CASCADE;



CREATE TABLE `basket_item`(
                                        `id` INT NOT NULL AUTO_INCREMENT,
                                        `basket_id` INT NOT NULL,
                                        `qty` INT NOT NULL DEFAULT '1',
                                        `product_id` INT(10) UNSIGNED NOT NULL,
                                        `service_insurance` INT NULL DEFAULT NULL,
                                        `service_guarantee` INT NULL DEFAULT NULL,
                                        `order_service` INT NULL DEFAULT NULL,
                                        `gift_type` INT(1) NOT NULL DEFAULT '0',
                                        PRIMARY KEY(`id`),
                                        INDEX(`basket_id`),
                                        INDEX(`product_id`),
                                        INDEX(`service_insurance`),
                                        INDEX(`service_guarantee`),
                                        INDEX (`order_service`)
) ENGINE = InnoDB;

ALTER TABLE
    `basket_item` ADD FOREIGN KEY(`basket_id`) REFERENCES `basket`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE
    `basket_item` ADD FOREIGN KEY(`service_guarantee`) REFERENCES `service_price`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE
    `basket_item` ADD FOREIGN KEY(`service_insurance`) REFERENCES `service_price`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE
    `basket_item` ADD FOREIGN KEY(`product_id`) REFERENCES `products_web2`(`proid`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE
    `basket_item` ADD FOREIGN KEY (`order_service`) REFERENCES `ordservices_web2`(`orsid`) ON DELETE SET NULL ON UPDATE CASCADE;