CREATE TABLE `campaign`(
                           `id` INT NOT NULL AUTO_INCREMENT,
                           `name` VARCHAR(120) NOT NULL,
                           `val_type` INT(1) NOT NULL,
                           `type` INT(1) NOT NULL,
                           `val` INT NOT NULL,
                           `product_id` INT(10) UNSIGNED NULL,
                           `catalog_id` INT(8) UNSIGNED NULL,
                           `manufacturer_id` INT(10) UNSIGNED NULL,
                           `valid_to` DATETIME NOT NULL,
                           PRIMARY KEY(`id`),
                           INDEX(`product_id`),
                           INDEX(`catalog_id`),
                           INDEX(`manufacturer_id`)
) ENGINE = InnoDB;

ALTER TABLE
    `campaign` ADD FOREIGN KEY(`catalog_id`) REFERENCES `catalogs_web2`(`catid`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE
    `campaign` ADD FOREIGN KEY(`product_id`) REFERENCES `products_web2`(`proid`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE
    `campaign` ADD FOREIGN KEY(`manufacturer_id`) REFERENCES `manufacturers_web2`(`manid`) ON DELETE CASCADE ON UPDATE CASCADE;


CREATE TABLE `coupon`(
                                   `id` INT NOT NULL AUTO_INCREMENT,
                                   `code` VARCHAR(65) NOT NULL,
                                   `use` BOOLEAN NOT NULL DEFAULT FALSE,
                                   `campaign_id` INT NOT NULL,
                                   PRIMARY KEY(`id`),
                                   INDEX(`campaign_id`)
) ENGINE = InnoDB;


ALTER TABLE `coupon` ADD FOREIGN KEY (`campaign_id`) REFERENCES `campaign`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `orders_web2` ADD `coupon_id` INT NULL DEFAULT NULL, ADD INDEX (`coupon_id`);
ALTER TABLE `orders_web2` ADD FOREIGN KEY (`coupon_id`) REFERENCES `coupon`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `orders_web3` ADD `coupon_id` INT NULL DEFAULT NULL, ADD INDEX (`coupon_id`);
ALTER TABLE `orders_web3` ADD FOREIGN KEY (`coupon_id`) REFERENCES `coupon`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `coupon` ADD UNIQUE(`code`);