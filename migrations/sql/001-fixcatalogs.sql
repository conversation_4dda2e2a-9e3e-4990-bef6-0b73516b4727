INSERT INTO `catalogs_web2` (`catid`, `catid2`, `catcodeven`, `catkey`, `catmasid`, `catrootid`, `catacccatid`, `catcamid`, `catcamparam`, `catinscatid`, `catgifts_web1`, `catgifts_web2`, `catgifts_web3`, `catgiftscumulate_web2`, `catgiftscumulate_web3`, `catmarginaff_web2`, `catmarginaff_web3`, `catmarginmall_web2`, `catmarginmall_web3`, `cataffadd_web2`, `cataffadd_web3`, `catmargin1_web1`, `catmargin1_web2`, `catmargin1_web3`, `catmargintype1_web1`, `catmargintype1_web2`, `catmargintype1_web3`, `catmarginfix1_web1`, `catmarginfix1_web2`, `catmarginfix1_web3`, `catmarginfix2_web1`, `catmarginfix2_web2`, `catmarginfix2_web3`, `catmargin2_web1`, `catmargin2_web2`, `catmargin2_web3`, `catmargintype2_web1`, `catmargintype2_web2`, `catmargintype2_web3`, `catlevel`, `catclass`, `catname`, `catkeywords`, `catsalestat`, `catdescription`, `catdesc`, `catorder`, `catrootorder`, `catcounter`, `catpath`, `catpathids`, `catparams`, `catpathheureka`, `catpathgoogle`, `catpathzbozi`, `catmargin`, `catstatus`, `catdatec`, `catdateu`, `catvendorkey`, `catmargin3_web1`, `catmargin3_web2`, `catmargin3_web3`, `catmargintype3_web1`, `catmargintype3_web2`, `catmargintype3_web3`, `catmarginfix3_web1`, `catmarginfix3_web2`, `catmarginfix3_web3`, `catmargin4_web1`, `catmargin4_web2`, `catmargin4_web3`, `catmargins4_web1`, `catmargins4_web2`, `catmargins4_web3`, `catmargintype4_web1`, `catmargintype4_web2`, `catmargintype4_web3`, `catmarginfix4_web1`, `catmarginfix4_web2`, `catmarginfix4_web3`, `catdelfree_web2`, `catdelfree_web3`, `catservices_web2`, `catservices_web3`, `catmargin5_web1`, `catmargin5_web2`, `catmargin5_web3`, `catmargintype5_web1`, `catmargintype5_web2`, `catmargintype5_web3`, `catmarginfix5_web1`, `catmarginfix5_web2`, `catmarginfix5_web3`, `catmargin6_web1`, `catmargin6_web2`, `catmargin6_web3`, `catmargintype6_web1`, `catmargintype6_web2`, `catmargintype6_web3`, `catmarginfix6_web1`, `catmarginfix6_web2`, `catmarginfix6_web3`, `catmargin7_web1`, `catmargin7_web2`, `catmargin7_web3`, `catmargintype7_web1`, `catmargintype7_web2`, `catmargintype7_web3`, `catmarginfix7_web1`, `catmarginfix7_web2`, `catmarginfix7_web3`, `catmargin8_web1`, `catmargin8_web2`, `catmargin8_web3`, `catmargintype8_web1`, `catmargintype8_web2`, `catmargintype8_web3`, `catmarginfix8_web1`, `catmarginfix8_web2`, `catmarginfix8_web3`, `catmargin9_web1`, `catmargin9_web2`, `catmargin9_web3`, `catmargintype9_web1`, `catmargintype9_web2`, `catmargintype9_web3`, `catmarginfix9_web1`, `catmarginfix9_web2`, `catmarginfix9_web3`, `catmargin10_web1`, `catmargin10_web2`, `catmargin10_web3`, `catmargintype10_web1`, `catmargintype10_web2`, `catmargintype10_web3`, `catmarginfix10_web1`, `catmarginfix10_web2`, `catmarginfix10_web3`, `catmargin11_web1`, `catmargin11_web2`, `catmargin11_web3`, `catmargintype11_web1`, `catmargintype11_web2`, `catmargintype11_web3`, `catmarginfix11_web1`, `catmarginfix11_web2`, `catmarginfix11_web3`, `catmargin12_web1`, `catmargin12_web2`, `catmargin12_web3`, `catmargintype12_web1`, `catmargintype12_web2`, `catmargintype12_web3`, `catmarginfix12_web1`, `catmarginfix12_web2`, `catmarginfix12_web3`) VALUES (0, NULL, NULL, '', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, '0', NULL, 'root', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '2022-04-09 07:41:36.000000', '2022-04-09 07:41:36.000000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0', NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', '0');
update catalogs_web2 set catid=0 where catname='root';


ALTER TABLE `catalogs_web2` CHANGE `catmasid` `catmasid` INT(8) UNSIGNED NULL DEFAULT '0' COMMENT 'ID nadrizene urovne, 0 - korenova uroven';

update catalogs_web2 set catmasid=null WHERE catmasid NOT in ( SELECT catid FROM (SELECT catid from catalogs_web2) as x);

ALTER TABLE `catalogs_web2` ADD FOREIGN KEY (`catmasid`) REFERENCES `catalogs_web2`(`catid`) ON DELETE CASCADE ON UPDATE CASCADE;


ALTER TABLE `catalogs_web2` CHANGE `catrootid` `catrootid` INT(8) UNSIGNED NULL DEFAULT NULL COMMENT 'ID korenove urovne';
ALTER TABLE `catalogs_web2` ADD INDEX(`catrootid`);
ALTER TABLE `catalogs_web2` ADD FOREIGN KEY (`catrootid`) REFERENCES `catalogs_web2`(`catid`) ON DELETE SET NULL ON UPDATE CASCADE;

update catalogs_web2 set catmasid=null where catname='root';

