ALTER TABLE `margin` ADD `priority` INT NULL DEFAULT NULL;
ALTER TABLE `margin` ADD `force_ignore_price_eu` BOOLEAN NOT NULL DEFAULT FALSE AFTER `priority`;

ALTER TABLE `products_web2` ADD `margin_web2` INT NULL DEFAULT NULL, ADD `margin_web3` INT NULL DEFAULT NULL AFTER `margin_web2`, ADD INDEX (`margin_web2`), ADD INDEX (`margin_web3`);
ALTER TABLE `products_web2` ADD FOREIGN KEY (`margin_web2`) REFERENCES `margin`(`id`) ON DELETE SET NULL ON UPDATE CASCADE; ALTER TABLE `products_web2` ADD FOREIGN KEY (`margin_web3`) REFERENCES `margin`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `products_web2` ADD `need_recalculate_price` BOOLEAN NOT NULL DEFAULT FALSE , ADD INDEX (`need_recalculate_price`);


<PERSON>TER TABLE `import_products_web2` ADD `margin_web2` INT NULL DEFAULT NULL, ADD `margin_web3` INT NULL DEFAULT NULL AFTER `margin_web2`, ADD INDEX (`margin_web2`), ADD INDEX (`margin_web3`);
ALTER TABLE `import_products_web2` ADD FOREIGN KEY (`margin_web2`) REFERENCES `margin`(`id`) ON DELETE SET NULL ON UPDATE CASCADE; ALTER TABLE `import_products_web2` ADD FOREIGN KEY (`margin_web3`) REFERENCES `margin`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE `import_products_web2` ADD `need_recalculate_price` BOOLEAN NOT NULL DEFAULT FALSE , ADD INDEX (`need_recalculate_price`);


ALTER TABLE `products_web2` ADD `propricea_web2_bck` DOUBLE(11,2) NULL DEFAULT NULL , ADD `propricea_web3_bck` DOUBLE(11,2) NULL DEFAULT NULL;
UPDATE `products_web2` SET propricea_web2_bck=propricea_web2, propricea_web3_bck=propricea_web3  WHERE 1 ;

UPDATE `products_web2` SET propricec_web2=null, propricec_web3=null WHERE  `provenid` != 'novaservis' ;

UPDATE `margin` SET  `type` = 0  WHERE `type` = 2;
UPDATE `margin` SET `type`=1 WHERE vendor_id is null AND `type`=0;

UPDATE products_web2
    left JOIN vendors ON (products_web2.provenid  = vendors.coll_on_products)
    SET products_web2.vendor_id = vendors.venid;