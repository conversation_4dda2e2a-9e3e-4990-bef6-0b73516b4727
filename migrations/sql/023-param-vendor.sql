CREATE TABLE `param_vendor`(
                                      `id` INT NOT NULL AUTO_INCREMENT,
                                      `param_id` INT NOT NULL,
                                      `vendor_id` INT NOT NULL,
                                      `param_name_on_feed` VARCHAR(50) NOT NULL,
                                      PRIMARY KEY(`id`),
                                      INDEX(`param_id`),
                                      INDEX(`vendor_id`)
) ENGINE = InnoDB;

ALTER TABLE `param_vendor` ADD FOREIGN KEY (`param_id`) REFERENCES `param`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `param_vendor` ADD FOREIGN KEY (`vendor_id`) REFERENCES `vendors`(`venid`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `param_vendor` ADD INDEX(`param_id`, `vendor_id`);