create table kaufland_catalog
(
    id                 int auto_increment
        primary key,
    kaufland_id        int              null,
    kaufland_parent_id int              null,
    catalog            int(11) unsigned null,
    name               varchar(255)     null,
    constraint kaufland_catalogs_catalogs_web2_catid_fk
        foreign key (catalog) references catalogs_web2 (catid)
            on update cascade on delete cascade
);

create table kaufland_catalog_param
(
    id               int auto_increment
        primary key,
    kaufland_par_id  int          not null,
    kaufland_catalog int          not null,
    name             varchar(255) not null,
    title            varchar(255) not null,
    explanation      varchar(255) null,
    constraint kaufland_catalog_param_kaufland_catalog_id_fk
        foreign key (kaufland_catalog) references kaufland_catalog (id)
            on update cascade on delete cascade
);



