CREATE TABLE `param`(
                                `id` INT NOT NULL AUTO_INCREMENT,
                                `name` VARCHAR(255) NOT NULL,
                                `ord` INT NOT NULL,
                                `value_type` INT(1) NOT NULL,
                                PRIMARY KEY(`id`)
) ENGINE = InnoDB;


CREATE TABLE `catalogs_web2_x_param` (`catalog_id` int(8) UNSIGNED NOT NULL , `param_id` INT NOT NULL ) ENGINE = InnoDB;
ALTER TABLE `catalogs_web2_x_param` ADD PRIMARY KEY(`catalog_id`, `param_id`);
ALTER TABLE `catalogs_web2_x_param` ADD FOREIGN KEY (`param_id`) REFERENCES `param`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `catalogs_web2_x_param` ADD FOREIGN KEY (`catalog_id`) REFERENCES `catalogs_web2`(`catid`) ON DELETE CASCADE ON UPDATE CASCADE;



CREATE TABLE `param_value`(
                                      `id` INT NOT NULL AUTO_INCREMENT,
                                      `val` VARCHAR(100) NOT NULL,
                                      `param_id` INT NOT NULL,
                                      PRIMARY KEY(`id`),
                                      INDEX(`param_id`)
) ENGINE = InnoDB;
ALTER TABLE `param_value` ADD FOREIGN KEY (`param_id`) REFERENCES `param`(`id`) ON DELETE CASCADE ON UPDATE RESTRICT;


ALTER TABLE `proparams_web2` ADD `param_id` INT NULL AFTER `prpdateu`, ADD INDEX (`param_id`);
ALTER TABLE `proparams_web2` ADD FOREIGN KEY (`param_id`) REFERENCES `param`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `proparams_web2` ADD `param_value_id` INT NULL AFTER `param_id`, ADD INDEX (`param_value_id`);
ALTER TABLE `proparams_web2` ADD FOREIGN KEY (`param_value_id`) REFERENCES `param_value`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `proparams_web2` ADD `rawvalue` FLOAT NULL AFTER `param_value_id`;