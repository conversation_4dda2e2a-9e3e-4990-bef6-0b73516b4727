# deploy master větve pomocí git-ftp https://github.com/git-ftp/git-ftp/blob/master/man/git-ftp.1.md
# vyžaduje nastavit v Settings -> Pipelines -> Environment variables pro FTP
# pokud ftp server podporuje používat pro FTP_..._URL ftpes:// protokol, tedy ftpes://ftp.server.name
# pro první spuštění a inicializaci na ftp serveru použít místo git ftp init ... nebo na server nahrát soubor .git-ftp.log ve kterém je ID aktuálního commitu, který je na FTP

image: bitnami/git

pipelines:
  branches:
    master:
      - step:
          script:
            - apt-get update
            - apt-get -qq install git-ftp
            #- git config git-ftp.key ~/.ssh/root2.pem
            - git ftp push --insecure --user $FTP_MASTER_USERNAME --passwd $FTP_MASTER_PASSWORD $FTP_MASTER_URL
            - curl -O https://www.sccom.cz/cc.php?k=lZwJIL
            - curl -O https://test:<EMAIL>/runmig.php?action=run&groups%5B0%5D=sql&mode=0&pass=tohleSeNesmiNikomuRikat!
    dev:
      - step:
          script:
            - apt-get update
            - apt-get -qq install git-ftp
            - git ftp push --user $FTP_DEV_USERNAME --passwd $FTP_DEV_PASSWORD $FTP_DEV_URL
            - curl -O https://test:<EMAIL>/cc.php?k=lZwJIL
            - curl -O https://test:<EMAIL>/runmig.php?action=run&groups%5B0%5D=sql&mode=0&pass=tohleSeNesmiNikomuRikat!
    nette3.1:
      - step:
          script:
            - ssh -t <EMAIL> "cd /var/www/html/www && git pull"
            - ssh -t <EMAIL> "cd /var/www/html && composer update"
            - ssh -t <EMAIL> "rm -rf  /var/www/html/temp*/cache"
            - ssh -t <EMAIL> "php /var/www/html/migrations/run.php sql"
            - ssh -t <EMAIL> "rm -rf  /var/www/html/temp*/cache"