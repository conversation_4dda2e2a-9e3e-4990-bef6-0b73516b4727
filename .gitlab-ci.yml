stages:
  - deploy
  - test
  - build

image: php:8.0







deploy to production:
  stage: deploy
  environment:
    name: production
    url: $URL_PRODUCTION
  #    only:
  #        - master
  before_script:
    - apt-get update
    - apt-get install -y ssh rsync
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - eval $(ssh-agent -s)
    - (echo "$DEPLOYER_SSH_KEY" | base64 --decode) | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - curl -LO https://github.com/deployphp/deployer/releases/download/v7.0.0/deployer.phar
    - mv deployer.phar /usr/local/bin/dep
    - chmod +x /usr/local/bin/dep
  script:
    - dep deploy -vvv --revision="$CI_COMMIT_SHA"  #$CI_ENVIRONMENT_SLUG
  only:
    - master
